**Twilight** comes with a ready-designed and styled set of web components for Salla stores. For example, ready components to display the login form, product availability section, search bar, localization menu, and many more.

**Twilight JS Web Components** are a collection of high-level building blocks and reusable web components that can be built together to swiftly develop the UI for custom Salla Themes, governed by clear guidelines.

:::info[Information]
**JS Web Components** are built from the ground up to be simple to learn and use, with various thoughtfully constructed user interface components. Its complete compatibility with the themes' structure and architecture makes it easy to customize, as the documentation explains.

:::

In this article, we'll go through the list of the various web components along with their benefits.

## 📙 What you'll learn

- [JS Web components](#js-web-components).
- [Benefits](#benefits).

## JS Web components

Below is a list of the ready-made Twilight JS Web Components which can be used easily. Following, in this part of the document, each component is explained in detail.

Every web component comes with a list of **properties and events** that make that component customizable. Besides, each web component uses methods from the [**Twilight JS SDK**](https://docs.salla.dev/doc-422610?nav=01HNFTDZPB31Y2E120R84YXKCX) to fetch any needed data from the backend.

<CardGroup cols="3">
  <Card title="salla-advertisement" href="https://docs.salla.dev/doc-478502/?nav=01HNFTE06J4QC24T0D5BPRYKMD"   icon="material-outline-ads_click">
    Shows all the details about product advertisements.
  </Card>
  <Card title="salla-apps-icons" href="https://docs.salla.dev/doc-478518/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-app_shortcut" >
    Shows clickable labels of Google Play Store and Apple Store for the Store's application.
  </Card>
  <Card title="salla-add-product-button" href="https://docs.salla.dev/doc-422692?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-production_quantity_limits">
    Allows controllability over button text labels and behaviors based on the product-status and product-type
    properties.
  </Card>
  <Card title="salla-breadcrumb" href="https://docs.salla.dev/doc-482370/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-double_arrow">
    Helps users navigate by showing their path through pages, allowing them to easily go back by clicking on links.
  </Card>
  <Card title="salla-button" href="https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD" icon="material-outline-smart_button" >

    Shows a customizable button, in terms of size, color, style, status, position etc..
  </Card>
  <Card title="salla-cart-summary" href="https://docs.salla.dev/doc-422695?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-shopping_cart">
    Show the icon of the shopping cart with a small circle badge indicating the number of items in the cart.
  </Card>
  <Card title="salla-color-picker" href="https://docs.salla.dev/doc-422696?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-colorize">
    Allows selection of a color using a variety of input methods.
  </Card>
  <Card title="salla-comments" href="https://docs.salla.dev/doc-422697?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-insert_comment">
    Displays a comment form for specific products or pages.
  </Card>
  <Card title="salla-contacts" href="https://docs.salla.dev/doc-478494/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-contact_page">
    Shows the store's contact information details.
  </Card>
  <Card title="salla-conditional-fields" href="https://docs.salla.dev/doc-422699?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-rule">
    Allows for hiding / displaying certain features in a product, such as size.
  </Card>
  <Card title="salla-conditional-offer" href="https://docs.salla.dev/doc-537931/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-rule_folder">
    Enables dynamic presentation of offers and discounts based on the customer's cart status.
  </Card>
  <Card title="salla-count-down" href="https://docs.salla.dev/doc-422701?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-watch_later">
    Shows the amount of time left until a given date.
  </Card>
  <Card title="salla-datetime-picker" href="https://docs.salla.dev/doc-422702?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-date_range">
    Allows users to select both date and time with the same control.
  </Card>
  <Card title="salla-file-upload" href="https://docs.salla.dev/doc-422703?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-upload_file">
    Allows the user to allow uploading a file or a number of files.
  </Card>
  <Card title="salla-filters" href="https://docs.salla.dev/doc-422704?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-filter_alt">
    Allows the user to filter the data in a variety of ways.
  </Card>
  <Card title="salla-gifting" href="https://docs.salla.dev/doc-422705?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-card_giftcard">
    Display items as gifts, which can be used after the customer has completed a purchase.
  </Card>
  <Card title="salla-infinite-scroll" href="https://docs.salla.dev/doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-keyboard_double_arrow_down">
    Allows for infinite scrolling to load content continuously as the user scrolls down a page.
  </Card>
  <Card title="salla-installment" href="https://docs.salla.dev/doc-422707?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-money">
    Shows a block area for the available installment payment options provided for a specific product.
  </Card>
  <Card title="salla-list-tile" href="https://docs.salla.dev/doc-422708?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-fact_check">
    Used to display listing items in a tile form.
  </Card>
  <Card title="salla-loading" href="https://docs.salla.dev/doc-422709?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-downloading">
    Used to convey that some data is currently loading to the user.
  </Card>
  <Card title="salla-localization-modal" href="https://docs.salla.dev/doc-422710?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-share_location">
    Shows the menu for the store's available languages and currencies.
  </Card>
  <Card title="salla-login-modal" href="https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-login">
    Displays the login form, which prompts a user for their credentials in order to authenticate their access.
  </Card>
  <Card title="salla-loyalty" href="https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-loyalty">
    Display a popup that represents the Loyalty program.
  </Card>
  <Card title="salla-map" href="https://docs.salla.dev/doc-422713?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-map">
    Displays geographical maps from various sources with multiple layers, and interaction through events.
  </Card>
  <Card title="salla-menu" href="https://docs.salla.dev/doc-478492/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-menu_open">
    Shows nested list items that either appear on the header section or footer section.
  </Card>
  <Card title="salla-metadata" href="https://docs.salla.dev/doc-464599/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-data_thresholding">
    Shows detailed specifications for a product. It can display one or multiple sections of information, like links, and
    text etc.
  </Card>
  <Card title="salla-modal" href="https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-format_list_bulleted">
    Displays a dialog box or pop-up window on top of the current page.
  </Card>
  <Card title="salla-offer" href="https://docs.salla.dev/doc-440408?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-local_offer">
    Displays offers, categories, products, banks, and discount information.
  </Card>
  <Card title="salla-offer-modal" href="https://docs.salla.dev/doc-422715?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-format_list_bulleted">
    Shows a list of products with an offer given by the store admin.
  </Card>
  <Card title="salla-orders" href="https://docs.salla.dev/doc-508225/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-shopping_bag">
    Shows a table with order details, such as order ID, product total, order status, and more.
  </Card>
  <Card title="salla-payments" href="https://docs.salla.dev/doc-478374/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-payment">
    Shows the available payment options as labeled footer items.
  </Card>
  <Card title="salla-placeholder" href="https://docs.salla.dev/doc-422716?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-place">
    Reserves space for content that soon will appear in a layout.
  </Card>

  <Card title="salla-product-availability" href="https://docs.salla.dev/doc-422717?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-fact_check">
    Show the "Notify availability" option as a button for the registered customer.
  </Card>
  <Card title="salla-product-card" href="https://docs.salla.dev/doc-422718?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-discount">
    Contains content and actions about a single subject in a card display mode.
  </Card>
  <Card title="salla-products-list" href="https://docs.salla.dev/doc-422719?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-article">
    Displays a group of related products with some of information, such as products' names, prices, and other relevant
    information in an organized way.
  </Card>
  <Card title="salla-product-options" href="https://docs.salla.dev/doc-422720?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-fact_check">
    Shows customizable product fields before proceeding to ordering.
  </Card>
  <Card title="salla-product-size-guide" href="https://docs.salla.dev/doc-422721?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-zoom_out_map">
    Enables the merchant to add product measurements of height, weight, depth and other metrics.
  </Card>
  <Card title="salla-products-slider" href="https://docs.salla.dev/doc-422722?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-height">
    Navigates horizontally through a group of related products.
  </Card>
  <Card title="salla-progress-bar" href="https://docs.salla.dev/doc-422723?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-moving">
    Displays a progress bar indicating that data processing is underway.
  </Card>
  <Card title="salla-quantity-input" href="https://docs.salla.dev/doc-422724?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-input">
    Allows the customer to use a counter to specify the needed quantity of a specific product.
  </Card>
  <Card title="salla-quick-buy" href="https://docs.salla.dev/doc-422725?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-shopping_basket">
    Allows for placing the Quick Buy button for a quickly checkout and pay for products.
  </Card>
  <Card title="salla-quick-order" href="https://docs.salla.dev/doc-422726?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-check_circle">
    Allows the customer to quickly checkout and pay for products.
  </Card>
  <Card title="salla-rating-stars" href="https://docs.salla.dev/doc-422727?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-star_half">
    Displays a form of rating scale using a star glyph.
  </Card>
  <Card title="salla-rating-modal" href="https://docs.salla.dev/doc-422728?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-stars">
    Displays the rating scale for a store, product, or shipping company.
  </Card>
  <Card title="salla-reviews" href="https://docs.salla.dev/doc-508226/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-star_border">
    Displays a vertically scrollable reviews, which can its data source can be customized.
  </Card>
  <Card title="salla-reviews-summary" href="https://docs.salla.dev/doc-602149/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-rate_review">
    Allows users to display the general rating out of 5 stars on the product details page.
  </Card>
  <Card title="salla-scopes" href="https://docs.salla.dev/doc-422729?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-assignment_turned_in">
    Shows a list of scopes (branches) owned by the store.
  </Card>
  <Card title="salla-search" href="https://docs.salla.dev/doc-422730?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-search">
    Shows a search box, field, or bar.
  </Card>
  <Card title="salla-sheet" href="https://docs.salla.dev/doc-422733?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-assignment">
    Baseline layout foundation for other components to be set on.
  </Card>
  <Card title="salla-skeleton" href="https://docs.salla.dev/doc-422731?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-reorder">
    Displays an indication to the user that something is coming but not yet available.
  </Card>
  <Card title="salla-slider" href="https://docs.salla.dev/doc-422735?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-swap_horiz">
    Gathers numerical user data by reflecting a range of values along a bar.
  </Card>
  <Card title="salla-social" href="https://docs.salla.dev/doc-499802?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-thumb_up">
    Displays a list of the store's social media account.
  </Card>
  <Card title="salla-social-share" href="https://docs.salla.dev/doc-422736?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-share">
    Displays a menu with social media platforms.
  </Card>
  <Card title="salla-swiper" href="https://docs.salla.dev/doc-422737?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-navigate_next">
    Modern touch slider to display a list of items.
  </Card>
  <Card title="salla-tabs" href="https://docs.salla.dev/doc-422738?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-table_chart">
    Makes it possible to have several panes inside a single view.
  </Card>
  <Card title="salla-tel-input" href="https://docs.salla.dev/doc-422739?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-perm_phone_msg">
    Shows a field for entering a telephone number, with country key/code prefix.
  </Card>
  <Card title="salla-user-menu" href="https://docs.salla.dev/doc-422740?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-menu_open">
    Shows a navigation menu list with links.
  </Card>
  <Card title="salla-userprofile" href="https://docs.salla.dev/doc-482367/?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-person">
    Displays user profile detailed information.
  </Card>
  <Card title="salla-user-settings" href="https://docs.salla.dev/doc-422741?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-settings">
    Allows the user to manage their account settings.
  </Card>
  <Card title="salla-verify" href="https://docs.salla.dev/doc-422742?nav=01HNFTE06J4QC24T0D5BPRYKMD"  icon="material-outline-verified_user">
    Shows fields for verifying email/mobile of users by sending OTP verification code.
  </Card>
</CardGroup>
<!--


| Component                                                         | Code Tag                     | Description                                                                                                                                         |
| ----------------------------------------------------------------- | ---------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Advertisement](https://docs.salla.dev/doc-478502/?nav=01HNFTE06J4QC24T0D5BPRYKMD)       | `salla-advertisement`        | Shows all the details about product advertisements.                                                                                                 |
| [Apps Icons](https://docs.salla.dev/doc-478518/?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-apps-icons`           | Shows clickable labels of Google Play Store and Apple Store for the Store's application                                                             |
| [Add Product](https://docs.salla.dev/doc-422692?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-add-product-button`   | Allows controllability over button text labels and behaviors based on the product-status and product-type properties.                               |
| [Breadcrumb](https://docs.salla.dev/doc-482370/?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-breadcrumb`           | Helps users navigate by showing their path through pages, allowing them to easily go back by clicking on links.                                     |
| [Button](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-button`               | Shows a customizable button, in terms of size, color, style, status, position etc..                                                                 |
| [Cart Summary](https://docs.salla.dev/doc-422695?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-cart-summary`         | Show the icon of the shopping cart with a small circle badge indicating the number of items in the cart.                                            |
| [Color Picker](https://docs.salla.dev/doc-422696?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-color-picker`         | Allows selection of a color using a variety of input methods.                                                                                       |
| [Comment Form](https://docs.salla.dev/doc-422697?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-comment-form`         | Displays a comment form for specific products or pages                                                                                              |
| [Contacts](https://docs.salla.dev/doc-478494/?nav=01HNFTE06J4QC24T0D5BPRYKMD)            | `salla-contacts`             | Shows the store's contact information details                                                                                                       |
| [Conditional Fields](https://docs.salla.dev/doc-422699?nav=01HNFTE06J4QC24T0D5BPRYKMD)   | `salla-conditional-fields`   | Allows for hiding / displaying certain features in a product, such as size.                                                                         |
| [Count Down](https://docs.salla.dev/doc-422701?nav=01HNFTE06J4QC24T0D5BPRYKMD)           | `salla-count-down`           | Shows the amount of time left until a given date.                                                                                                   |
| [Date Time Picker](https://docs.salla.dev/doc-422702?nav=01HNFTE06J4QC24T0D5BPRYKMD)     | `salla-datetime-picker`      | Allows users to select both date and time with the same control.                                                                                    |
| [File Upload](https://docs.salla.dev/doc-422703?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-file-upload`          | Allows the user to allow uploading a file or a number of files.                                                                                     |
| [Filters](https://docs.salla.dev/doc-422704?nav=01HNFTE06J4QC24T0D5BPRYKMD)              | `salla-filters`              | Allows the user to filter the data in a variety of ways.                                                                                            |
| [Gifting](https://docs.salla.dev/doc-422705?nav=01HNFTE06J4QC24T0D5BPRYKMD)              | `salla-gifting`              | Display items as gifts, which can be used after the customer has completed a purchase.                                                              |
| [Infinite Scroll](https://docs.salla.dev/doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD)      | `salla-infinite-scroll`      | Allows for infinite scrolling to load content continuously as the user scrolls down a page.                                                         |
| [Installment](https://docs.salla.dev/doc-422707?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-installment`          | Shows a block area for the available installment payment options provided for a specific product.                                                   |
| [List Tile](https://docs.salla.dev/doc-422708?nav=01HNFTE06J4QC24T0D5BPRYKMD)            | `salla-list-tile`            | Used to display listing items in a tile form.                                                                                                       |
| [Loading](https://docs.salla.dev/doc-422709?nav=01HNFTE06J4QC24T0D5BPRYKMD)              | `salla-loading`              | Used to convey that some data is currently loading to the user.                                                                                     |
| [Localization](https://docs.salla.dev/doc-422710?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-localization-modal`   | Shows the menu for the store's available languages and currencies.                                                                                  |
| [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD)                | `salla-login-modal`          | Displays the login form, which prompts a user for their credentials in order to authenticate their access.                                          |
| [Loyalty](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD)              | `salla-loyalty`              | Display a popup that represents the Loyalty program.                                                                                                |
| [Map](https://docs.salla.dev/doc-422713?nav=01HNFTE06J4QC24T0D5BPRYKMD)                  | `salla-map`                  | Displays geographical maps from various sources with multiple layers, and interaction through events.                                               |
| [Menu](https://docs.salla.dev/doc-478492/?nav=01HNFTE06J4QC24T0D5BPRYKMD)                | `salla-menu`                 | Shows nested list items that either appear on the header section or footer section.                                                                 |
| [Meta Data](https://docs.salla.dev/doc-464599/?nav=01HNFTE06J4QC24T0D5BPRYKMD)           | `salla-metadata`             | Shows detailed specifications for a product. It can display one or multiple sections of information, like links, and text etc                       |
| [Modal](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD)                | `salla-modal`                | Displays a dialog box or pop-up window on top of the current page.                                                                                  |
| [Offer](https://docs.salla.dev/doc-440408?nav=01HNFTE06J4QC24T0D5BPRYKMD)                | `salla-offer`                | Displays offers, categories, products, banks, and discount information.                                                                             |
| [Offer Modal](https://docs.salla.dev/doc-422715?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-offer-modal`          | Shows a list of products with an offer given by the store admin.                                                                                    |
| [Payments](https://docs.salla.dev/doc-478374/?nav=01HNFTE06J4QC24T0D5BPRYKMD)            | `salla-payments`             | Shows the available payment options as labaled footer items                                                                                         |
| [Placeholder](https://docs.salla.dev/doc-422716?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-placeholder`          | Reserves space for content that soon will appear in a layout.                                                                                       |
| [Product Availability](https://docs.salla.dev/doc-422717?nav=01HNFTE06J4QC24T0D5BPRYKMD) | `salla-product-availability` | Show the "Notify availability" option as a button for the registered customer                                                                       |
| [Product Card](https://docs.salla.dev/doc-422718?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-product-card`         | Contains content and actions about a single subject in a card display mode.                                                                         |
| [Product List](https://docs.salla.dev/doc-422719?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-product-list`         | Displays a group of related products with some of information, such as products' names, prices, and other relevant information in an organized way. |
| [Product Options](https://docs.salla.dev/doc-422720?nav=01HNFTE06J4QC24T0D5BPRYKMD)      | `salla-product-options`      | Shows customizable product fields before proceeding to ordering                                                                                     |
| [Product Size Guide](https://docs.salla.dev/doc-422721?nav=01HNFTE06J4QC24T0D5BPRYKMD)   | `salla-product-size-guide`   | Enables the merchant to add product measurements of height, weight, depth and other metrics.                                                        |
| [Products Slider](https://docs.salla.dev/doc-422722?nav=01HNFTE06J4QC24T0D5BPRYKMD)      | `salla-products-slider`      | Navigates horizontally through a group of related products.                                                                                         |
| [Progress bar](https://docs.salla.dev/doc-422723?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-progress-bar`         | Displays a progress bar indicating that data processing is underway.                                                                                |
| [Quantity Input](https://docs.salla.dev/doc-422724?nav=01HNFTE06J4QC24T0D5BPRYKMD)       | `salla-quantity-input`       | Allows the customer to use a counter to specify the needed quantity of a specific product.                                                          |
| [Quick Buy](https://docs.salla.dev/doc-422725?nav=01HNFTE06J4QC24T0D5BPRYKMD)            | `salla-quick-buy`            | Allows for placing the Quick Buy button for a quickly checkout and pay for products.                                                                |
| [Quick Order](https://docs.salla.dev/doc-422726?nav=01HNFTE06J4QC24T0D5BPRYKMD)          | `salla-quick-order`          | Allows the customer to quickly checkout and pay for products.                                                                                       |
| [Rating Stars](https://docs.salla.dev/doc-422727?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-rating-stars`         | Displays a form of rating scale using a star glyph.                                                                                                 |
| [Rating](https://docs.salla.dev/doc-422728?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-rating-modal`         | Displays the rating scale for a store, product, or shipping company.                                                                                |
| [Scopes](https://docs.salla.dev/doc-422729?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-scopes`               | Shows a list of scopes (branches) owned by the store                                                                                                |
| [Search](https://docs.salla.dev/doc-422730?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-search`               | Shows a search box, field, or bar.                                                                                                                  |
| [Sheet](https://docs.salla.dev/doc-422733/?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-sheet`                | Baseline layout foudnation for other components to be set on.                                                                                       |
| [Skeleton](https://docs.salla.dev/doc-422731?nav=01HNFTE06J4QC24T0D5BPRYKMD)             | `salla-skeleton`             | Displays an indication to the user that something is coming but not yet available.                                                                  |
| [Slider](https://docs.salla.dev/doc-422735?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-slider`               | Gathers numerical user data by reflecting a range of values along a bar.                                                                            |
| [Social Share](https://docs.salla.dev/doc-422736?nav=01HNFTE06J4QC24T0D5BPRYKMD)         | `salla-social-share`         | Displays a menu with social media platforms.                                                                                                        |
| [Swiper](https://docs.salla.dev/doc-422737?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-swiper`               | Modern touch slider to display a list of items.                                                                                                     |
| [Tabs](https://docs.salla.dev/doc-422738?nav=01HNFTE06J4QC24T0D5BPRYKMD)                 | `salla-tabs`                 | Makes it possible to have several panes inside a single view.                                                                                       |
| [Tel Input](https://docs.salla.dev/doc-422739?nav=01HNFTE06J4QC24T0D5BPRYKMD)            | `salla-tel-input`            | Shows a field for entering a telephone number, with country key/code prefix.                                                                        |
| [User Menu](https://docs.salla.dev/doc-422740?nav=01HNFTE06J4QC24T0D5BPRYKMD)            | `salla-user-menu`            | Shows a navigation menu list with links.                                                                                                            |
| [User Profile](https://docs.salla.dev/doc-482367/?nav=01HNFTE06J4QC24T0D5BPRYKMD)        | `salla-userprofile`          | Displays user profile detailed information                                                                                                          |
| [User Settings](https://docs.salla.dev/doc-422741?nav=01HNFTE06J4QC24T0D5BPRYKMD)        | `salla-user-settings`        | Allows the user to manage their account settings.                                                                                                   |
| [Verify](https://docs.salla.dev/doc-422742?nav=01HNFTE06J4QC24T0D5BPRYKMD)               | `salla-verify`               | Shows fields for verifying email/mobile of users by sending OTP verification code.                                                                  |

-->

## Benefits


|  |  |
| --- | --- |
| **Clean, simple, and standardized** | Developers will follow standards and let users fall in love with the user experience with ready-made components and beautiful (yet changeable) themes. Developers can use a stylistic guideline and functional designs to create huge Themes with UI Component modules.
 |
| **Using Twilight JS SDK** | Twilight Web Components are using the [**Twilight JS SDK**](https://docs.salla.dev/doc-422610?nav=01HNFTDZPB31Y2E120R84YXKCX), which allows communication between the frontend and backend using specific REST API. 
 |
| **Ecosystem** | Twilight Web Components are maintained by a full-time core team and a large community of developers and contributors. This is because Twilight helps developers collaborate. 
 |