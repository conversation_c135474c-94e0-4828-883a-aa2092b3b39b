The `<salla-select>` web component is used to allow selection from a particular dropdown list, which can be an item's color, size, and so on. The component can be customized using the properties' parameters available.

## Example

<!--
focus: false
-->

![Select](https://cdn.salla.network/docs/twilight/6/js-web-select-01.png)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Basic Select component usage -->
<salla-select
  color="primary"
  autocomplete="true"
  size="large">
</salla-select>
```     
  </Tab>
<Tab title="SASS">
    
```css
.s-select-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
}

.s-select-underline {
  display: block;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  background-color: transparent;
  color: #6B7280;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: 100%;
  border-width: 0;
  border-bottom-width: 2px;
  border-color: #E5E7EB;
  appearance: none;
}

.s-select-outlined {
  display: block;
  padding: 0.625rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1rem;
  padding-right: 1rem;
  background-color: #F9FAFB;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.25rem;
  width: 100%;
  border-radius: 0.5rem;
  border-width: 1px;
  border-color: #D1D5DB;
}

.s-select-x-large {
  height: 5rem;
}

.s-select-large {
  height: 4rem;
}

.s-select-normal {
  height: 3rem;
}

.s-select-small {
  height: 3rem;
}

.s-select-x-small {
  height: 2.5rem;
}

.s-select-field {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  margin: 0;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 400;
  line-height: 1.75rem;
  width: 100%;
  border-radius: 0.375rem;
  border-color: #9CA3AF;
  box-shadow: none;
}


.s-select-hint {
  margin-top: 0.25rem;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 1.25rem;
}
```
      
  </Tab>    
</Tabs>

## Properties

| Property      | Attribute       | Description                                                                                                                 | Type                                                                             | Default          |
| ------------- | --------------- | --------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------- | ---------------- |
| Auto Complete | `autocomplete`  | Enables multiple item selection.                                                                                            | `boolean`                                                                        | `false`          |
| Auto Focus    | `autofocus`     | Enables `autofocus`.                                                                                                        | `boolean`                                                                        | `false`          |
| Chips         | `chips`         | Changes display of selections to `chips`.                                                                                   | `boolean`                                                                        | `false`          |
| Clear Icon    | `clear-icon`    | Applies when using `clearable` and the input is filled.                                                                     | `string`                                                                         | `'sicon-cancel'` |
| Clearable     | `clearable`     | Adds input clear functionality.                                                                                             | `boolean`                                                                        | `false`          |
| Color         | `color`         | Applies specified color to the control.                                                                                     | `"danger" \| "dark" \| "gray" \| "light" \| "primary" \| "success" \| "warning"` | `'primary'`      |
| Disabled      | `disabled`      | Disable the input.                                                                                                          | `boolean`                                                                        | `false`          |
| Flat          | `flat`          | Removes elevation or box shadow.                                                                                            | `boolean`                                                                        | `undefined`      |
| Hide Detail   | `hide-detail`   | Hides `hint` if any.                                                                                                        | `boolean`                                                                        | `false`          |
| Hint          | `hint`          | Adds hint text.                                                                                                             | `string`                                                                         | `undefined`      |
| Item Disabled | `item-disabled` | Sets property of items’s disabled value.                                                                                    | `string`                                                                         | `'disabled'`     |
| Item Text     | `item-text`     | Sets property of items’s text value.                                                                                        | `string`                                                                         | `"text"`         |
| Item Value    | `item-value`    | Sets property of items’s value - must be primitive. Dot notation is supported.                                              | `string`                                                                         | `"value"`        |
| Items         | --              | Can be an array of objects or array of strings. When using objects, it will look for a `text`, `value` and `disabled keys`. | `any[]`                                                                          | `[]`             |
| Label         | `label`         | Sets label value.                                                                                                           | `string`                                                                         | `undefined`      |
| Loading       | `loading`       | Displays linear progress bar.                                                                                               | `boolean`                                                                        | `false`          |
| Loading Color | `loading-color` | Specifies which color is applied to the progress bar.                                                                       | `string`                                                                         | `'primary'`      |
| Multiple      | `multiple`      | Enables multiple item selection.                                                                                            | `boolean`                                                                        | `false`          |
| Persist Hint  | `persist-hint`  | Forces hint to always be visible.                                                                                           | `boolean`                                                                        | `false`          |
| Placeholder   | `placeholder`   | Sets the input’s placeholder text.                                                                                          | `string`                                                                         | `undefined`      |
| Required      | `required`      | Enables multiple item selection.                                                                                            | `boolean`                                                                        | `false`          |
| Return Object | `return-object` | Changes the selection behavior to return the object directly, rather than the value specified with `item-value`.            | `boolean`                                                                        | `false`          |
| Shape         | `shape`         | Defines the appearance of the component.                                                                                    | `"outlined" \| "underline"`                                                      | `'outlined'`     |
| Size          | `size`          | Available sizing options.                                                                                                   | `"large" \| "normal" \| "small" \| "x-large" \| "x-small"`                       | `'normal'`       |
| Value         | `value`         | The selected value.                                                                                                         | `any`                                                                            | `undefined`      |
