This endpoint handles the customer's access code verification. It sends the entered access code to the backend and waits for the response. In the case of receiving a positive response, it helps by directing the customer to the store's home page. Otherwise, if the verification process fails, it informs the customer to re-send the correct access code.

:::tip
The *verify* endpoint has been implemented in the [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


```mermaid
sequenceDiagram
    autonumber
    Customer->>Login: Enter email or phone to login
    Login->>Backend: Sends payload with email/phone, and requests sending access code
    Backend-->>Customer: Sends access code to the customer
    loop Access code check
        Customer->>Login: Enters access code
    end
    Login->Backend: Sends the recived access code
    Backend->Backend: Verifies the recived access code
    Backend->>Login: Sends verification response
    alt wrong access code
        Login->>Customer: Requests new login
    else correct access code
        Login->>Customer: Redirects to Home Page
    end
```

## Payload


<DataSchema id="1387191" />



## Response

<Tabs>
 <Tab title="Success">
 
 
<DataSchema id="1427196" />
     
  </Tab>
  <Tab title="Error">
 
 

<DataSchema id="1427184" />
</Tab>
  
</Tabs>

## Usage
The `verify()` passes the customer access code to the backend in order to proceed with the verification process. In the case of using the phone number method to receive the access code, this method will pass the received access code along with the customer's phone number and the country code.

<Tabs>
  <Tab title="Mobile">
 
```js
salla.auth.verify({
  type: 'mobile', 
  phone: '5555555', 
  country_code: 'SA'
  code: '1111'
}).then((response) => {
  /* add your code here */
});
```     
  </Tab>
   <Tab title="Email">

```js
salla.auth.verify({
  type: 'email', 
  email: '<EMAIL>', 
  code: '1111'
}).then((response) => {
  /* add your code here */
});
```      
  </Tab>
  
</Tabs>



## Events
This endpoint may trigger two events, the onVerified and onVerificationFailed events.

### onVerified
This event is triggered when the verification process is done without having any errors back from the backend.
```js
salla.event.auth.onVerified((response) => {
  console.log(response)
});
```
### onVerificationFailed
This event may happen when there is an issue with setting the verification type, by `phone` or `email`. Additionally, this event will be triggered when the verification process fails and the backend sends error codes. In other words, the received response status is not 200.
```js
salla.event.auth.onVerificationFailed((errorMessage) => {
  console.log(errorMessage)
});
```