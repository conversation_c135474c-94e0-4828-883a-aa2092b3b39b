This endpoint is used for the purpose of rating a store. The customer will be able to send a review to a store after placing an order with that store.

:::tip
The *store rating* endpoint has been implemented in the [Rating](https://docs.salla.dev/doc-422728?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::
## Payload `authenticated`


<DataSchema id="1387273" />


## Response
<Tabs>
  <Tab title="Success">


<DataSchema id="1427922" />
```      
  </Tab>
   <Tab title="Error">
    
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of rating a store after placing an order, the developer may use the method `store()` as follows.


```js
salla.rating.store({
  comment: "Fast delivery",
  order_id: 587,
  rating: 5,
})
.then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onStoreRated and onStoreFailed events.

### onStoreRated
This event is triggered when rating a store by the customer is done without having any errors coming back from the backend.

```js
salla.event.rating.onStoreRated((response) => {
  console.log(response)
});
```
### onStoreFailed
This event is triggered when rating a store by the customer is not completed and an error has occurred.

```js
salla.event.rating.onStoreFailed((errorMessage) => {
  console.log(errorMessage)
});
