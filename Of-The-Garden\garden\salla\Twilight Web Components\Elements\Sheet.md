The `<salla-sheet>` component is the baseline for numerous components such as the [Button component](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD). It is a transformable layout, based on the `position` property, that provides a basic foundation for other components to be set on.


## Example

![Sheet](https://cdn.salla.network/docs/twilight/6/js-web-sheet-01.jpeg)

## Usage

<Tabs>
  <Tab title="HTML">
    
```html
<!-- Basic Salla Sheet usage-->
<salla-sheet
  position="right"
  persistent="true"
  width="450">
</salla-sheet>
```
      
  </Tab>
    <Tab title="SASS">
    
```css
.s-sheet-overlay {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: #00000080;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  display: none;
}

.s-sheet-overlay.active {
  display: flex;
}

.s-sheet-container {
  width: 100%;
  height: 40%;
  background-color: #ffffff;
  transform: translateY(100%);
  transition: transform 0.4s ease-in-out;
}
```
  
  </Tab>
</Tabs>



## Properties

| Property    | Attribute     | Description                                                                   | Type                                     | Default     |
| ----------- | ------------- | ----------------------------------------------------------------------------- | ---------------------------------------- | ----------- |
| Height      | `height`      | Sets the vertical height of the component                                     | `number`                                 | `300`       |
| Persistent  | `persistent`  | Whether or not the component is in a force view mode where it is not closable | `boolean`                                | `false`     |
| Position    | `position`    | Positions the component for different UI layout usecases, such as mobile view | `"bottom" \| "left" \| "right" \| "top"` | `'bottom'`  |
| Sheet Color | `sheet-color` | Customizable coloring of the component                                        | `string`                                 | `"#ffffff"` |
| Width       | `width`       | Sets the horizontal height of the component                                   | `number`                                 | `600`       |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `open` and `close` which opens and closes the sheet layout.


| Method  | Description             | Return Type     |
| ------- | ----------------------- | --------------- |
| `open`  | Opens the sheet layout  | `Promise<void>` |
| `close` | Closes the sheet layout | `Promise<void>` |
