This endpoint displays an <PERSON><PERSON>'s details for the customer, The cart's items will be listed here along with their details.

## Response
<Tabs>
  <Tab title="Success">
 
<DataSchema id="1427374" />
      
     
  </Tab>
   <Tab title="Error">

     
<DataSchema id="1427314" />
  </Tab>
  
</Tabs>


## Usage
The `details()` method does not receive any parameters. Simply, the developer may call this method and be able to get the customer's order details according to the cart's items.

```js
salla.cart.details().then((response) => {
  /* add your code here */
});
```

## Events
This endpoint may trigger two events, the onDetailsFetched and onDetailsNotFetched events.

### onDetailsFetched
This event is triggered when fetching the cart's items list is done without having any errors coming back from the backend.

```js
salla.cart.event.onDetailsFetched((response) => {
  console.log(response)
});
```
### onDetailsNotFetched
This event is triggered when fetching the cart's items list is not completed and an error has occurred.

```js
salla.cart.event.onDetailsNotFetched((errorMessage) => {
  console.log(errorMessage)
});
```