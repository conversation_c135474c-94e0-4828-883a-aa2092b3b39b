The `<salla-Scopes>` web component shows a list of scopes (Branches) owned by the store. It consists of [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component that enables the addition of a header component, and that can be customized using the properties' parameters.


## Example

<!--
focus: false
-->

![Branch Example](https://cdn.salla.network/docs/twilight/6/js-web-branches-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Button to activate Show Branch -->
<salla-button data-target="branches-header"
    onclick="salla.event.dispatch('scopes::open'{mode: 'availability', product_id: 1234})">
    Show Branch
</salla-button>

<!-- Scopes Component props -->
<salla-scopes search-display-limit="6" selection="optional"></salla-scopes>
```
      
  </Tab>
<Tab title="JS">
 
```js
// Save reference to the Button Component below
var branches = document.querySelector("salla-branches");
```
  </Tab>   
    
  <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-branches` class. Following is a complete source code for customizing this component:

```css

.s-branches {
  &-title {

  }
  &-input-wrap {

  }
  &-input {

  }
  &-label {

  }
  &-select {

  }
  &-space-v {

  }
  &-sr-only {

  }
  &-is-closed {

  }
  &-closed-badge {

  }
  &-clickable {

  }
  &-submit {

  }
  &-color-red {

  }
  &-color-green {

  }
  &-color-gray {

  }
}
```
      
  </Tab>  
</Tabs>

## Properties

| Property             | Attribute              | Description                                                          | Type                          | Default    |
| -------------------- | ---------------------- | -------------------------------------------------------------------- | ----------------------------- | ---------- |
| Search Display Limit | `search-display-limit` | Determines when to show the search field                             | `number`                      | `6`        |
| Selection            | `selection`            | Either optionally open the modal or enforce the pop-up to the viewer | `"mandatory" \| "optional" \| | `optional`|

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `show` and/or `hide` the component.


| Method                                  | Description                                        | Return Type            |
| --------------------------------------- | -------------------------------------------------- | ---------------------- |
| `close()`                               | Closes the scope modal dialog.                     | `Promise<HTMLElement>` |
| `open(mode?: any, product_id?: number)` | Opens the scope modal dialog.                      | `Promise<any>`         |
| `handleSubmit()`                        | Submits a form which modifies the existing scopes. | `Promise<any>`         |

## Slots
The `slots` makes it customizable to modify certain labels, such as `footer`
| Slot     | Description                                                                                                                            |
| -------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| `footer` | This slot utilizes the "`handleSubmit`" method to submit the form. The bottom section of the component, which is used for form action. |

<!-- theme: success

> 💡 **Tip**
>
> To use a method, you can for instance `show` the component via the event:
>
> ```html
> onclick="salla.event.dispatch(`branches::show`)"
> ```
>
> and `hide` the component via the event:
>
> ```html
> onclick="salla.event.dispatch(`branches::hide`)"
> ``` -->
