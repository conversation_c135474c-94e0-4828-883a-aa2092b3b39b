The `<salla-progress-bar>` web component is used to convey data visually to users. It is also designed to help users quickly interpret numerical data at a glance, and can be customized according to the color and unit of the bar, as well as the unit, value, and textual representation. 

## Example

<!--
focus: false
-->

![Progress Bar](https://cdn.salla.network/docs/twilight/6/js-web-progress-bar-01.png?v=1-10-2022)

## Usage

<Tabs>
  <Tab title="HTML">
 
```html
<!-- Basic Salla Progress Bar component Usage -->
<salla-progress-bar
  color="#baf2e5"
  header="Points Left"
  value="100">
</salla-progress-bar>
```

  </Tab>
  <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-progress-bar` class. Following is a complete source code for customizing this component:

```css
.s-progress-bar{
  &-header{
    font-size: 0.875rem;
    line-height: 1.25rem;
    color:#6b7280;
    font-weight: bold;
  }
  &-target-section {
    display: flex;
    margin-bottom: 0.625rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    justify-content: space-between;
  }

  &-container {
    margin-bottom: 0.625rem;
  }

  &-wrapper {
    background-color: #E5E7EB;
    width: 100%;
    height: 0.625rem;
    border-radius: 9999px;
    margin-bottom: 0.375rem;
  }

  &-progress {
    // background-color: #5dd5c4;
    height: 0.625rem;
    border-radius: 9999px;
  }

  &-message {
    color: #a2a8b4;
    font-size: 0.75rem;
    line-height: 1rem;
    display: block;
    margin-bottom: 0.625rem;
  }
}
``` 
  </Tab>
</Tabs>


## Properties

| Property | Attribute  | Description                                                                                                                                                                                                         | Type                 | Default                                              |
| -------- | ---------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------- | ---------------------------------------------------- |
| Color    | `color`    | Progress bar color.                                                                                                                                                                                                 | `string`             | `salla.config.get('theme.color.primary', "#ffd5c4")` |
| Donation | `donation` | Pass the Donation object as a JSON string in the following format: `{"target_message":null,"target_date":"2023-04-18","target_end_date":"2023-04-18","target_amount":400,"collected_amount":380,"can_donate":true}` | `Donation \| string` | `undefined`                                          |
| Header   | `header`   | Header Title that appears before the progress bar.                                                                                                                                                                  | `string`             | `undefined`                                          |
| Height   | `height`   | Sets the height for the wrapper.                                                                                                                                                                        | `string`             | `"10px"`    |
| Message  | `message`  | Subtitle text that comes under the progress bar or instead of it if the `target` is not set.                                                                                                                        | `string`             | `undefined`                                          |
| Stripped | `stripped` | A stripped effect for the progress bar.                                                                                                                                                              | `boolean`            | `undefined` |
| Target   | `target`   | Progress bar's goal.                                                                                                                                                                                                | `number`             | `undefined`                                          |
| Unit     | `unit`     | The unite to be added after the numbers.                                                                                                                                                                            | `string`             | `salla.config.currency().symbol`                     |
| Value    | `value`    | The progress so far as of the goal.                                                                                                                                                                                 | `number`             | `undefined`                                          |