The `<salla-conditional-offer>` web component enables dynamic presentation of offers and discounts based on the customer's cart status.
:::info[Information]
Developers can use this component to enhance the user experience by automatically applying relevant offers, encouraging higher cart turnover and improved customer satisfaction. It can be enabled from the [Merchant Dashboard > Marketing Tools > Conditional Offers tab](https://s.salla.sa/marketing/marketing_tools).
:::

### Example
![image](https://cdn.salla.network/docs/twilight/6/js-web-conditional-offer.png)

## Usage

:::caution[Alert]
Conditional offers are activated when the total value of the cart meets or exceeds a defined limit set by the Merchant from the Dashboard. Potential promotions include fixed / percentage discounts on the cart total or the addition of a free product. This applies on:
- Number of products added in the cart.
- Cart total value.
:::

<Tabs>
  <Tab title="HTML">
 	 
```html
<!-- Conditional Offer component usage -->

<salla-conditional-offer> </salla-conditional-offer> 
``` 	 
  	</Tab>
    <Tab title="SASS">
     This JS web component can be targeted for styling by using the prefix `s-conditional-offer`, for example developers can customize the styling of the variable `checkpoint` in this way `s-conditional-offer-checkpoint: {background-color: #000000}` . Following is a complete list of variables for customizing this component:
        
| Variable Name |
| -- |
| `active-checkpoint` |
| `checkpoint` |
| `checkpoint-border` |
| `checkpoint-border-colored` |
| `checkpoint-container` |
| `checkpoint-image-content` |
| `checkpoint-label` |
| `checkpoint-label.active` |
| `checkpoint-label.first-checkpoint` |
| `container` |
| `item-avatar-content` |
| `item-avatar-content.active` |
| `product-link` |
| `progress-container` |
| `progress-line` |
| `progress-line-active` |
| `progress-line-container` |
| `progress-line-inactive` |
| `skeleton-checkpoint` |
| `skeleton-checkpoints-wrapper` |
| `skeleton-subtitle` |
| `skeleton-title` |
| `skeleton-wrapper` |
| `subtitle` |
| `subtitle-i` |
| `title` |
| `title-wrapper` |
    </Tab>   
</Tabs>

    


:::tip[Note]
No configurations are needed to use this component, as it is enabled/disabled from the [Merchant’s dashboard](https://s.salla.sa/marketing/marketing_tools) which can be queried using: `salla.config.get('store.features')`
:::