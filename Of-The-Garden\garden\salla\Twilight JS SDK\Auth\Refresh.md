To make the customer's login process easier, the store may use an access token to keep the client logged in for a period of time. Access tokens, on the other hand, may only be valid for a short amount of time for security reasons. A refresh token is used to "refresh" the access token once it has expired. This endpoint refresh token allows a client to receive new access tokens without requiring them to log in again.


:::tip
The *refresh* endpoint has been implemented in the [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::

## Response

<Tabs>
  <Tab title="Success">


<DataSchema id="1427208" />
      
  </Tab>
   <Tab title="Error">


       
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>

## Usage

The method `refresh()` can be called in order to generate a new access token for the customer. It does not require any input.

```js
salla.auth.refresh().then((response) => {
    /* add your code here */
});
```

## Events
This endpoint may trigger one event, which is the onRefreshFailed event.

### onRefreshFailed
This event will be triggered when the access token *refresh* process is not completed, and an error has occurred.
```js
salla.event.auth.onRefreshFailed((response) => {
  console.log(response)
});
```
