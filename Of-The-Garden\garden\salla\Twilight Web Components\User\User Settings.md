The `<salla-user-settings>` web component allows the user to manage their account settings such as enabling notifications and closing accounts, and that can be customized using the properties' parameters available.

:::tip[Note]
Available API Endpoints for the User Settings component is:

- [Update Settings](https://docs.salla.dev/doc-422685?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::

## Example

<!--focus: false -->
![User Settings](https://cdn.salla.network/docs/twilight/6/js-web-user-settings-01.png)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Basic User Settings component usage -->
<salla-user-settings
  is-notifiable="true">
</salla-user-settings>
```      
  </Tab>

<Tab title="SASS">

This JS web component can be targeted for styling by its `.s-user-settings` class. Following is a complete source code for customizing this component:

```css
.s-user-settings{
  &-wrapper{

  }
  &-section{

  }
  &-title{

  }
  &-subtitle{

  }
  &-action{
    
  }
}
```
      
  </Tab>  
    
</Tabs>



## Properties

| Property       | Attribute       | Description                                            | Type      | Default |
| -------------- | --------------- | ------------------------------------------------------ | --------- | ------- |
| Is Notifiable | `is-notifiable` | Value used for handling notification toggle check box. | `boolean` | `false` |