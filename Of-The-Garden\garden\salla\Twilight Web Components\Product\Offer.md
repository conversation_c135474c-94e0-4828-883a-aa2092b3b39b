
The `<salla-offer>` web component displays offers, categories, products, banks, and discount information. It uses the [`salla-slider`](https://docs.salla.dev/doc-422735?nav=01HNFTE06J4QC24T0D5BPRYKMD) component for carousel functionality.

## Example

<Frame caption="Product Offer">
  ![](https://cdn.salla.network/docs/twilight/6/js-web-offer-01.png)
</Frame>

<Frame caption="Discount Table Offer">
  ![](https://cdn.salla.network/docs/twilight/6/js-web-offer-02.png)
</Frame>

<Frame caption="Category Offer">
  ![](https://cdn.salla.network/docs/twilight/6/js-web-offer-03.png)
</Frame>

<Frame caption="Banks Offer">
  ![](https://cdn.salla.network/docs/twilight/6/js-web-offer-04.png)
</Frame>

## Usage


<Tabs>
  <Tab title="HTML">

```html
<salla-offer product-id="*********"></salla-offer>
```
  </Tab>
  <Tab title="SASS">
This JS web component can be targeted for styling by its `.s-offer-wrapper .s-slider-block__title` class. Following is a complete source code for customizing this component:

```js

.s-offer-wrapper .s-slider-block__title {
  h2 {
    font-size: 1.125rem;
    line-height: 1.75rem;
    color: #f87171;

    &::before {
      font-family: "sallaicons";
      content: "\ee30" !important;
      position: absolute;
      top: 1rem;
      font-size: 3rem;
      font-weight: 400;
      line-height: 1;
      color: #fef2f2;
    }
  }
}

.s-offer-bank-wrapper-sinlge-item{
    display: flex;
    align-items: center !important;
    gap: 14px;
}

.s-offer-bank-wrapper {
  display: flex !important;
  width: 100% !important;
}
```
      
  </Tab>
</Tabs>



## Properties

| Property    | Attribute    | Description                                     | Type     | Default     |
| ----------- | ------------ | ----------------------------------------------- | -------- | ----------- |
| Product Card Component` | `product-card-component` | [Custom Card Component](doc-422718) for the [`salla-products-list`](doc-422719).  This component allows you to customize the appearance of individual [product cards](doc-422718) within a [`salla-products-list`](doc-422719). | `string` | `'custom-salla-product-card'` |



## Slots
The`slots` makes it customizable to modify certain labels, such as `category`.

| Slot         | Description                                         |
| ------------ | --------------------------------------------------- |
| `category` | This slot is for customizing the category entry layout. |