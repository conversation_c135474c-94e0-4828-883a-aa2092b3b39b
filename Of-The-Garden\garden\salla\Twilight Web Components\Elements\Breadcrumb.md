The `<salla-breadcrumb>` web component is used as a navigational helper and hierarchy for pages. Breadcrumbs are used as a high-level representation of where users have navigated. Users can click the pages' texts to go back to previous pages.

## Example

<!--
focus: false
-->

![Alt text](https://cdn.salla.network/docs/twilight/6/js-web-breadcrumbs-01.png)


## Usage


<Tabs>
  <Tab title="HTML">
      
      
<Tabs>
  <Tab title="Simple Example">

```html
  <salla-breadcrumb></salla-breadcrumb>
```
      
  </Tab>
    
  <Tab title="Advanced Example | Using `slots`">

```html
<salla-breadcrumb>
   <div slot="item">
     <salla-button fill="outline" color="success" href={url}>{title}</salla-button>
   </div>
   <i slot="icon" class="sicon-cancel"></i>
 </salla-breadcrumb>
```
  </Tab>
</Tabs>


  </Tab>
  <Tab title="SASS">
 This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
:host {
  display: block;
}
```
  </Tab>

</Tabs>


<!--
type: tab
title: TWIG
-->




<!--
type: tab
title: SASS
-->




<!-- type: tab-end -->


<!-- ## Properties

| Property | Attribute | Description | Type    | Default |
| -------- | --------- | ----------- | ------- | ------- |
| Items     | `items`    | Breadcrumb items in an array of object where you specify the page title and URL        | `string` |`undefined`| -->


## Slots
The`slots` makes it customizable to modify certain labels, such as `icon`.

| Slot   | Description |
| ------ | ----------- |
| `icon` | Replaces the breadcrumb arrow icon with a customized icon. Find a list of Salla Icons [here](https://docs.salla.dev/docs/twilight-themes-documentation/5ace16b196fa5-salla-icons)        |
| `item` | Replaces breadcrumb item, with replaceable props which are `{url}`, `{title}`.|

:::info[Note]
Breadcrumbs in your theme configuration are managed with the following method: 
`salla.config.get('theme.settings.is_breadcrumbs_enabled')` to toggle their visibility.

Once enabled in your theme, breadcrumbs are automatically generated based on the `previous` page or by retrieving the parent page using: 
`salla.config.get('page.parent')`.

:::
