This endpoint is used when the customer removes an added reward from the live cart. In some cases, customers may need to remove a prize after adding it to the live cart.

:::tip
The *reset* endpoint has been implemented in the [Loyalty](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, , and It's all setup to save developer's time and effort.
:::

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427840" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427314" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of removing an added reward from the live cart, the method `reset()` may be called as below, with the id of the order to be rated.

```js
salla.loyalty.reset().then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onResetSucceeded and onResetFailed events.

### onResetSucceeded
This event is triggered when the action of removing an added reward from the live cart is done without having any errors coming back from the backend.

```js
salla.event.rating.onResetSucceeded((response) => {
  console.log(response)
});
```
### onResetFailed
This event is triggered when the action of removing an added reward from the live cart is not completed and an error has occurred.

```js
salla.event.rating.onResetFailed((errorMessage) => {
  console.log(errorMessage)
});