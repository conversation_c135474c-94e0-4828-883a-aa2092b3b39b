The `<salla-loyalty>` web component is used to display a popup that represents the Loyalty program. This program enables the store's customers to benefit from collecting the points for vouchers and offers provided by the store. Once they have collected enough points, the customers will be eligible to redeem them for exciting gifts or products.

It consists of a [Modal](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD) activated by the [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component, and that can be customized using the properties' parameters available.

:::tip[Note]
Available API Endpoints for the Loyalty component are:

- [Get Program](https://docs.salla.dev/doc-422667?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Reset](https://docs.salla.dev/doc-422669?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Exchange](https://docs.salla.dev/doc-422668?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::

## Example

<!--focus: false -->
![Loyalty Image](https://cdn.salla.network/docs/twilight/6/js-web-loyalty-01.png)

## Usage

<Tabs>
  <Tab title="HTML">

 ```html
<!-- Show button as widget to activate the modal -->
<salla-loyalty customer-points="1000">
  <salla-button slot="widget" onclick="salla.event.dispatch('loyalty::open')">
    Exchange
  </salla-button>
</salla-loyalty>

<!-- Show default widget to exchange the points -->
<salla-loyalty
 customer-points="1000"
 prize-points="10"
 prize-title="Discount Coupon (percentage amount): 10%">
</salla-loyalty>
```      
  </Tab>
  
</Tabs>

## Properties


| Property        | Attribute         | Description                                             | Type               | Default     |
| --------------- | ----------------- | ------------------------------------------------------- | ------------------ | ----------- |
| Customer Points | `customer-points` | Available customer points with which they can exchange. | `number`           | `undefined` |
| Guest Message   | `guest-message`   | Message to show for guest users.                        | `string`           | `undefined` |
| Prize Points    | `prize-points`    | The exchanged prize point.                              | `number \| string` | `undefined` |
| Prize Title     | `prize-title`     | The exchangable prize title.                            | `string`           | `undefined` |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `open` and `close` the Loyalty modal.


| Method                   | Description                                           | Return Type            |
| ------------------------ | ----------------------------------------------------- | ---------------------- |
| `open()`                 | Shows loyalty modal                                   | `Promise<any>`         |
| `close()`                | Hides loyalty modal                                   | `Promise<HTMLElement>` |
| `exchangeLoyaltyPoint()` | Exchanges loyalty points with the selected prize item | `Promise<any>`         |
| `resetExchange()`        | Cancels exchanged prizes                              | `Promise<any>`         |