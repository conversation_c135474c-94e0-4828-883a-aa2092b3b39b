The [`cart page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/cart.twig) renders the shopping cart page where customers can check what they have added to their shopping cart, and then simply proceed for checkout. The general view of this page displays the list of the items added to the cart, so then the customer can modify any quantity or remove any item.
In addtion,  can upload any files or attach any note along with the cart's items.

**Following is the page location and url:**

```shell title = "🌐 Page URL: http://www.store-domain.com/cart"
└── src
  ├── views
    ├── pages
    |   ...
    |   ├── cart.twig
    |   ...
    ...
```
<br/>

### Example
<!--
focus: false
-->
![Cart page](https://cdn.salla.network/docs/twilight/4/pages-cart-01.png)

### Variables


<DataSchema id="1383859" />


### Components
This page starts by displaying the `breadcrumbs` component. The `{% component breadcrumbs %}` line returns the current navigation  for the user.

```js
{% component 'header.breadcrumbs'%}
```
### JS Web Components
Cart page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores.

- Quantity Input [`<salla-quantity-input>`](https://docs.salla.dev/doc-422724?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Loyalty [`<salla-loyalty>`](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The [`cart page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/cart.twig) calls for the following [hooks](https://docs.salla.dev/doc-422552?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) in order to inject extra information.

```js
{% hook 'cart:items.start' %}
{% hook 'cart:items.end' %}
{% hook 'cart:summary.start' %}
{% hook 'cart:summary.end' %}
```


#### Usage

The [`cart page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/cart.twig) consists of several parts, which are a form to display all of the cart's items/product, free shipping option, and any added discount.

#### Cart items form
Using an HTML `<form>`, the developer can display all of the `items` added to the cart. All of the items can be displayed along with their options by using a *for-loop* statement as follows:

```php lineNumbers
{% for item in cart.items %}
    <form onchange="salla.cart.updateItem(new FormData(event.currentTarget))" id="item-{{ item.id }}">
        <input name="id" type="hidden" value="{{ item.id }}"/>

        <salla-conditional-fields>

            <a href="{{ item.url }}">
                <img src="{{ item.product_image }}" alt="{{ item.product_name }}"/>
            </a>

            <a href="{{ item.url }}">{{ item.product_name }} </a>
            <span class="{{ item.offer?'':'hidden' }}">{{ item.product_price|money }}</span>
            <span class="{{ item.offer?'case1-style':'case2-srtyle' }}">{{ item.price|money }}</span>
            <span class="{{ item.offer?'':'hidden' }}">{{ item.offer.names }} </span>

            {% if item.is_hidden_quantity %}
                <input name="quantity" type="hidden" value="{{ item.quantity }}"/>
                <span>{{ item.quantity }}</span>
            {% else %}
                <salla-quantity-input max="{{ product.max_quantity }}" value="{{ item.quantity }}" name="quantity">
                </salla-quantity-input>
            {% endif %}

            <span>{{ trans('pages.cart.total') }}:</span>
            <span>{{ item.is_available?item.total|money: trans('pages.cart.out_of_stock') }}</span>

            {% for option_index, option in item.options %}
                {# if there is no need for a label, or it needs special handling in the element file #}
                {% if option.type in ["splitter"] %}
                    {{ option.element|raw }}
                {% else %}
                    {% if option.type != "donation" %}
                        <label for="options[{{ option.id }}]">
                            <strong>
                                {{ option.name }} {% if option.required %}<span>*</span>{% endif %}
                            </strong>
                            <small>{{ option.placeholder }}</small>
                        </label>
                    {% endif %}
                    {{ option.element(product.id)|raw }}
                {% endif %}
            {% endfor %}
        </salla-conditional-fields>  
        .....
    </form>
{% else %}
    {{ trans('pages.cart.empty_cart') }}
{% endfor %}
```

#### Free shipping
Using the variable `cart.free_shipping_bar.has_free_shipping`, the developer may check if there is free shipping selected, and then build the logic to handle this part.

```php lineNumbers
<h4>{{ trans('pages.cart.free_shipping') }}</h4>
<p>
    <span>{{ (cart.free_shipping_bar.has_free_shipping ? trans('pages.cart.has_free_shipping') :trans('pages.cart.free_shipping_alert', {'amount': cart.free_shipping_bar.remaining|money})) | raw }}</span>
    <span>🎉</span>
</p>

<h4>{{ trans('pages.cart.summary') }}</h4>
<div>
    <span>{{ trans("pages.cart.items_total") }}</span>
    <strong>{{ cart.sub_total|money }}</strong>
</div>
<div>
    <span>{{ trans('pages.cart.shipping_cost') }}</span>
    <strong>{{ cart.real_shipping_cost|money }}</strong>
</div>
```

#### Apply Copoun and discount
Customer can also apply any given copoun in this page; however this is subject to the store settings which can be checked by the `store.settings.cart.apply_coupon_enabled` variable. Accordingly, any given discount can be shown using the `cart.real_discount` varible.

```php lineNumbers
{% if store.settings.cart.apply_coupon_enabled %}
    <form onsubmit="return salla.form.submit('coupon.add');" class="border-t border-border-color border-b py-5 mb-5">
        <input type="hidden" name="id" value="{{  cart.id }}">
        <label class="block text-sm">{{ trans('pages.cart.have_coupon') }}</label>
        <div class="mt-2.5 relative">
            <input placeholder="{{ trans('pages.cart.coupon_placeholder') }}"
                   class="pe-24 form-input" value="{{ cart.coupon }}"
                   name="coupon"
                   type="text">

            {% if cart.coupon %}
                <salla-button type="button" onclick="salla.coupon.remove({{ cart.id }})" class="btn--coupon has-coupon" oader-position="center">
                    <span class="coupon-text">{{ trans('pages.cart.remove_coupon') }}</span>
                    <i class="sicon-cancel icon text-xl w-8"></i>
                </salla-button>
            {% else %}
                <salla-button type="submit" class="btn--coupon" oader-position="center">
                    <span class="coupon-text">{{ trans('pages.cart.save_coupon') }}</span>
                    <i class="sicon-cancel icon text-xl w-8"></i>
                </salla-button>
            {% endif %}
        </div>
    </form>
{% endif %}
```


Finally, the complete order within the cart page can sent using the following Salla component button:

```php lineNumbers
<salla-button onclick="salla.cart.submit()" loader-position="end" width="wide">
  {{ trans('pages.cart.complete_order') }}
</salla-button>
```


:::tip[Educational Clip]
<Video src="https://youtu.be/B09v5wejnQM?si=rm4TzBSPmlGkiw8G"></Video>
:::
