A search query is group of keywords that a customer enters to satisfy their information needs. This endpoint enables the customer to enter a phrase or a keyword combination to find a product list of interest.

:::tip
The *search products* endpoint has been implemented in the [Search](https://docs.salla.dev/doc-422730?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


## Payload


<DataSchema id="1387275" />


## Response
<Tabs>
  <Tab title="Success">
  
<DataSchema id="1387277" />
  </Tab>
    
   <Tab title="Error">

<DataSchema id="1427314" />
      
  </Tab>
  
</Tabs>



## Usage
To perform the action of searching for products using a query string, the developer may call the method `products()` as follows;


```js
salla.product.search({ query: "green iphone" }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.product.search("green iphone").then((response) => {
  /* add your code here */
});
```

## Events
This endpoint may trigger two events, the onSearchResults and onSearchFailed events.
### onSearchResults
This event is triggered when searching for products using a query string is done without having any errors coming back from the backend.

```js
salla.product.event.onSearchResults((response) => {
  console.log(response)
});
```
### onSearchFailed
This event is triggered when searching for products using a query string is not completed and an error has occurred.

```js
salla.product.event.onSearchFailed((errorMessage) => {
  console.log(errorMessage)
});
