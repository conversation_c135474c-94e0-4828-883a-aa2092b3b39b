{#
| Variable          | Type    | Description                                                      |
|-------------------|---------|------------------------------------------------------------------|
| items             | array   | List of sliders                                                  |
| items[].url       | string  |                                                                  |
| items[].link_type | ?String | category,product,offers,page,external_link,brand                 |
| items[].image.url | string  |                                                                  |
| items[].image.alt | string  |                                                                  |
| position          | Int     | Sorting number start from zero                                   |
#}
<section class="s-block s-block--photos-slider {{ is_repeated ? 'repeated-block' : '' }}">
    <salla-slider 
      type="carousel" 
      class="home-slider photos-slider"
      centered 
      auto-play 
      pagination
      id="photos-{{ position }}-slider"> 
      <div slot="items">
        {% for index, item in items %}
          <div class="swiper-slide">
            <a href="{{item.url}}">
              <img loading="eager" src="{{ item.image.url }}" class="w-full object-contain rounded-md" height="300" width="1200" alt="{{store.name}} image-slider-{{index}}"></img>
            </a>
          </div>
        {% endfor %}
      </div>
    </salla-slider>
</section> 
