The `<salla-quick-buy>` web component is used for quick purchase. It redirects the customer immediately to the checkout page. By streamlining the shopping experience, customer can cut out extra steps and reduce abandonments, giving customers a secure and frictionless way to buy goods online. 

:::tip[Note]
This component redirects customers directly to the checkout page if *Apple Pay* is not active for their current store. However, if *Apple Pay* is active, it will start a new Apple Pay session to prompt the customer to pay.
:::

## Example

<!--focus: false -->
![Quick Buy](https://cdn.salla.network/docs/twilight/6/js-web-quick-buy-01.jpeg) 

## Usage

<Tabs>
  <Tab title="HTML">
      
   ```html
<!-- Basic Quick Buy component usage -->
<salla-quick-buy
  product-id="950497"
  type="donate">
</salla-quick-buy>
```   
  </Tab>
  <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-quick-buy` class. Following is a complete source code for customizing this component:

```css
.s-quick-buy {
  &-button {
    .s-button-text {
      display: flex;
    }
  }

  &-apple-pay {

  }
}

apple-pay-button {
  --apple-pay-button-width: 100%;
  --apple-pay-button-height: 40px;
  --apple-pay-button-border-radius: 7px;
  --apple-pay-button-padding: 0px 5px;
  --apple-pay-button-box-sizing: content-box;
}
```

      
  </Tab>  
  
</Tabs>


## Properties

| Property | Attribute  | Description                                                                                                | Type                             | Default     |
| -------- | ---------- | ---------------------------------------------------------------------------------------------------------- | -------------------------------- | ----------- |
| Amount            | `amount`              | Product's amount value.                                                                                                         | `number`                                                     | `0`         |
| Currency          | `currency`            | Product's amount currency.                                                                                                 | `string`                                                     | `undefined` |
| Is Require Shipping | `is-require-shipping` | To be passed to the `purchaseNow` request.                                                                                     | `boolean`                                                    | `undefined` |
| Options           | --                    | Product options, if is empty will get the data from the `document.querySelector('salla-product-options[product-id="X"]')` | `{}`                                                         | `{}`        |
| Product ID         | `product-id`          | Product's ID.                                                                                                             | `string`                                                     | `undefined` |
| Type              | `type`                | Button's type.                                                                                                            | `"book" \| "buy" \| "donate" \| "order" \| "pay" \| "plain"` | `'buy'`     |
