Featured products list is a collection, created in a specific design to draw customers' attention to see a certain collection of products primarily. This component is a **_pre-defined component_**
Twilight comes with three pre-styled featured products components, and this is the _style-3_ component.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── featured-products-style3.twig
          ...
```


### Example
<!--
focus: false
-->
![Featured Products Style 3](https://cdn.salla.network/docs/twilight/4/pages-components-home-featured-style3-01.png)

### Variables


<DataSchema id="1383694" />



### Usage
In general, this pre-styled featured products component with a _main product_ to display in bigger size, and then a column of products in smaller size:

```php lineNumbers
{% for section in items %}

    <h2>{{ section.title }}</h2>

    {% if section.featured_product %}
        <div id="featured-product-{{ section.featured_product.id }}">
            <a href="{{ section.featured_product.url }}">
                <img src="{{ 'images/s-empty.png' | asset }}"
                     data-src="{{ section.featured_product.image.url }}"
                     alt="{{ section.featured_product.image.alt }}"/>
            </a>
            {% if section.featured_product.promotion_title %}
                {{ section.featured_product.promotion_title }}
            {% endif %}
            <a href="{{ section.featured_product.url }}">{{ section.featured_product.name }}  </a>
            <h3>
                <a href="{{ section.featured_product.url }}">{{ section.featured_product.name }}</a>
            </h3>
            {% if section.featured_product.is_on_sale %}
                <div>
                    <h4>{{ section.featured_product.sale_price|money }}</h4>
                    <span>{{ section.featured_product.regular_price|money }}</span>
                </div>
            {% else %}
                <h4>{{ section.featured_product.price|money }}</h4>
            {% endif %}

            {% if section.featured_product.rating %}
                <div>{{ section.featured_product.rating|number }}</div>
            {% endif %}

            <salla-add-product-button product-id="{{ section.featured_product.id }}"
                    product-status="{{ section.featured_product.status }}"
                    product-type="{{ section.featured_product.type }}">
            </salla-add-product-button>
        </div>
    {% endif %}


    {% for product in section.products %}
        <div class="product-item">
            <a href="{{ product.url }}">
                <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
                {% if product.promotion_title %} {{ product.promotion_title }}
                {% endif %}
            </a>
            <h3>
                <a href="{{ product.url }}">{{ product.name }}</a>
            </h3>
            {% if product.on_sale %}
                <div>
                    <h4>{{ product.sale_price|money }}</h4>
                    {{ product.regular_price|money }}
                </div>
            {% else %}
                <h4>{{ product.price|money }}</h4>
            {% endif %}
        </div>
    {% endfor %}
{% endfor %}
```

