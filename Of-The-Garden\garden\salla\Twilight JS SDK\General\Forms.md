Theme's Forms are typically used to enclose one or more user inputs in order to enable an implicit submit function, in which users can submit an HTML `<form>` by clicking a "submit" button. This is a generally anticipated behavior that should be addressed as frequently as feasible.


:::info
✅ **Twilight JS SDK** makes it easier to interact with a Theme's Forms by using the included REST API endpoints. This is to ease tasks connected to API calls, such as Cart APIs, Product APIs, and many others.
:::
In this article, we'll look at how a developer may easily add forms to their Themes. Additionally, how the Form input will be collected upon any change or summit.

## 📙 What you'll learn
- [Twilight Forms](#twilight-forms)
- [Usage](#usage)
  - JS Web Components [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- [Response Schema](#response-schema)
  - Show Message

## Twilight Forms

In Twilight Themes, a form's fields are not automatically submitted via HTTP, unlike the HTML `form` element. Instead, the developer must include a JavaScript onSubmit callback that will send the required HTTP requests. The following is a complete example for adding a `form` to collect a comment on a product:

```php
<form class="form form--add-comment" onsubmit="return salla.form.onSubmit('comment.add', event);">
  <input name="type" type="hidden" value="product" />
  <input name="id" type="hidden" value="1234" />

  <h2 class="mb-5 text-lg font-bold text-dark">Comment</h2>
  <textarea class="form-input" name="comment" required maxlength="500" cols="30" rows="30"></textarea>

  <div class="text-end mb-4">
    <salla-button type="submit" className="w-36" loader-position="end">Submit</salla-button>
  </div>
</form>
```

## Usage 

In the previous example, developers may notice that the HTML `<form>` element contains the `onsubmit` attribute, which is an `event`. This event is triggered when the form is submitted. Sequentially, the method `salla.comment.add` will be implemented, which is one of the [Twilight API JS SDK](https://docs.salla.dev/doc-422610?nav=01HNFTDZPB31Y2E120R84YXKCX) methods. 

Under the hood, the event `onSubmit` calls the helper method `salla.comment.add`, which in its turn converts all of the form inputs to an object and passes it as arguments to that method. Below is that code that will be run as a result of submitting the previous form:

```js
salla.comment.add({
  type: "product",
  id: 1234,
  comment: "A nice product",
})
.then((response) => {
  // success submit
})
.catch((error) => {
  // we have an error
});
```

### JS Web Components `<salla-button>`
As what will see in the next section of this documentation, Twiligh is shipped with a ready-designed and styled set of [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD). These components are a collection of high-level building blocks and reusable web components that can be built together to swiftly develop the UI for custom Salla Themes. One of the main JS Web Components is the [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) which allows calling functions built by Salla to carry out certain activities. 

```js
<salla-button type="submit" className="w-36" loader-position="end">Submit</salla-button>
```
In the previous example, the [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) is used to submit the form, which causes the helper method `salla.comment.add` to be called and implemented. It will be loaded automatically and wait for any response from the server.

## Response Schema
The previous [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component will receive the server Response, which looks like the following schema:

```js
{
  "status": 200,
  "success": true,
  "data": {
    .......
  },
  "message": "a optional message if its exists"
}
```

### Show Message
The developer can show an alert message to the end user by adding a `message` to the response and a `success` flag indicating whether or not the message was successful.

<Tabs>
  <Tab title="Success">
      
```js
{
  "status": 200,
  "success": true,
  "message": "تمت إضافة المنتج بنجاح"
}
```
      
  </Tab>
  <Tab title="Error">
      
```js
{
  "status": 401,
  "success": false,
  "message": "يتطلب تسجيل دخول لاكمال هذا الاجراء"
}
```

  </Tab>
</Tabs>