The `<salla-datetime-picker>` web component is used to allow users to select both date and time with the same control. The date and time can be entered directly in the format of the current locale or through the Date Time Picker’s visible overlay.

## Example

<!--
focus: false
-->

![Date Time Picker](https://cdn.salla.network/docs/twilight/6/js-web-date-time-picker-01.png)

## Usage
<Tabs>
  <Tab title="HTML">

```html
<!-- Basic Ranged Date Time Picker -->
  <salla-datetime-picker mode="range"></salla-datetime-picker>

<!-- Enabled Date Time Picker with Seconds and Formatted Date -->
  <salla-datetime-picker enable-seconds enable-time date-format="Y-m-d H:i">
  </salla-datetime-picker>

<!-- Inline Enabled Date Time Picker with Seconds and Formatted Date -->
  <salla-datetime-picker inline enable-seconds date-format="Y-m-d H:i">
  </salla-datetime-picker>
```   
  </Tab>
  
</Tabs>

## Properties

| Property                 | Attribute                 | Description                                                                                                                                                                                                                                  | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | Default                                                          |
| ------------------------ | ------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------- |
| Allow Input              | `allow-input`             | Allows the user to enter a date directly into the input field.                                                                                                                                                                               | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Allow Invalid Preload    | `allow-invalid-preload`   | Allows the preloading of an invalid date. When disabled, the field will be cleared if the provided date is invalid                                                                                                                           | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Alternative Format               | `alt-format`              | Exactly the same as date format, but for the altInput field.                                                                                                                                                                                 | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `"F j, Y"`                                                       |
| Alternative Input                | `alt-input`               | Displays to the user a readable date (as per `altFormat`), but return something totally different to the server.                                                                                                                             | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Alternative Input Class          | `alt-input-class`         | Adds to the input element created by the `altInput` option. Note that `altInput` already inherits classes from the original input.                                                                                                           | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `undefined`                                                      |
| Append To                | --                        | Appends the calendar to the specified node Instead of body.                                                                                                                                                                                  | `HTMLElement`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | `undefined`                                                      |
| Aria Date Format         | `aria-date-format`        | Defines how the date will be formatted in the `aria-label` for calendar days, using the same tokens as `dateFormat`. If this is changed, choose a value that will make sense when a screen reader reads it out loud.                         | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `"F j, Y"`                                                       |
| Auto Fill Default Time   | `auto-fill-default-time`  | Whether or not the default time should be `auto-filled` when the input is empty and gains / loses focus when entered.                                                                                                                        | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `true`                                                           |
| Click Opens              | `click-opens`             | Whether or not clicking on the input should opens the datetime picker. Set it to false if only opening the calendar programmatically with the [open()] method is needed.                                                                     | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `true`                                                           |
| Close On Select          | `close-on-select`         | Whether or not the calendar should close after date selection.                                                                                                                                                                               | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `true`                                                           |
| Conjunction              | `conjunction`             | Separates dates in the entry field, using Conjunction, as it is used in "multiple" mode.                                                                                                                                                     | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `undefined`                                                      |
| Date Format              | `date-format`             | String of characters which are used to define how the date will be displayed in the input box.                                                                                                                                               | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `"Y-m-d"`                                                        |
| Date Parser              | --                        | Custom `datestring` parser                                                                                                                                                                                                                   | `(date: string, format: string) => Date`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `undefined`                                                      |
| Default Date             | `default-date`            | Sets the initial selected date(s). If using mode: "multiple" or a range calendar supply an Array of Date objects or an Array of date strings which follow the defualt `dateFormat`. Otherwise, supply a single Date object or a date string. | `Date \| DateOption[] \| number \| string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | `undefined`                                                      |
| Default Hour             | `default-hour`            | Initializes value of the `hour` element, when no date is selected.                                                                                                                                                                           | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `12`                                                             |
| Default Minute           | `default-minute`          | Initializes the value of the `minute` element, when no date is selected.                                                                                                                                                                     | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `0`                                                              |
| Default Seconds          | `default-seconds`         | Initializes the value of the `seconds` element, when no date is selected.                                                                                                                                                                    | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `0`                                                              |
| Disable                  | --                        | Disables certain dates, preventing them from being selected.                                                                                                                                                                                 | `DateLimit<DateOption>[]`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `[]`                                                             |
| Disable Mobile           | `disable-mobile`          | Sets this field to `true` in order to always use the non-native picker on mobile devices.                                                                                                                                                    | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Enable                   | --                        | Disables all dates except the specified ones.                                                                                                                                                                                                | `DateLimit<DateOption>[]`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `[(_) => true]`                                                  |
| Enable Seconds           | `enable-seconds`          | Enables seconds selection in the time picker.                                                                                                                                                                                                | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Enable Time              | `enable-time`             | Enables the time picker.                                                                                                                                                                                                                     | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Format Date              | --                        | Allows using a custom date formatting function instead of the built-in handling for date formats using `dateFormat`, `altFormat`, etc.                                                                                                       | `(date: Date, format: string, locale: Object) => string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `undefined`                                                      |
| Hour Increment           | `hour-increment`          | Adjusts the step for the hour input, including scrolling.                                                                                                                                                                                    | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `1`                                                              |
| Inline                   | `inline`                  | Displays the calendar in an inline manner.                                                                                                                                                                                                   | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Locale                   | `locale`                  | The locale, either as a string (e.g. "ar", "en") or as an object.                                                                                                                                                                            | `"ar" \| "at" \| "az" \| "be" \| "bg" \| "bn" \| "bs" \| "ca" \| "cat" \| "ckb" \| "cs" \| "cy" \| "da" \| "de" \| "default" \| "en" \| "eo" \| "es" \| "et" \| "fa" \| "fi" \| "fo" \| "fr" \| "gr" \| "he" \| "hi" \| "hr" \| "hu" \| "hy" \| "id" \| "is" \| "it" \| "ja" \| "ka" \| "ko" \| "km" \| "kz" \| "lt" \| "lv" \| "mk" \| "mn" \| "ms" \| "my" \| "nl" \| "nn" \| "no" \| "pa" \| "pl" \| "pt" \| "ro" \| "ru" \| "si" \| "sk" \| "sl" \| "sq" \| "sr" \| "sv" \| "th" \| "tr" \| "uk" \| "vn" \| "zh" \| "uz" \| "uz_latn" \| "zh_tw"` | `"en"`                                                           |
| Maximum Date             | `max-date`                | The maximum date that a user can pick to.                                                                                                                                                                                                    | `Date \| number \| string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | `null`                                                           |
| Maximum Time             | `max-time`                | The minimum date that a user can start picking from.                                                                                                                                                                                         | `Date \| number \| string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | `null`                                                           |
| Minimum Date             | `min-date`                | The minimum date that a user can start picking from.                                                                                                                                                                                         | `Date \| number \| string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | `null`                                                           |
| Minimum Time             | `min-time`                | The minimum time that a user can start picking from.                                                                                                                                                                                         | `Date \| number \| string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | `null`                                                           |
| Minute Increment         | `minute-increment`        | Adjusts the step for the minute input, including scrolling.                                                                                                                                                                                  | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `5`                                                              |
| Mode                     | `mode`                    | Date selection mode.                                                                                                                                                                                                                         | `"multiple" \| "range" \| "single" \| "time"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | `"single"`                                                       |
| Month Selector Type      | `month-selector-type`     | How the month should be displayed in the header of the calendar. If `showMonths` has a value greater than `1`, the month is always shown as static.                                                                                          | `"dropdown" \| "static"`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `"dropdown"`                                                     |
| Name               | `name`              | Input name. | `string`      | `undefiend`             |
| Next Arrow               | `next-arrow`              | HTML for the arrow icon, used to switch months.                                                                                                                                                                                              | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `'<span class="sicon-keyboard_arrow_right"></span>'`             |
| No Calendar              | `no-calendar`             | Hides the day selection in calendar. Use it along with `enableTime` to create a time picker.                                                                                                                                                 | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Placeholder              | `placeholder`             | Placeholder text to show on the input element.                                                                                                                                                                                               | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `salla.lang.get('blocks.buy_as_gift.select_send_date_and_time')` |
| Position                 | `position`                | How the calendar should be positioned with regards to the input.                                                                                                                                                                             | `"above center" \| "above left" \| "above right" \| "above" \| "auto center" \| "auto left" \| "auto right" \| "auto" \| "below center" \| "below left" \| "below right" \| "below" \| ((self: any, customElement: HTMLElement) => void)`                                                                                                                                                                                                                                                                                                             | `"auto"`                                                         |
| Position Element         | --                        | The element off, of which the calendar will be positioned.                                                                                                                                                                                   | `HTMLElement`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | `undefined`                                                      |
| Previous Arrow               | `prev-arrow`              | HTML for the left arrow icon, used to switch months.                                                                                                                                                                                         | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `'<span class="sicon-keyboard_arrow_left"></span>'`              |
| Required               | `required`              | Whether or not the input is required. | `boolean` | `false`              |
| Short Hand Current Month | `shorthand-current-month` | Whether or not to display the current month name in shorthand mode, e.g. `"Sep"` instead `"September"`.                                                                                                                                      | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Show Months              | `show-months`             | The number of months to be shown at the same time when displaying the calendar.                                                                                                                                                              | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `1`                                                              |
| Static                   | `static`                  | Positions the calendar inside the wrapper and next to the input element.                                                                                                                                                                     | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Time 24 Hours            | `time_-2-4hr`             | Displays time picker in 24 hour mode without `AM/PM` selection when enabled.                                                                                                                                                                 | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Value                    | `value`                   | Two way data binding to retrieve the selected `datetime` value.                                                                                                                                                                              | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | `null`                                                           |
| Week Numbers              | `week-numbers`            | Enables display of week numbers in calendar.                                                                                                                                                                                                 | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |
| Wrap                     | `wrap`                    | Visit [here](https://chmln.github.io/flatpickr/examples/#flatpickr-external-elements) for more details                                                                                                                                             | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | `false`                                                          |

## Events

| Event    | Description                                                                                               | Type               |
| -------- | --------------------------------------------------------------------------------------------------------- | ------------------ |
| `invalidInput` | This event will be fired when the input is invalid. | `CustomEvent<any>` |
| `picked` | This event will be fired when the file input gets changed by the user, which happens when selecting file(s). | `CustomEvent<any>` |
