In this article, we will list the group components in order to create the options related to the product. For example, the product's colors and size, which we need some special text components to display them. 


**Following is the location of a product's options components:**

```shell
└── src
  ├── views 
    ├── components
    |   ...
    |   └── product
    |   |   └── options
    |   |       ├── color.twig
    |   |       ├── date.twig
    |   |       ├── datetime.twig
    |   |       ├── donation.twig
    |   |       ├── image.twig
    |   |       ├── multiple-options.twig
    |   |       ├── number.twig
    |   |       ├── single-option.twig
    |   |       ├── splitter.twig
    |   |       ├── text.twig
    |   |       ├── textarea.twig
    |   |       ├── thumbnail.twig
    |   |       └── time.twig                               
    |   ...
    ...

```

Following are the option components which can be used to create a product's options :
- [Color](#color)
- [Date](#date)
- [Datetime](#datetime)
- [Donation](#donation)
- [Image](#image)
- [Multiple Options](#multiple-options)
- [Number](#number)
- [Single Option](#single-option)
- [Splitter](#splitter)
- [Text](#text)
- [Textarea](#textarea)
- [Thumbnail](#thumbnail)
- [Time](#time)

<hr/>

## Color
This component is a set of options rendered as colors, in which the customer can select the color they want. It can be useful to present a product's color.

### Example

<!--
focus: false
-->

![Color Options Components](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-01.png)

### Variables


<DataSchema id="1384760" />

### Usage
The HTML element _input_radio_ is used to allow customers to select one item at a time. The details of the product's colors can be displayed using a _for-loop_ statement through the `details` object.

```js
{% for detail in option.details %}
<div>
    <input type="radio"
           {{ detail.is_selected?'checked':'' }} 
           {{ detail.is_out?"disabled":"" }} 
           name="options[{{ option.id }}]"
           value="{{ detail.id }}" ... />

    <p>{{ detail.name }}</p>
    
    {% if detail.is_out %}
    <small>Out of stock</small>
    {% endif %}
</div>
{% endfor %}
```

## Date
This component works as a date picker to allow customers to select a date. It can be used as an input field for the delivery date, as an example.

### Example
<!--
focus: false
-->
![Date](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-02.png)

### Variables


<DataSchema id="1307388" />

### Usage
The HTML element _input_ is used to show this component with some predefined attributes.
```js
<input class="form-input date-element{{ required?' required':'' }}"
       id="..."
       data-min-date="today"
       placeholder="{{ option.placeholder }}"
       name="options[{{ option.id }}]" {{ attirubtes|raw }}
       readonly="readonly"
       value="{{ value }}"
       type="text"
/>
```

## Datetime
This component works as a date-and-time picker to allow customers to select a date and time. It can be used as an input field for the delivery date and time, as an example.
### Example

<!--
focus: false
-->

![Datetime](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-03.png)

### Variables


<DataSchema id="1384766" />


### Usage
The HTML element _input_ is used to show this component with some predefined attributes. Some of the main variables here are `option.placeholder` and `order_times`. 

```js
<input class="form-input date-time-element{{ required?' required':'' }}"
       placeholder="{{ option.placeholder }}"
       data-order-times="{{ order_times|json_encode }}"
       data-timezone="{{ time_zone }}"
       name="options[{{ option.id }}]"
        {{ attirubtes|raw }}
       {% if from_date_time %}
       data-from-date-time='{{ from_date_time }}'
       data-to-date-time='{{ to_date_time }}'
       {% endif %}
       readonly="readonly"
       value="{{ value }}"
       type="text"
/>
```
## Donation
The donation component is a track bar that allows customers to set or adjust a donation value. When the customer changes the value, the donation amount will take the value of this track bar.

### Example

<!--
focus: false
-->

![Donation](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-04.png)

### Variables


<DataSchema id="1384772" />

### Usage
In order for the customer to make a donation using the currently being viewed product, that product should allow donations. This can be checked by checking the variable `product.can_donate`. The price of that product can also be used as a default value by using `<input type="text"  value="{{ product.price }}" />`.

To check if the donation target has been accomplished, the developer can use the variable `product.donation.completed`.

```js
{% if product.can_donate %}
<div>
    <p>Donation Amount</p>
    <div>
        <input type="text"  value="{{ product.price }}" />
        ...
    </div>
</div>
{% else %}
<h4 class="...">
    {% if product.donation.completed %}
        <p>Donation Exceed Target Amount</p>
    {% else %}
        <p>Donation Exceed Target Date</p>
    {% endif %}
</h4>
{% endif %}
```

## Image
This component gives the user the ability to upload an image from the product page. It can be used in many ways, such as allowing customers to send more details about the product they require.

### Example

<!--
focus: false
-->

![Image](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-05.png)

### Variables

<DataSchema id="1384774" />

### Usage
The HTML element _input_ is used to show this component with some predefined attributes. 
```js
<input name="options[{{ option.id }}]"
       class="...""
       {% if value %}
            data-default="{{ value }}"
       {% endif %}
            {{ attirubtes|raw }}
       type="file"
/>
```

## Multiple Options
This component works similarly to "checkboxes" to allow the user to select one or more options from a set. 

### Example

<!--
focus: false
-->

![Multiple Options](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-06.png)

### Variables


<DataSchema id="1384775" />

### Usage
The list of given options to the user can be retrieved and displayed using the _for-loop_ statement through the `option object. This list of options includes details such as `full_name`.
```js
<div>
    {% for key,detail in option.details %}
    <div >
        <input  id="..." 
                {{ disabled?"disabled":"" }} {{detail.is_selected?"checked":"" }} 
                name="options[{{ option.id }}]" data-option="{{ option.id }}"
                value="{{ detail.id }}" type="checkbox" {{ attirubtes|raw }} />
        
        <p>{{ detail.full_name }}</p>
            
    </div>
    {% endfor %}
</div>
```

## Number
This component gives the user the ability to enter numbers, which can be used in many cases, such as entering the required quantity for a product.

### Example

<!--
focus: false
-->

![Number](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-07.png)

### Variables

<DataSchema id="1384780" />


### Usage
The HTML element _input_ is used to show this component with some predefined attributes. 

```js
<input placeholder="{{ option.placeholder }}"
       name="options[{{ option.id }}]"
       value="{{ value }}"
       type="text"
       {{ attirubtes|raw }}
/>
```

## Single Option
This component shows a list of options where the customer is allowed to select a single option at a time. The list is more compact and can support a longer list of options if needed.

### Example

<!--
focus: false
-->

![Single Option](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-08.png)


### Variables

<DataSchema id="1384788" />


### Usage
The HTML element _select_ is used to show this component with some predefined attributes. Inside this element, a _for-loop_ is used to iterate through the `options` which contains the list of the given options.
```js
<select name="options[{{ option.id }}]"
        data-option="{{ option.id }}" {{ attirubtes|raw }}>
    <option placeholder value="">{{ option.placeholder }}</option>
    {% for key,detail in option.details %}
        <option {{ detail.is_selected ? "selected" : "" }} value="{{ detail.id }}">
            {{ detail.full_name }}
        </option>
    {% endfor %}
</select>
```

## Splitter
This component defines a thematic break in a product page. It works similarly to the _hr_ HTML tag in order to create a splitter.

### Example

<!--
focus: false
-->

![Splitter](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-09.png)

### Variables


<DataSchema id="1384789" />

### Usage
The developer has the ability to create a _css class_ to style a break line. As shown below, we can apply this splitter class to any _div_ element.

```js
<div class="splitter"></div>
```

## Text
This component allows users to enter text. It can allow a single line and can be used to enter a product name, for example.

### Example

<!--
focus: false
-->

![Text](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-10.png)

### Variables


<DataSchema id="1384791" />


### Usage
The HTML element _input_ is used to show this component with some predefined attributes. The `option` object is used to show any detail related to the component, for example, `option.placeholder` to set a default text as a start.

```js
<input placeholder="{{ option.placeholder }}"
       name="options[{{ option.id }}]" {{ attirubtes|raw }}
       value="{{ value }}"
       type="text"/>
```
## Textarea
This component allows users to enter multiple line text. It allows the user to enter multiple lines of text and can be used to enter a note about a product, for example.

### Example

<!--
focus: false
-->

![Textarea](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-11.png)

### Variables


<DataSchema id="1384792" />


### Usage
The HTML element _input_ is used to show this component with some predefined attributes. The `option` object is used to show any detail related to the component, for example, `option.placeholder` to set a default text as a start.

```js
<textarea placeholder="{{ option.placeholder }}"
          name="options[{{ option.id }}]" {{ attirubtes|raw }}>
          {{ value }}
</textarea>
```

## Thumbnail
A thumbnail is a small image that represents a larger image when clicked on, and is commonly identified with a border around it. This component can be used to show example images of a product's options.

### Example

<!--
focus: false
-->

![Thumbnail](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-12.png)

### Variables


<DataSchema id="1384793" />

### Usage
The list of given thumbnails for a product's options can be retrieved and displayed using the _for-loop_ statement through the `option object. This list of options includes details such as `detail.image` to display the thumbnail's image. This component may use the  _html input radio_ element in order to restrict the user to selecting one image, option, at a time.

```js
{% for detail_index, detail in option.details %}
<input {{ detail.is_selected?'checked':'' }} 
       required type="radio" 
       id="option_{{ detail.id }}-{{ key_prefix }}" 
       {{ detail.is_out?"disabled":"" }} 
       data-itemid="{{ detail.id }}" 
       name="options[{{ option.id }}]"
       data-img-id="{{ detail.option_value }}" 
       value="{{ detail.id }}" 
/>

<img data-src="{{ detail.image }}" src="{{ asset('images/s-empty.png') }}" class="object-cover h-full w-full lazy-load"
    title="{{ detail.name }}" alt="{{ detail.name }}" />

<p>{{ detail.name }}</p>
{% if detail.is_out %}
<small>Out of stock</small>
{% endif %}
{% endfor %}
```
## Time
This component works as a time picker to allow customers to enter a specific time. It can be used as an input field for the delivery time, as an example.

### Example

<!--
focus: false
-->

![Time](https://cdn.salla.network/docs/twilight/4/pages-components-products-options-13.png)

### Variables


<DataSchema id="1384794" />

### Usage
The HTML element _input_ is used to show this component with some predefined attributes along with the `option` object.

```js
<input placeholder="{{ option.placeholder }}"
       name="options[{{ option.id }}]" {{ attirubtes|raw }}
       readonly="readonly"
       value="{{ value }}"
       type="text"/>
```