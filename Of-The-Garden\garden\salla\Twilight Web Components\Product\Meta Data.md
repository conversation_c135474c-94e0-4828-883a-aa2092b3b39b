The `<salla-metadata>` web component helps to show detailed specifications for a product. It can display one or multiple sections of information, like links, text, files, and other details about the product.

## Example

<!--
focus: false
-->

![Meta Data Example](https://cdn.salla.network/docs/twilight/6/js-web-metadata-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
    
  ```js
<salla-metadata entity-id="691043">
</salla-metadata>
```

      
  </Tab>  
</Tabs>


## Properties

| Property   | Attribute   | Description                                                                      | Type     | Default     |
| ---------- | ----------- | -------------------------------------------------------------------------------- | -------- | ----------- |
| Entity   | `entity`    | The entity type which its default value is `product`.                                                                 | `string` | `'product'` |
| Entity ID | `entity-id` | Either Product or Entity ID to which the specifications are going to be fetched for. | `number` | `undefined` |
