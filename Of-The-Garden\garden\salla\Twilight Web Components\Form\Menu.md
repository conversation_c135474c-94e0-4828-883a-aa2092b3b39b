 
The `<salla-menu>` web component displays a nested list items that either appear on the header section or footer section.

:::tip[API Usage]
Learn more about the Menu API usage from [here](https://docs.salla.dev/doc-705835?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::

## Example 
<!--
focus: false
-->

<Frame caption="Header">
  ![Image](https://cdn.salla.network/docs/twilight/6/js-web-menu-header-01.png)
</Frame>


<Frame caption="Footer">
  ![Image](https://cdn.salla.network/docs/twilight/6/js-web-menu-footer-01.png)
</Frame>

## Usage

<Tabs>
  <Tab title="HTML">

```html
<salla-menu source="footer"></salla-menu>
```
  </Tab>
  <Tab title="SASS">

This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
:host {
  display: block;
}
```
  </Tab>

</Tabs>

## Properties

| Property           | Attribute        | Type                               | Default          |
| ------------------ | ---------------- | ---------------------------------- | ---------------- |
| Limit        | `limit`          | Limiting the number of menu items options                                         | `number`                         | `undefined` |
| Source             | `source`        | `Sources.Footer \| Sources.Header` | `Sources.Header` |
| Source Value       | `source-value`  | `string`                           | `undefined`      |
| Top Navigation Bar | `topnav`        | `boolean`                          | `undefined`      |
| Use React Link     | `use-react-link`| `boolean`                          | `false`          |