This endpoint is used for fetching product lists from a Merchant Store. The endpoint accepts query parameters that are validated to ensure they are of the correct data type. If the validation succeeds, a GET request is made to the API endpoint and the response is returned. If the validation fails, an error message is returned via a Promise. 

:::tip
The *fetch* endpoint has been implemented in the [Product List](https://docs.salla.dev/doc-422719?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::

## Payload


<DataSchema id="1387254" />



## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427819" />
   
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of retrieving a list of products, the developer may call the method fetch() as follows. For this method we define the query parameters as an object with a source property of `categories` and a source_value property of an array `[1, 2, 3]`. We then call the `fetch()` method of the salla.product object and pass in the query parameters as an argument.


```js
// Define the query parameters
const queryParams = {
  source: 'categories',
  source_value: [1, 2, 3]
};

// Call the fetch method
salla.product.fetch(queryParams)
  .then((response) => {
    // Do something with the response data
    console.log(response);
  })
  .catch((error) => {
    // Handle any errors that occur
    console.error(error);
  });
```


## Events
This endpoint may trigger two events, the onProductListFetchSucceeded and onProductListFetchFailed events.

### onProductListFetchSucceeded
This event is triggered when the action of retrieving a list of products is done without having any errors coming back from the backend.

```js
salla.product.event.onProductListFetchSucceeded((response) => {
  console.log(response)
});
```
### onProductListFetchFailed
This event is triggered when the action of retrieving a list of products is not completed and an error has occurred.

```js
salla.product.event.onDetailFetchFailed((errorMessage) => {
  console.log(errorMessage)
});

