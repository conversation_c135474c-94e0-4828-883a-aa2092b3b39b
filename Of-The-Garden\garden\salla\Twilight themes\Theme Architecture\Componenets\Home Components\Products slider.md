This **_pre-defined component_** slider helps navigate between a list of products in the form of a scrolling list.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── products-slider.twig
          ...
```


### Example

<!--
focus: false
-->
![Product Slider](https://cdn.salla.network/docs/twilight/4/pages-components-home-products-slider-01.png)

### Variables


<DataSchema id="1383701" />


### Usage 
This component takes a list of the `products` that should be displayed, and then use **for-loop** statement to show them out.The method for displaying the products is `swiper-slide`. Developer has the option to use any other slider.

```php lineNumbers

<section id="best-offers-{{ position }}-slider">
    <div>
        {% if display_all_url %}
            <a href="{{ display_all_url }}">{{ trans('blocks.home.display_all') }}</a>
        {% endif %}
        <div>
            <button aria-label="Previous Slide"></button>
            <button aria-label="Next Slide"></button>
        </div>
    </div>
    <div data-id="{{ type }}-slider">
        {% for product in products %}
            <a href="{{ product.url }}">
                <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
                {% if product.promotion_title %} {{ product.promotion_title }} {% endif %}
            </a>
            <h3>
                <a href="{{ product.url }}">{{ product.name }}</a>
            </h3>
            {% if product.on_sale %}
                <div>
                    <h4>{{ product.sale_price|money }}</h4>
                    {{ product.regular_price|money }}
                </div>
            {% else %}
                <h4>{{ product.price|money }}</h4>
            {% endif %}
        {% endfor %}
    </div>
    {% endif %}
</section>
```

