This web component is used to display a form of rating scale using a star glyph or similar typographic symbol that is customizable in terms if `name`, `size`, and `value`, and that can be customized using the properties' parameters available.

## Example

<!--
focus: false
-->

![Rating Stars Example](https://cdn.salla.network/docs/twilight/6/js-web-rating-stars-01.gif)

## Usage
<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Rating Stars component usage -->
<salla-rating-stars
  name="large"
  size="large">
</salla-rating-stars>
```

  </Tab>
  <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-rating-stars` class. Following is a complete source code for customizing this component:

```js
.s-rating-stars{
  &-wrapper{

  }
  &-btn-star{

  }
  &-large{
    
  }
  &-medium{
    
  }
  &-small{
    
  }
  &-hovered{
    
  }
  &-selected{

  }
}
```
      
  </Tab>  
</Tabs>

## Properties

| Property | Attribute | Description                                    | Type     | Default     |
| -------- | --------- | ---------------------------------------------- | -------- | ----------- |
| Name     | `name`    | Sets the input name                            | `string` | `'rating'`  |
| Reviews     | `reviews`    | Number of reviews to display.                            | `number` | `'0'`  |
| Size     | `size`    | Adjusts the height and width of the component to fixed, standardized size. | `"large" \| "medium" \| "mini" \| "small"` | `'medium'`  |
| Value    | `value`   | Controls the numbers of rating stars           | `number` | `undefined` |
