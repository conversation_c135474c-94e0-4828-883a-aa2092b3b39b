**Salla Icons** is an icon library that can be easily integrated into a Twilight theme. It allows developers to add scalable vector icons to their website without the need for additional images. In this article, we'll go over the steps to add Salla Icons to a Twilight theme and show some examples of how to use it.

To see a full example of the available icons, the developer can check out the following Codepen example <a href="https://codepen.io/CGLion/pen/xxJeQPM">here</a>.


:::tip[A thing to know!]
The above example provides a comprehensive list of the icons available in Salla Icons and demonstrates how to use them in a variety of contexts.
:::


## 📙 What you'll learn

- [How to use Salla Icons](#how-to-use-salla-icons)
- [How to browse Salla Icons in an IcoMoon App](#browsing-salla-icons-in-an-icomoon)
<hr>

## How to use Salla Icons

The first step to using Salla Icons in a Twilight theme is to make sure that icons library's [CSS file](https://cdn.salla.network/fonts/sallaicons.css) in the theme's `head` section. <PERSON><PERSON><PERSON> can either link to the CSS file hosted on the Salla Icons website or download it and host it on your own server:

```html
<link rel="stylesheet" href="{{ 'fonts/sallaicons.css'|cdn }}"/>
```

Once the CSS file is linked, you can start using Salla icons on the theme by using the appropriate class names.

:::info[Information]
 **By default**, each Twilight theme includes the Salla Icons library
:::
### Usage

For example, to add a `sicon-discount-calculator` icon, the developer can use the following code:

```html
<i class="sicon-discount-calculator"></i>
```

Developer can also use Salla Icons in conjunction with other HTML elements, such as buttons. For example, to create a "Add to Cart" button with a shopping cart icon `sicon-add-to-cart`, you can use the following code:

```html
<button>
  <i class="sicon-add-to-cart"></i> Add to Cart
</button>
```

Developer can also use Font Salla Icons with liquid variables, for example, to show the number of items in the cart:

```html
<span class="sicon-cart"></span> {{ cart.items|length }} items
```

## Browsing Salla Icons in an IcoMoon

Salla Icons can be browsed and used from IcoMoon, a popular online icon library and tool for creating custom icon fonts. First, visit [IcoMoon](https://icomoon.io/), and then follow the following steps:

1. Download the Salla Icons Library from [here](https://cdn.salla.network/fonts/lib/sallaicons/sallaicons.svg).  **Note:** Click "s+ctrl" in windows or "Cmd -t" in Mac OS, to save the file locally.
2. Inside IcoMoon, go to [IcoMoonApp](https://icomoon.io/app/#/select).
3. Click on `Import Icon` on the upper left of the page.
4. Import the file downloaded in step 1.
5. Choose the Icons you wish to use by clicking on the Icon. The selected Icons will chage color if selected.
<!--
focus: false
-->
![image.png](https://cdn.salla.network/docs/twilight/4/salla-icons-01.png?v=1-10-2022)
<!--
focus: false
-->
7. Click on `Generate Font F` on th bottom right of the page
<!--
focus: false
-->
![image.png](https://cdn.salla.network/docs/twilight/4/salla-icons-02.png?v=1-10-2022)
<!--
focus: false
-->
8. Click on `prefrences` to enter a prefix
<!--
focus: false
-->
![image.png](https://cdn.salla.network/docs/twilight/4/salla-icons-03.png?v=1-10-2022)
<!--
focus: false
-->
9. Enter prefix `"sicon-"`
<!--
focus: false
-->
![image.png](https://cdn.salla.network/docs/twilight/4/salla-icons-04.png?v=1-10-2022)

10. Click `Download` to download the zip file for the Icons
<!--
focus: false
-->
![image.png](https://cdn.salla.network/docs/twilight/4/salla-icons-05.png?v=1-10-2022)

11. Extract the floder

12. Open Demo file, which will display the list of Icons with Salla Icon prefix
<!--
focus: false
-->
![image.png](https://cdn.salla.network/docs/twilight/4/salla-icons-06.png?v=1-10-2022)

The list of Salla Icons are now available for the developer to use in the themes.