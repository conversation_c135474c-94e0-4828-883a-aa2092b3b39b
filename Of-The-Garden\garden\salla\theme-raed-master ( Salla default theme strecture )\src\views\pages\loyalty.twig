{#
| Variable                             | Type          | Description                                  |
|--------------------------------------|---------------|----------------------------------------------|
| page                                 | Page          |                                              |
| page.title                           | string        |                                              |
| page.slug                            | string        |                                              |
| loyalty                              | Loyalty       |                                              |
| loyalty.name                         | string        |                                              |
| loyalty.description                  | string        |                                              |
| loyalty.image                        | string        |                                              |
| loyalty.promotion_title              | string        |                                              |
| loyalty.promotion_description        | string        |                                              |
| loyalty.points                       | Point[]       |                                              |
| loyalty.points[].name                | string        |                                              |
| loyalty.points[].description         | string        |                                              |
| loyalty.points[].type                | string        | share, rating, order, profile                |
| loyalty.points[].url                 | ?string       |                                              |
| loyalty.points[].points              | int           |                                              |
| loyalty.points[].icon                | string        |                                              |
| loyalty.points[].color               | string        |                                              |
| loyalty.prizes[]                     | PrizesGroup[] |                                              |
| loyalty.prizes[].title               | string        |                                              |
| loyalty.prizes[].type                | string        | free_product, coupon_discount, free_shipping |
| loyalty.prizes[].items[]             | Prize[]       |                                              |
| loyalty.prizes[].items[].id          | int           |                                              |
| loyalty.prizes[].items[].name        | string        |                                              |
| loyalty.prizes[].items[].description | string        |                                              |
| loyalty.prizes[].items[].image       | string        |                                              |
| loyalty.prizes[].items[].url         | ?string       |                                              |
| loyalty.prizes[].items[].cost_points | int           |                                              |
| user.loyalty_points                  | ?int          |                                              |
| user.is_profile_completed            | bool          |                                              |
#}
{% extends "layouts.master" %}

{% block content %}
    <div class="h-62 -mb-52 w-full bg-primary rtl:right-0 ltr:left-0 relative -z-1 bg-cover bg-no-repeat bg-center"
         style="background-image:url({{ loyalty.image }})">
        <div class="overlay h-full w-full bg-black opacity-50 absolute top-0 left-0"></div>
    </div>
    <div class="container space-y-8 lg:space-y-16">
        {# Breadcrumbs #}
        <nav class="breadcrumbs w-full py-5"><salla-breadcrumb></salla-breadcrumb></nav>

        {# loyalty banner #}
        <div class="loyalty__banner">
            <div class="loyalty__banner-inner">
                <i class="sicon-star2 star-anime text-amber-400 text-7xl"></i>
                <div class="loyalty__banner-content">
                    <div class="info">
                        <h1>{{ loyalty.name }}</h1>
                        <p>{{ loyalty.description }}</p>
                        {% if user.loyalty_points %}
                            <div class="loyalty-points">
                                {{ trans('pages.loyalty_program.you_have') }}
                                <span class="count-number count-anime" data-count="{{ user.loyalty_points }}">0</span>
                                {{ trans("pages.loyalty_program.point") }}
                            </div>
                        {% endif %}
                    </div>

                    <salla-loyalty customer-points="{{ user.loyalty_points }}">
                        <salla-button onclick="document.querySelector('salla-loyalty').open();" slot="widget">
                            {{ trans("pages.loyalty_program.exchange_points") }}
                        </salla-button>
                    </salla-loyalty>
                </div>
            </div>
            <!-- background big stars -->
            <div class="loyalty-star loyalty-star--first"><i class="sicon-star2"></i></div>
            <div class="loyalty-star loyalty-star--second"><i class="sicon-star2"></i></div>
        </div>

        {# ways to get points #}
        <section class="points-ways pt-6">
            <div class="s-block__title">
              <h2>{{ trans("pages.loyalty_program.ways_to_get_points") }}</h2>
            </div>
            <div class="points-ways__list">
                {% for point in loyalty.points %}
                    <div class="way-item">
                        <div class="flex rtl:space-x-reverse space-x-4">
                            <span class="way-item__icon {{ point.icon ?:'sicon-star' }}"
                                  style="color: {{ point.color }}">
                              <span style="background-color: {{ point.color }}"></span>
                            </span>
                            <div class="way-item__content">
                                <h3 style="color: {{ point.color }}">
                                    {{ point.points }}
                                    {{ trans("pages.loyalty_program.point") }}
                                </h3>
                                <p>{{ point.description }}</p>
                            </div>
                        </div>

                        {% if user.type == 'user' %}
                            <div class="way-item__action">
                                {% if point.type == 'share' %}
                                    <div class="border relative border-primary rounded-3xl h-[34px] text-center pr-14 pl-5">
                                        <salla-button
                                                onclick="app.copyToClipboard(event)"
                                                shape="link"
                                                data-content="{{ point.url }}"
                                                class="absolute w-12 top-0 right-0 h-full px-3 !pt-1 border-l border-primary hover:opacity-80"
                                                title="{{ trans('pages.loyalty_program.copy_link') }}">
                                            <i class="copy-icon sicon-swap-fill"></i>
                                        </salla-button>
                                        <h3 class="font-bold text-sm leading-8 whitespace-nowrap text-ellipsis overflow-hidden text-primary"
                                            dir="ltr">
                                            {{ point.url }}
                                        </h3>
                                    </div>
                                {% elseif customer.is_profile_completed and point.type == 'profile' %}
                                    <div class="text-green-500 text-center border relative border-green-300 rounded-3xl bg-green-50 min-h-[34px] w-full pt-1 px-4">
                                        <i class="sicon-check-circle d-inline-block"></i>
                                        <span class="break-all">{{ trans('pages.loyalty_program.completed') }}</span>
                                    </div>
                                {% else %}
                                    <salla-button
                                            fill="outline"
                                            width="wide"
                                            href="{{ point.url }}"
                                            class="!rounded-3xl text-sm !py-1.5">
                                        {{ point.name }}
                                    </salla-button>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </section>

        {# 2-block:: prizes #}
        {% for prize_group in loyalty.prizes %}
            <div>
              <salla-slider 
                type="carousel"
                loop="{{prize_group.items|length < 4  ? 'false' : 'true'}}"
                block-title="{{ prize_group.title }}"
                id="loyalty-slider-{{ prize_group.type }}">
                  <div slot="items">
                      {% for prize in prize_group.items %} 
                          <div class="slide--one-fourth">
                              <div class='product-entry flex-col'>
                                  {% set img %}
                                      <img class="lazy sm:h-full w-full object-cover rounded-t-md"
                                          src="{{ 'images/s-empty.png' | cdn }}"
                                          data-src="{{ prize.image }}"
                                          alt="{{ prize.name }}"/>
                                  {% endset %}
                                  {% if prize.url %}
                                      <a href="{{ prize.url }}" class='product-entry__image h-40'>
                                          {{ img }}
                                      </a>
                                  {% else %}
                                      {{ img }}
                                  {% endif %}
                                  <div class="flex-1 p-5">
                                      <h3 class="product-entry__title leading-6 mb-2.5">
                                          {% if prize.url %}
                                              <a href="{{ prize.url }}">{{ prize.name }}</a>
                                          {% else %}
                                              {{ prize.name }}
                                          {% endif %}
                                      </h3>
                                      <p class="text-sm leading-6 ">{{ prize.description }}</p>
                                  </div>
                                  {% if user.type == 'user' %}
                                      <div class="font-bold text-primary p-4 text-center border-t">
                                          {{ prize.cost_points }}
                                          {{ trans("pages.loyalty_program.point") }}
                                      </div>
                                  {% endif %}
                              </div>
                          </div>
                      {% endfor %}
                  </div>
              </salla-slider>
            </div>
        {% endfor %}
    </div>
{% endblock %}

{% block scripts %}
    <script defer src="{{ 'pages.js' | asset }}"></script>
{% endblock %}
 
