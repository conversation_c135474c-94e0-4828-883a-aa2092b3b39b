This endpoint helps the customer in the absence of a product's stock. A customer may need to subscribe to that product availability subscription list in order to be notified once the stock is available for purchase again. 

:::tip
The *product availability* endpoint has been implemented in the [Product Availability](https://docs.salla.dev/doc-422717?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and it's ready for use.

:::
## Payload

<DataSchema id="1427481" />

## Response
<Tabs>
  <Tab title="Success">
 
<DataSchema id="1427486" />
  </Tab>
   <Tab title="Error">
       
<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>



## Usage
To enable the customer to subscribe to a product and get notification once its stock is available, the developer can use the method `availabilitySubscribe()` to receive the customer's contact information and add them to the product availability subscription list.

#### Logged User


```js
// product id: 12345
salla.product.availabilitySubscribe({
  id: 12345
}).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.product.availabilitySubscribe(12345).then((response) => {
  /* add your code here */
});
```
#### Guest (Mobile)

```js
salla.product.availabilitySubscribe({
  id: 12345,
  country_code: "+966",
  mobile: "55558887",
}).then((response) => {
      /* add your code here */
});
```

#### Guest (Email)


```js
salla.product.availabilitySubscribe({
  id: 12345,
  email: "<EMAIL>",
}).then((response) => {
      /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onAvailabilitySubscribed and onAvailabilitySubscribedFailed events.

### onAvailabilitySubscribed
This event is triggered when subscribing a customer to a product availability subscription list is done without having any errors coming back from the backend.

```js
salla.event.product.onAvailabilitySubscribed((response) => {
  console.log(response)
});
```
### onAvailabilitySubscribedFailed
This event is triggered when subscribing a customer to a product availability subscription list is not completed and an error has occurred.

```js
salla.event.product.onAvailabilitySubscribedFailed((errorMessage) => {
  console.log(errorMessage)
});
