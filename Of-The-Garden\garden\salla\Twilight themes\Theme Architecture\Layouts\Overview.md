Layouts are considered the foundation for Salla theme, through which all of the [theme pages](doc-422556?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) will share the same [layout](https://docs.salla.dev/doc-422576?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). When building a Salla theme, you will need to start by planing for your layouts, so then you can use them to unify your pages' look-and-feel.

## 📙 What you'll learn about
- [Locate Layout files](#locate-layout-files) .
- [Master layout hooks](#master-layout-hooks).
- [Using layouts](#using-layouts).
- [Embed theme pages within the layout](embed-theme-pages-within-the-layout.).
- [Build a new layout](#build-a-new-layout).

<hr>

## Locate Layout files
According to the Twilight [directory structure](https://docs.salla.dev/doc-421918?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), all of the layouts should be stored in the [`src/views/layouts/`](https://github.com/SallaApp/theme-raed/tree/master/src/views/layouts) folder.

```shell
└── src
  ├── views
  |    ...
  |  ├── layouts
  |   ...

```

## Master layout hooks
Most of the theme [hooks](https://docs.salla.dev/422552m0?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) are used within the [master layout](https://docs.salla.dev/421944m0?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). This is due to the fact that this layout, which can be overwritten by the developer, is used across all of the website pages. It means that all of the hooks' contents will be injected into all of the website pages. 


## Using layouts
As we saw in the [Twig basic syntax](https://twig.symfony.com/doc/3.x/), template inheritance is one of twigs perks; it allows you to set a base code that contains all of the elements for your website and define blocks that can override from child templates.

Inheritance is the main concept of building layouts. The developer starts by creating an HTML page with the overall skeleton of the theme pages, with creating `blocks` inside it for future content. These future contents will be filled by the page that will inherit this layout page.

:::info[Information]
A [default layout](https://docs.salla.dev/421944m0?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is created at [`src/views/layouts/master.twig`](https://github.com/explore).
:::

## Embed theme pages within the layout.
In order to embed the theme pages within the layout page, first we need to extend, inherit, the layout page. In the following example, we have a `mylayout.twig` file where we define three blocks: `head`, `title` and `footer`. 
```php title= "mylayout.twig " lineNumbers= True
<!DOCTYPE html>
<html>

<head>
  <title>{% block title %}{% endblock %} - My Webpage</title>
  {% block block_with_default_content %}
  <link rel="stylesheet" href="style.css" />
  {% endblock %}
  {% block head %}{% endblock %}
</head>

<body>
  <div id="content">
    {% block content %}{% endblock %}
  </div>
  <div id="footer">
    {% block footer %}{% endblock %}
    &copy; Copyright 2011 by <a href="http://domain.invalid/">you</a>.
  </div>
</body>

</html>
```
To embed the theme pages within this layout, we start by extending them. Then we will enject the content into the defined three blocks: `head`, `title` and `footer`.

```js title ="template_page.twig"
{% extends "mylayout.twig" %}

{% block title %}Index{% endblock %}

{% block head %}
<style type="text/css">
  .important {
    color: #336699;
  }
</style>
{% endblock %}

{% block block_with_default_content %}
{{ parent() }}
<div>lets append a new content to our main block</div>
{% endblock %}

{% block content %}
<h1>Index</h1>
<p class="important">
  Welcome to my awesome homepage.
</p>
``` 

## Build a new layout
Building layouts is easy with Twilight; we simply need to create any new layout file inside the folder [`src\views\layouts\`](https://github.com/SallaApp/theme-raed/tree/master/src/views/layouts). This step will make the layout available throughout the theme pages. 

:::tip[]
Note that all of the new files are nothing but `*.twig` files.
:::


The main two steps for building layouts are:
- Create an HTML file, with the extension `*.twig`, that contains the main skeleton of your theme with creating `blocks` inside it for future contents.
- Extend this layout page inside your theme's pages, and enject the content within the predefined `blocks`.









