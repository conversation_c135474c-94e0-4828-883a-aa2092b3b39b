The `quickAdd` endpoint enables the customer to add a product directly from the products list to the cart without the need to open that product page. Under the hood, this endpoint calls the [`addItem`](https://docs.salla.dev/doc-422629?nav=01HNFTDZPB31Y2E120R84YXKCX) endpoint by passing the id of the product which will be added to the cart.

:::tip
The *quick add* endpoint has been implemented in the [Add Product](https://docs.salla.dev/doc-422692?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and it's ready for use.
:::


## Payload


<DataSchema id="1387213" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427381" />

  </Tab>
   <Tab title="Error">
      
<DataSchema id="1427314" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of adding a product into the cart, the developer may call the method `quickAdd` as follows:
```js
salla.cart.quickAdd({ id: 12345 }).then((response) => {
  /* add your code here */
});


// TIP: short version
salla.cart.quickAdd(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint calls the [`addItem`](https://docs.salla.dev/doc-422629?nav=01HNFTDZPB31Y2E120R84YXKCX) endpoint by passing the id of the product which will be added to the cart. Accordingly, all of the addItem's [events](https://docs.salla.dev/doc-422611?nav=01HNFTDZPB31Y2E120R84YXKCX) are applicable.