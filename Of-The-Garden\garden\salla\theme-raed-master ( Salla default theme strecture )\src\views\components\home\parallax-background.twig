{#
| Variable    | Type   | Description                                                      |
|-------------|--------|------------------------------------------------------------------|
| image       | string | Image url                                                        |
| url         | string | Link url                                                         |
| title       | string | Section title                                                    |
| link_text   | String | Link title                                                       |
| is_opacity  | Bool   | Should set overlay on the background or not                      |
| position    | Int    | Sorting number start from zero                                   |
#}

<section class="s-block s-block--full-banner s-block--full-bg">
    <div class="full-banner-entry" style="background-image: url({{ image.url }});">
        <div class="overlay h-full w-full {{ is_opacity?'bg-black':'' }} opacity-60 absolute top-0 left-0"></div>
        <div class="flex h-full items-center justify-center p-5 xs:p-11 relative">
            <div>
                {% if title %}
                <h2 class="text-2xl font-bold leading-12">{{title}}</h2>
                {% endif %}

                {% if url and link_text %}
                    <a href="{{ url }}" class="inline-block text-white text-sm border rounded-md font-bold px-8 py-4">{{ link_text }}</a>
                {% endif %}
            </div>
        </div>
    </div>
</section>
