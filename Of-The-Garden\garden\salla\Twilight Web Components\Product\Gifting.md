The `<salla-gifting>` web component is used to display items as gifts, which can be used after the customer has completed a purchase. It can be customized using the properties' parameters available.

:::tip[Note]
Available API Endpoints for the Gifting component is:

- [Gift Details](https://docs.salla.dev/doc-422646?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Add Gift To Cart](https://docs.salla.dev/doc-422647?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Upload Gift Image](https://docs.salla.dev/doc-422648?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::



## Example

<!--focus: false -->
![Gifting](https://cdn.salla.network/docs/twilight/6/js-web-gifting-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Gifting component usage -->
<salla-gifting
  product-id="1153090815"
  onclick="gift.open()">
</salla-gifting>
```
  </Tab>
    
  <Tab title="JS">
        
```js
// Save reference to the Gifting Component as below
const gift = document.querySelector("salla-gifting");
```      
  </Tab>  
    <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-gifting-steps-wrapper` class. Following is a complete source code for customizing this component:

```css
.s-gifting-steps-wrapper {
  transition: .2s cubic-bezier(.55, 0, .1, 1) 0s;
}

.s-gifting-select{
  background-image: url("data:image/svg+xml;utf8,<svg version='1.1' fill='gray' xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'><title>keyboard_arrow_down</title><path d='M9.875 11.104l6.125 6.125 6.125-6.125 1.875 1.875-8 8-8-8z'></path></svg>");
  background-size: 24px;
  background-repeat: no-repeat;
  background-position: 99%;
  appearance: none;
}
// dir rtl
[dir=rtl] .s-gifting-select{
  background-position: 5px;
}
```
      
  </Tab>  
  
</Tabs>


## Properties

| Property | Attribute | Description | Type  | Default |
| -------- | --------- | ----------- | ----- | ------- |
| Product ID      | `product-id`     | The product id for which the gifting system is required.         | `number` | `undefined`   |
| Widget Title|	`widget-title` |	The Widget's title value |	`string` |	`undefined`   |
| Widget Subtitle|	`widget-subtitle` |	The Widget's subtitle value |	`string` |	`undefined`   |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `currencywidget-btn-content` which is used to customize the widget's button content.

| Method  | Description | Return Type |
| ------- | ----------- | ----------- |
| `close()` | Hides / closes the gifting modal window.         | `Promise<HTMLElement>`       |
| `goToStep2()` | Go to the step 2 in the gifting system.          | `Promise<void>`       |
| `open()` | Shows / opens the gifting modal window.         | `Promise<any>`       |

## Slots
The`slots` makes it customizable to modify certain labels, such as `currencywidget-btn-content`.

| Slot       | Description                                                                                    |
| ---------- | ---------------------------------------------------------------------------------------------- |
| `currencywidget-btn-content` | It is used to customize the widget's button content. |