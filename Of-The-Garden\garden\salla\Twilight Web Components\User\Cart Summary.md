The `<salla-cart-summary>` web component is used to show the icon of the shopping cart with a small circle badge indicating the number of items in the cart.


## Example

<!--focus: false -->
![Cart Summary](https://cdn.salla.network/docs/twilight/6/js-web-cart-summary-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
  <!-- <!-- in case you want to use custom icon -->
 <!-- < i slot="icon" class="sicon-shopping-bag"></i> -->

```html
<!-- Basic Cart Summary component usage -->
<salla-cart-summary show-cart-label ="true"></salla-cart-summary>
```
    
  </Tab>
    
<Tab title="SASS">

This JS web component can be targeted for styling by its `.s-cart-summary` class. Following is a complete source code for customizing this component:

```css

.s-cart-summary{
  &-wrapper{

  }
  &-icon{

  }
  &-count{

  }
  &-total{
    
  }
}
```
      
  </Tab>    
</Tabs>


## Properties

| Property        | Attribute         | Description                             | Type                                                                             | Default     |
| --------------- | ----------------- | --------------------------------------- | -------------------------------------------------------------------------------- | ----------- |
| Show Cart Label |	`show-cart-label` |	Shows the cart label |	`boolean` |	`undefined` |


## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `animateToCart(image:any)` which animates the product images in the cart summary.


| Method                      | Description                                    | Return Type     |
| --------------------------- | ---------------------------------------------- | --------------- |
| `animateToCart(image: any)` | Animates the product Image in the cart summary | `Promise<void>` |
