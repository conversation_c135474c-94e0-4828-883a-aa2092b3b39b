This pre-defined component is used to display a menu for a group of the sub-pages' links in a vertical menu. It has a static view, which can easily be modified by the developer.

**Following is the location of this component.**

```shell
└── src
  ├── views 
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── vertical-menu-with-slider.twig
          ...
```

### Example

<!--
focus: false
-->
![image](https://cdn.salla.network/docs/twilight/4/pages-components-home-vertical-menu-with-slider-01.png.png)

### Variables


<DataSchema id="1383685" />

### Usage
This component renderes dynamic content with links and images by looping through items and conditionally adding classes.

``` php lineNumbers
<section>
  <div> {% component menu render_in 'home.vertical-cats'  with {title:title, icon:icon} %}
    <ul> {% for item in items %}
      <li>
        <a href="{{item.url}}">
          <img src="{{ item.image.url }}" alt="{{ item.image.alt }}" />
        </a>
      </li>
      {% endfor %}
    </ul>
  </div>
</section>
```