This endpoint allows the user to add an image file to the cart in the case that the user needs to attach an image to the order it is placing through the shopping cart.

## Payload

<DataSchema id="1427420" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427421" />

  </Tab>
   <Tab title="Error">


<DataSchema id="1427184" />
  </Tab>
  
</Tabs>

## Usage
To perform the action of uploading an image to the cart, the developer may call the `getUploadImageEndpoint()` as shown below.

```js
salla.cart.getUploadImageEndpoint({cart_id: 5432}).then((response) => {
  /* add your code here */
});
```

