The `<salla-social-share>` web component is used to display a menu with social media platforms and sharing capabilities in the form of email and copyable links. This can be customized using the properties' parameters.


## Example

![Social Share](https://api.apidog.com/api/v1/projects/451700/resources/343652/image-preview)

<!--
![Social Share](https://cdn.salla.network/docs/twilight/6/js-web-social-share-001.webp) -->

## Usage
<Tabs>
  <Tab title="HTML">
      
```html
<!-- Show soical share for large screen only
     mobile has native share via browser -->
<salla-social-share platforms="whatsapp, facebook, twitter"></salla-social-share>
```      
  </Tab>
  
</Tabs>




## Properties

| Property  | Attribute  | Description                                                                                | Type       | Default             |
| --------- | ---------- | ------------------------------------------------------------------------------------------ | ---------- | ------------------- |
| Platforms | `platforms`         | Selected platforms to share the content on, such as Facebook, Twitter, WhatsApp, Email, and shareable link | `string` | `'facebook,twitter,whatsapp,email,copy_link'` |
| URL       | `url`      | Custom Page URL that is to be shared                                                       | `string`   | `""`                 |
| URL Name  | `url-name` | Custom Page URL name that is be shared                                                     | `string`   | `""`                 |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `open` which opens or activates the share menu. 


| Method   | Description                      | Return Type     |
| -------- | -------------------------------- | --------------- |
| `open()` | Opens / Activates the share menu | `Promise<void>` |
| `refresh()` | Refreshs the URL and URL Name after re-rendering the component | `Promise<void>` |


## Slots
The`slots` makes it customizable to modify certain labels, such as `widget`.

| Slot       | Description                                                                                |
| ---------- | ------------------------------------------------------------------------------------------ |
| `widget` | An action that can be used to activete or open the component by calling the `open` method. |