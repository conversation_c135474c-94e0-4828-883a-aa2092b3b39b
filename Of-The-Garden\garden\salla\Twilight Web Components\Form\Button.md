The `<salla-button>` web component shows a customizable button, in terms of size, color, style, status, position etc ,which can be used with any other web component, and that can be customized using the properties' parameters available.


## Example

<!--
focus: false
-->

![Button Example](https://cdn.salla.network/docs/twilight/6/js-web-button-01.png)


## Usage
<Tabs>
  <Tab title="HTML">

```html
<!-- Basic Default Button-->
<salla-button> Default Button </salla-button>

<!-- Primary Start Loading Wide Button-->
<salla-button width="wide" loader-position="center" color="primary">
    Start Loading
</salla-button>

<!-- Button with loading effect-->
<salla-button fill="outline" loading="true" onclick="button.stop()">
    Stop Loading
</salla-button>

<!-- Solid Disabled Button-->
<salla-button fill="solid" onclick="button.disable()">
    Disable Button!
</salla-button>

<!-- Outlined Enabled Button-->
<salla-button fill="outline" disabled="true" onclick="button.enable())">
    Enable Button!
</salla-button>

<!-- Light Solid Link Button-->
<salla-button href="https://www.salla.sa/" fill="solid" color="light">
    Redirect URL
</salla-button>
```
      
  </Tab>
  <Tab title="JS">

```js
// Save reference to the Button Component below
const button = document.querySelector("salla-button");

// enable loading effect
button.load().then(() => {
  // let's call API request and await for it
  // let stop the loading effect
  setTimeout(() => button.stop(), 40000)
});
```
      
  </Tab> 
    <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-button-*` class. Following is a complete source code for customizing this component:


```js


.s-button{
  &-wrap{
    
  }
  &-element{
    
  }
  &-link{
    
  }
  &-icon{
    
  }
  &-btn{
    
  }
  &-solid{
    
  }
  &-outline{
    
  }
  &-fill-none{
    
  }
  &-large{
    
  }
  &-small{
    
  }
  &-wide{
    
  }
  &-primary{
    
  }
  &-success{
    
  }
  &-warning{
    
  }
  &-danger{
    
  }
  &-light{
    
  }
  &-gray{
    
  }
  &-dark{
    
  }
  &-primary-link{
    
  }
  &-success-link{
    
  }
  &-warning-link{
    
  }
  &-danger-link{
    
  }
  &-light-link{
    
  }
  &-gray-link{
    
  }
  &-dark-link{
    
  }
  &-primary-outline{
    
  }
  &-success-outline{
    
  }
  &-warning-outline{
    
  }
  &-danger-outline{
    
  }
  &-light-outline{
    
  }
  &-gray-outline{
    
  }
  &-dark-outline{
    
  }
  &-disabled{
    
  }
  &-loader{

  }
  &-loader-start{
    
  }
  &-loader-end{
    
  }
  &-loader-center{
    
  }
  &-loader-after{
    
  }
  &-text{

  }
  &-hide{

  }
}
```

      
  </Tab>  
</Tabs>


## Properties

| Property        | Attribute         | Description                             | Type                                                                             | Default     |
| --------------- | ----------------- | --------------------------------------- | -------------------------------------------------------------------------------- | ----------- |
| Color           | `color`           | Customized Button color                            | `"danger" \| "dark" \| "gray" \| "light" \| "primary" \| "success" \| "warning"` | `'primary'` |
| Disabled        | `disabled`        | Button disability status                | `boolean`                                                                        | `'false'`     |
| Fill            | `fill`            | Button fill style                       | `"none" \| "outline" \| "solid"`                                                 | `'solid'`   |
| href            | `href`            | Button with a link capability           | `string`                                                                         | `undefined` |
| Loader Position | `loader-position` | Set the loading animation's position    | `"after" \| "center" \| "end" \| "start"`                                        | `'after'`   |
| Loading         | `loading`         | Activate Button loading                 | `boolean`                                                                        | `'false'`     |
| Shape           | `shape`           | Button type Configs                     | `"btn" \| "icon" \| "link"`                                                      | `'btn'`     |
| Size            | `size`            | Button size Configs                     | `"small" \| "medium" \| "large"`                                                     | `'medium'`  |
| Width           | `width`           | Button extends the full available width | `"normal" \| "wide"`                                                               | `'normal'`    |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `enable` and/or `disable` loading the component.


| Method                  | Description                                                                                              | Return Type            |
| ----------------------- | -------------------------------------------------------------------------------------------------------- | ---------------------- |
| `disable()`             | Inactivates the button by adding `disabled` attribute. Visually, it becomes unclickable and unhoverable. | `Promise<void>`        |
| `enable()`              | Activates the button by removing `disabled` attribute.                                                   | `Promise<void>`        |
| `load()`                | Runs loading animation to notify a user that there is a processing taking place.                           | `Promise<HTMLElement>` |
| `stop()`                | Stops loading animation after a process has completed                                                    | `Promise<HTMLElement>` |
| `setText(html: string)` | Customizes the button's body text                                                                        | `Promise<HTMLElement>` |
