The `<salla-bottom-alert>` web component displays a message to the Merchant at the bottom of the used component. It blockes interaction with the rest of the screen until they are dismissed


## Example

![Bottom Alert Example](https://cdn.salla.network/docs/twilight/6/js-web-quick-buy-01.gif?v)

## Usage
<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Bottom Alert component usage -->
  <salla-bottom-alert
    message="Lorem Ipsum"
    type="banner"
    icon="sicon-anchor">
  </salla-bottom-alert>
```
      
  </Tab>
  
</Tabs>

## Properties

| Property      | Attribute      | Description                                                | Type                            | Default     |
| ------------- | -------------- | ---------------------------------------------------------- | ------------------------------- | ----------- |
| Action Label | `action-label` | Al<PERSON>'s Button label, which is used when the `type` value is either `link` or `popup`            | `string`                        | `undefined` |
| Action URL`   | `action-url`   | Alert's Button URL, which is used when the `type` value is `link`                        | `string`                        | `undefined` |
| Icon        | `icon`         | Alert's Icon class from [Salla Icons library](https://docs.salla.dev/doc-422550) | `string`                        | `undefined` |
| Message     | `message`      | Alert's Message which is a text that appears on the action button                                              | `string`                        | `undefined` |
| Type        | `type`         | Alert Type, which specifies how the alert should look like                                                 | `"banner" \| "link" \| "popup"` | `'popup'`   |