This endpoint is used to send the order's invoice to the customer's email. The invoice is to confirm the customer's order by showing the products he has ordered, their quantities, and their prices.

## Payload `authenticated`


<DataSchema id="1427908" />

## Response
<Tabs>
  <Tab title="Success">
      
<DataSchema id="1427912" />

  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of sending the order's invoice to the customer's email, the developer may call the method `send()` along with the `order_id`.

```js
salla.order.sendInvoice({ id: 98789 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.order.sendInvoice(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoins may trigger two events, the onSent and onNotSent events.

### onSent
This event is triggered when sending the order's invoice to the customer's email is done without having any errors coming back from the backend.

```js
salla.event.order.onSent((response) => {
  console.log(response)
});
```
### onNotSent
This event is triggered when sending the order's invoice to the customer's email is not completed and an error has occurred.

```js
salla.event.order.onNotSent((errorMessage) => {
  console.log(errorMessage)
});
