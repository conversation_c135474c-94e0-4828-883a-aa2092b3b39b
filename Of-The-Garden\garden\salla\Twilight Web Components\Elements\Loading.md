The `<salla-loading>` web component is used to convey that some data is currently loading to the user, and that can be customized using the properties' parameters available.

## Example

<!--
focus: false
-->

![Loading](https://cdn.salla.network/docs/twilight/6/js-web-loading-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
 
```html
<!-- Basic Loading component usage -->
<salla-loading
  size="120"
  width="16"
  color="pink"
  bg-color="black">
</salla-loading>
```
     
  </Tab>
  
  <Tab title="SASS">
   
This JS web component can be targeted for styling by its `.s-loading` class. Following is a complete source code for customizing this component:

```css
.s-loading {
  animation: spin 1s linear infinite;
  fill: var(--color-primary)
}

.s-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
```
   
  </Tab>
  
</Tabs>

## Properties

| Property  | Attribute  | Description                                           | Type               | Default     |
| --------- | ---------- | ----------------------------------------------------- | ------------------ | ----------- |
| Background Color | `bg-color` | Spinner background color.                             | `string`           | `"#e5e7eb"` |
| Color   | `color`    | Spinner content color.                                | `string`           | `undefined` |
| Size    | `size`     | Sets the diameter of the circle in pixels.            | `number \| string` | `32`        |
| Width   | `width`    | Sets the stroke of the circle, aka border, in pixels. | `number \| string` | `2`         |
