This  **_pre-defined component_** displays testimonials, which are feedback given by customers. The display order is set as per newest.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── testimonials.twig
          ...
```


### Example
<!--
focus: false
-->
![Testimonials Components](https://cdn.salla.network/docs/twilight/4/pages-components-home-testimonials-01.png)

### Variables


<DataSchema id="1383704" />



### Usage
The file `src\views\components\home\testimonials.twig` receives a list of `testimonials`, which are the customers' feedbacks, in the form of a collection of `items[]`,and then displays them inside a slider. The sorting of this collection is as per the newest.

In the following code we see how we can easily display the values of `item.avatar`, `item.name`, `item.text` inside the slider uing **for-loop**. Also `salla-button` is used here to navigate between `previous slide` and `next slide`.

```php lineNumbers
<h2>{{ trans('blocks.home.testimonials') }}</h2>

{% for item in items %}
    <img src="{{ item.avatar }}" alt="{{ item.name }}"/>

    <p>{{ item.text }}</p>
    <div>
        <h4>{{ item.name }}</h4>
    </div>
    <div>
        {% for i in range(0, item.stars) %}
            <i>*</i>
        {% endfor %}
    </div>
{% endfor %}
```

