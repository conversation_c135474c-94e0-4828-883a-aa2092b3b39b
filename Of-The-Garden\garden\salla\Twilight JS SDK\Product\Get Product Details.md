This endpoint is used to return information about a particular product to the user.

:::tip
The *get details* endpoint has been implemented in the [Product Options](https://docs.salla.dev/doc-422720?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


## Payload


<DataSchema id="1387256" />


## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427797" />
   
      
  </Tab>
   <Tab title="Error">
  
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of retrieving product details, the developer may call the method getDetails() as follows:


```js
salla.product.getDetails(23345, ["images", "sold_quantity", "category"])
  .then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onDetailFetched and onDetailFetchFailed events.

### onDetailFetched
This event is triggered when the action of retrieving product details is done without having any errors coming back from the backend.

```js
salla.product.event.onDetailFetched((response) => {
  console.log(response)
});
```
### onDetailFetchFailed
This event is triggered when the action of retrieving product details is not completed and an error has occurred.

```js
salla.product.event.onDetailFetchFailed((errorMessage) => {
  console.log(errorMessage)
});

