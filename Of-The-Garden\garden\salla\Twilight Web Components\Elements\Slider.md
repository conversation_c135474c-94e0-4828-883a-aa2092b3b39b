The `<salla-slider>` component allows you to create a slider that can display multiple slides, which can be navigated by the Merchant using arrows or thumbnails.


<!--focus: false -->
![Slider](https://cdn.salla.network/docs/twilight/6/js-web-slider-04.png?v)

## Usage

<Tabs>
  <Tab title="HTML">


```html
<!-- Basic Salla Slider component Usage -->
<salla-slider id="thumbs" show-controls="true" type="thumbs">
  <div slot="items">
    <img src="https://picsum.photos/600/400?">
    <img src="https://picsum.photos/600/400?">
    <img src="https://picsum.photos/600/400?">
    <img src="https://picsum.photos/600/400?">
  </div>
  <div slot="thumbs">
    <img src="https://picsum.photos/600/400?">
    <img src="https://picsum.photos/600/400?">
    <img src="https://picsum.photos/600/400?">
    <img src="https://picsum.photos/600/400?">
  </div>
</salla-slider>
```
      
  </Tab>
   <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-slider-wrapper` class. Following is a complete source code for customizing this component:

```css
.s-slider-wrapper {
  &-container{

  }
  &-thumbs{
    &-container{

    }
  }
  &-button-next{
    svg{

    }
  }
  &-button-prev{
    svg{
      
    }
  }
}
```
      
  </Tab> 
</Tabs>



## Properties

| Property                    | Attribute                     | Description                                                                                                                                                                                                                | Type                                                                                                             | Default     |
| --------------------------- | ----------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------- | ----------- |
| Arrows Centered             | `arrows-centered`             | Whether or not to center the slider's arrows                                                                                                                                                                               | `boolean`                                                                                                        | `false`     |
| Auto Height                 | `auto-height`                 | Whether or not to auto the height of the slider                                                                                                                                                                            | `boolean`                                                                                                        | `false`     |
| Auto Play                   | `auto-play`                   | Whether or not to enable autoplay, which only works with the following: `type="carousel" ` only                                                                                                                            | `boolean`                                                                                                        | `false`     |
| Block Subtitle              | `block-subtitle`              | Whether or not to show the slider's block sub title                                                                                                                                                                        | `string`                                                                                                         | `''`        |
| Block Title                 | `block-title`                 | Whether or not to show the slider's block title                                                                                                                                                                            | `string`                                                                                                         | `''`        |
| Centered                    | `centered`                    | Whether or not to enable the center mode, which only works with `type="carousel"` only                                                                                                                                     | `boolean`                                                                                                        | `false`     |
| Controls Outer              | `controls-outer`              | Whether or not to show arrow controls on the outerside of the slider                                                                                                                                                       | `boolean`                                                                                                        | `false`     |
| Direction                   | `direction`                   | The slider's direction, which is defaulted to: `document.documentElement.dir`                                                                                                                                              | `string`                                                                                                         | `undefined` |
| Display All Url             | `display-all-url`             | Whether or not to display all button beside arrows                                                                                                                                                                         | `string`                                                                                                         | `''`        |
| Grid Thumbs                 | `grid-thumbs`                 | Whether or not to disable the thumbs slider and rather show it as a grid                                                                                                                                                   | `boolean`                                                                                                        | `false`     |
| Listen To Thumbnails Option | `listen-to-thumbnails-option` | Whether or not to enable the calling of a specific slide by index from thumbnails option in the `salla-slider-options` component, which only works if `data-img-id` and `data-slid-index` attributes are set on each slide | `boolean`                                                                                                        | `false`     |
| Loop                        | `loop`                        | Run the slider in loop. It is recommended not to use it for slides with custom components inside of it, because that may cause some re-rendering issues                                                                    | `boolean`                                                                                                        | `false`     |
| Pagination                  | `pagination`                  | Whether or not to enable slide pagination. <TipInfo>Pagination will not be displayed unless the slider has an ID </TipInfo>                                                                                                                                                                                   | `boolean`                                                                                                        | `false`     |
| Show Controls               | `show-controls`               | Whether or not to show slider arrow controls                                                                                                                                                                               | `boolean`                                                                                                        | `true`      |
| Show Thumbs Controls        | `show-thumbs-controls`        | Whether or not to show thumbs slider controls                                                                                                                                                                              | `boolean`                                                                                                        | `true`      |
| Slider Config               | `slider-config`               | Set the slider's configurations. Refer to `https://swiperjs.com/swiper-api#parameters` and pass the entire `config` object                                                                                                 | `any`                                                                                                            | `undefined` |
| Slides Per View             | `slides-per-view`             | Show slides per view based on the user's interactivity with the slides                                                                                                                                                     | `string`                                                                                                         | `"auto"`    |
| Thumbs Config               | `thumbs-config`               | Set thumbs slider configurations Refer to `https://swiperjs.com/swiper-api#parameters` and pass the entire `config` object                                                                                                 | `any`                                                                                                            | `undefined` |
| Type                        | `type`                        | Set the type of the slider, which is defaulted to: ''                                                                                                                                                                      | `"" \| "blog" \| "carousel" \| "default" \| "fullscreen" \| "fullwidth" \| "hero" \| "testimonials" \| "thumbs"` | `''`        |
| Vertical                    | `vertical`                    | Align the main slider in either vertical or horizontal mode                                                                                                                                                                | `boolean`                                                                                                        | `false`     |
| Vertical Thumbs             | `vertical-thumbs`             | Align the thumbs slider in either vertical or horizontal mode                                                                                                                                                              | `boolean`                                                                                                        | `false`     |

## Events

| Event                        | Description                                                                                                       | Type               |
| ---------------------------- | ----------------------------------------------------------------------------------------------------------------- | ------------------ |
| `afterInit`                  | This event will be fired when the Slider is initialized immediately.                                              | `CustomEvent<any>` |
| `reachBeginning`             | This event will be fired when the Slider reaches its beginning (initial position)                                 | `CustomEvent<any>` |
| `reachEnd`                   | This event will be fired when Slider reaches last slide                                                           | `CustomEvent<any>` |
| `slideChange`                | This event will be fired when currently active slide is changed                                                   | `CustomEvent<any>` |
| `slideChangeTransitionStart` | This event will be fired when the animation starts to transition (either to the next or from the previous slide). | `CustomEvent<any>` |
| `slideChangeTransitionEnd`   | This event will be fired when the animation ends its transition (either to the next or from the previous slide).  | `CustomEvent<any>` |
| `slideNextTransitionStart`   | This event will be fired when the slide animation's transition starts onto the next slide.                        | `CustomEvent<any>` |
| `slideNextTransitionEnd`     | This event will be fired when the slide animation's transition ends onto the next slide.                          | `CustomEvent<any>` |
| `slidePrevTransitionStart`   | This event will be fired when the slide animation's transition ends onto the previous slide.                      | `CustomEvent<any>` |
| `slidePrevTransitionEnd`     | This event will be fired when the slide animation's transition starts onto the previous slide.                    | `CustomEvent<any>` |
| `sliderMove`                 | This event will be fired when the user touches and moves the mouse pointer the slider and interacts with it.      | `CustomEvent<any>` |
| `sliderTransitionEnd`        | This event will be fired after the transition ends.                                                               | `CustomEvent<any>` |
| `sliderTransitionStart`      | This event will be fired in the beginning of the transition.                                                      | `CustomEvent<any>` |
| `touchSliderEnd`             | This event will be fired when the slider's mouse pointer is released from the Slider.                             | `CustomEvent<any>` |
| `touchSliderMove`            | This event will be fired when the slider's mouse pointer touches moves over the Slider.                           | `CustomEvent<any>` |
| `touchSliderStart`           | This event will be fired when the slider's mouse pointer touches the Slider.                                      | `CustomEvent<any>` |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `getSlides` which gets all of the slider slides.


| Method                                                               | Description                                                                                                                                                                | Return Type     |
| -------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------- |
| `getSlides()`                                                        | Gets all of the slider's slides                                                                                                                                            | `Promise<any>`  |
| `slideNext(speed?: number, runCallbacks?: boolean)`                  | Runs a transition to the next slide.                                                                                                                                       | `Promise<void>` |
| `slideNextLoop(speed?: number, runCallbacks?: boolean)`              | Runs a transition to the next slide in an enabled looped condition.                                                                                                        | `Promise<void>` |
| `slidePrev(speed?: number, runCallbacks?: boolean)`                  | Runs a transition to the previous slide.                                                                                                                                   | `Promise<void>` |
| `slidePrevLoop(speed?: number, runCallbacks?: boolean)`              | Runs a transition to the previous slide in an enabled looped condition.                                                                                                    | `Promise<void>` |
| `slideReset(speed?: number, runCallbacks?: boolean)`                 | Resets the slider position to the currently active slide for the duration equal to the `speed` parameter.                                                                  | `Promise<void>` |
| `slideTo(index: number, speed?: number, runCallbacks?: boolean)`     | Runs a transition to the slide with the index number equal to the `index` parameter for the duration equal to `speed` parameter.                                           | `Promise<any>`  |
| `slideToClosest(speed?: number, runCallbacks?: boolean)`             | Resets the slider position to the closest slide / snap point for the duration equal to the `speed` parameter.                                                              | `Promise<void>` |
| `slideToLoop(index: number, speed?: number, runCallbacks?: boolean)` | Runs a looped condition transition to the slide with the index number equal to the `index` parameter for the duration equal to `speed` parameter                           | `Promise<void>` |
| `update()`                                                           | Either call this method after adding / removing or showing / hiding the slides manually. Also, any custom DOM modification has to have this method called upon the update. | `Promise<void>` |
| `updateAutoHeight(speed?: number)`                                   | Forces the slider to update its height, when `autoHeight` enabled, for the duration equal to the `speed` parameter.                                                        | `Promise<void>` |
| `updateProgress()`                                                   | Recalculates the slider's progress.                                                                                                                                        | `Promise<void>` |
| `updateSlides()`                                                     | Recalculates the number of slides and their offsets. Useful after adding / removing slides with JavaScript                                                                 | `Promise<void>` |
| `updateSlidesClasses()`                                              | Updates `active` / `prev` / `next` classes on the slides and bullets                                                                                                       | `Promise<void>` |

## Slots

The`slots` makes it customizable to modify certain labels, such as `items`.

| Slot       | Description                           |
| ---------- | ------------------------------------- |
| `items`  | Customizing the Slider items.         |
| `thumbs` | Customizing the Thumbs' slider items. |