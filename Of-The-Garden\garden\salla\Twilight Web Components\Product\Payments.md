
The `<salla-payments>` web component allows users to display Payments items including `sbc-id`, `withMadeInKsa`, and `withSbc`.

## Example 

![Payments Component](https://cdn.salla.network/docs/twilight/6/js-web-payment-01.png)

## Usage

<Tabs>

  <Tab title="HTML">

```
<salla-payments></salla-payments>
```

  </Tab>

  <Tab title="SASS">

This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
    :host {
    display: block;
    }
```
  </Tab>

</Tabs>



## Properties

| Property    	| Attribute      	| Description                                                                     	| Type  	| Default 	|
| --------------- | ------------------ | ----------------------------------------------------------------------------------- | --------- | ----------- |
| SBC ID     	| `sbc-id`       	| The SBC (Saudi Business Council) certificate ID.                                	| `string`  | `undefined` |
| With Made In KSA | `with-made-in-ksa` | Whether or not to include the ["Made in KSA"](https://saudimade.sa/en) certification.          	| `boolean` | `undefined` |
| With SBC   	| `with-sbc`     	| Whether or not to include the SBC (Saudi Business Council) certificate. | `boolean` | `undefined` |


## Slots
The`slots` makes it customizable to modify certain labels, such as `cod`.

| Slot    	| Description                                                   	|
| ----------- | ----------------------------------------------------------------- |
| `cod` 	| Replaces the Cash On Delivery (cod) item with the slot `image`.           	|
| `payment` | Replaces the payment item with the slots `image`, `name`. |
| `sbc` 	| Replaces the SBC certificate item with the slot `image`.   |



