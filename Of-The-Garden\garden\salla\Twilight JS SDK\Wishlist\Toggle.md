This endpoint switches a product's addition or removal from the customer's wishlist. It simply calls the other endpoints [Add](https://docs.salla.dev/doc-422654?nav=01HNFTDZPB31Y2E120R84YXKCX) and [Remove](https://docs.salla.dev/doc-422655?nav=01HNFTDZPB31Y2E120R84YXKCX) as needed. A copy of the customer's wishlist is saved in the [broswer local storage](https://docs.salla.dev/doc-422613?nav=01HNFTDZPB31Y2E120R84YXKCX) to speed the process of retrieving its content. Based on the content of the wishlist in the local storage, the items will be added or removed.

## Payload `authenticated`



<DataSchema id="1387280" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427829" />
  
  </Tab>
   
  
</Tabs>


## Usage
To perform the action of switching a product's addition or removal from a wishlist, the method `toggle()` may be called as below.

```js
salla.wishlist.toggle({ id: 12345 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.wishlist.toggle(12345).then((response) => {
  /* add your code here */
});
```

## Events
This endpoint calls the  [`add`](https://docs.salla.dev/doc-422654?nav=01HNFTDZPB31Y2E120R84YXKCX) and [`remove`](https://docs.salla.dev/doc-422655?nav=01HNFTDZPB31Y2E120R84YXKCX)endpoint, accordingly, all of their  [events](https://docs.salla.dev/doc-422611?nav=01HNFTDZPB31Y2E120R84YXKCX) are applicable.