The `<salla-infinite-scroll>` web component allows for infinite scrolling to load content continuously as the user scrolls down the page, eliminating the need for pagination, and that can be customized using the properties' parameters available.

## Example

<!--
focus: false
-->

![Infinite Scroll](https://cdn.salla.network/docs/twilight/6/js-web-infinite-scroll-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">


```html
<!-- infinite Scroll Settings-->
<salla-infinite-scroll
  next-page="infinit-scroll.html?page=2"
  container=".container"
  item=".item"
  next-page.autoload
>
  <!--  container Class Selector-->
  <div class="container">
    {% for product in products %} <!-- inner Item Class Selector -->
    <div class="item">
      <p>{{ product.image }}</p>
      <p>{{ product.name }}</p>
      <p>{{ product.price }}</p>
    </div>
    {% endfor %}
  </div>
</salla-infinite-scroll>
```
  </Tab>
    
  <Tab title="SASS">
      
This JS web component can be targeted for styling by its `.s-infinite-scroll` class. Following is a complete source code for customizing this component:

```js

.s-infinite-scroll {
  &-wrapper {

  }
  // last & error message wrapper.
  &-status{

  }
  // the message that displys after reaching the last page.
  &-last {

  }
  // the message that displys if there is an error on loading
  &-error {

  }
  // Load more button
  &-btn{
    // button text
    &-text{

    }
    // spinner loader
    &-loader{

    }
  }
}
```

  </Tab>
  
</Tabs>




:::tip[Note]
You may set the property `autoload` to be `true` for loading content as customers scroll through the content, or set it to `false` and add a [Button Component](Button.md) to activiate the content auto-scrolling capability.
:::


<!-- Auto Generated Below -->

## Properties

| Property  | Attribute   | Description                                                                             | Type      | Default                          |
| --------- | ----------- | --------------------------------------------------------------------------------------- | --------- | -------------------------------- |
| Autoload  | `autoload`  | Whether or not to autoload the next page's content when scrolling to its view           | `boolean` | `'false'`                        |
| Container | `container` | Class selector to know if the container is or is not the host `<salla-infinite-scroll>` | `string`  | `'.s-infinite-scroll-container'` |
| Item      | `item`      | Class selector to list items                                                            | `string`  | `'salla-infinite-scroll > *'`    |
| Load More Text | `load-more-text` | Informs the user that more data is available, and to prompt them to click on the button to load it.                                                                              | `string`  | `undefined`                   |
| Next Page | `next-page` | Loads content from a next page path URL                                                 | `string`  | `''`                             |
