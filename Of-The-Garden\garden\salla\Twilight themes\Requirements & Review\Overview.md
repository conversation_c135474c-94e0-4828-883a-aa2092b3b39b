Personalized experiences are provided by the Themes in the Salla Themes Marketplace for both online customers and retail owners. Before being approved for publication, every Theme is put through a rigorous vetting process that follows strict publishing criteria. By ensuring that Salla Themes are presented in a polished and unified manner, this meticulous procedure improves user experience. 

:::caution[Alert]
For a Theme to be published in the Salla Themes Marketplace, it must fulfil the following requirements. If these conditions were not met, the Theme will be instantly rejected and the Theme developer will have to make the required changes and resubmit the request for publishing .
:::

:::tip[Note]
The Salla team reserves the right to amend these terms at any moment, provided that advance notice is given to all partners.
:::


### References

Creating Theme with Salla Twilight Theme Engine is a straightforward process you can find a full [documentation ](https://docs.salla.dev/doc-421877?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)to guid your Theme development journey from Theme Creating to Theme Publishing all in one stop. 

<PERSON><PERSON> also provided tutorials as a quick guide handling the following topics among others:

| Article Title | Description |
|---------------|-------------|
| [Start with Twilight Themes 101](https://salla.dev/blog/start-with-twilight-themes-101/) | Learn the basics of Theme development, including creating a Salla Partners Account and initiating a Salla Theme using Twilight. |
| [Twilight VSCode for an Enhanced Theme Building Experience](https://salla.dev/blog/twilight-vscode-for-an-enhanced-theme-building-experience/) | Explore the Twilight Themes AutoComplete Extension on VSCode for Salla JS Web Components, and how to install and use it. |
| [Stand Out with Theme Settings](https://salla.dev/blog/stand-out-with-theme-settings/) | Dive into Theme Settings, showcasing behind-the-scenes details and guiding developers on creating a dark mode setting. |
| [Themes Figma Template, Innovative Design Resource](https://www.figma.com/community/file/1268914036251108812) | Discover the power of Figma in Theme development and how to use the Salla Figma template to streamline creation. |
| [Seize the Opportunity and Publish Your Theme Today!](https://salla.dev/blog/seize-the-opportunity-and-publish-your-theme-today/) | A comprehensive guide to successfully navigating the process of publishing your Theme. |


:::tip[Note]
 Checkout more Themes articles [here](https://salla.dev/category/theme/).

:::