This endpoint lists all of the products within a given category. Categories aid in the organisation of products so that visitors may quickly find what they're looking for in the store.

## Payload

<DataSchema id="1427521" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427522" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of retriving the categories for a product, the developer may call the method `categories()` as follows.


```js
salla.product.categories({ id: 17 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.product.categories(17).then((response) => {
  /* add your code here */
});
```

More about adding Categories as a component in this clip.


<Video src="https://youtu.be/WgItYTCSISI?si=GGjP7A5durx_A1To
"></Video>


## Events
This endpoint may trigger two events, the onCategoriesFetched and onCategoriesFailed events.

### onCategoriesFetched
This event is triggered when retriving the categories for a product is done without having any errors coming back from the backend.

```js
salla.event.search.onCategoriesFetched((response) => {
  console.log(response)
});
```
### onCategoriesFailed
This event is triggered when retriving the categories for a product is not completed and an error has occurred.

```js
salla.event.search.onCategoriesFailed((errorMessage) => {
  console.log(errorMessage)
});
