This endpoint is used for the purpose of rating an order. It fetches the order's id, which will be rated.


:::tip
The *order rating* endpoint has been implemented in the [Rating](https://docs.salla.dev/doc-422728?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, , and It's all setup to save developer's time and effort.
:::
 

## Payload `authenticated`

<DataSchema id="1387268" />


## Response
<Tabs>
    

  <Tab title="Success">

<DataSchema id="1427921" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of rating an order, the method `order()` may be called as below, with the id of the order to be rated.

```js
salla.rating.order({ order_id: 978 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.rating.order(978).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onOrderFetched and onOrderNotFetched events.

### onOrderFetched
This event is triggered when fetching the id of the order to be rated is done without having any errors coming back from the backend.

```js
salla.event.rating.onOrderFetched((response) => {
  console.log(response)
});
```
### onOrderNotFetched
This event is triggered when fetching the id of the order to be rated is not completed and an error has occurred.

```js
salla.event.rating.onOrderNotFetched((errorMessage) => {
  console.log(errorMessage)
});