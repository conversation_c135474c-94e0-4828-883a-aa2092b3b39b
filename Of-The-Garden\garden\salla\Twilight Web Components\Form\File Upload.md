The `<salla-file-upload>` web component is used to allow the user to allow uploading a file or a number of files is supported by the File Upload web component, with the help of completed and supported parameters.


## Example

<!--
focus: false
-->

![File Upload](https://cdn.salla.network/docs/twilight/6/js-web-file-upload-01.png)

## Usage
<Tabs>
<Tab title="HTML">
 
```html
<!-- Basic File Upload component usage -->
<salla-file-upload 
  profile-image = false
  name = "First File"
  required = false">
</salla-file-upload>
```     
  </Tab>  

</Tabs>


## Properties

| Property                           | Attribute                            | Description                                                                                                                                                                                                                                                                                                                                           | Type                                                                                                                                                                                                                                                                                                                                                                                                                                                      | Default                                                                                                                                       |
| ---------------------------------- | ------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| Accept                       | `accept`                       | Accepted file types.                                                                                                                                                                                                                                                                                                                          | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `image/png, image/jpeg, image/jpg, image/gif, video/*`                                                                                                                                        |
| Allow Browse                       | `allow-browse`                       | Enables or disables browser.                                                                                                                                                                                                                                                                                                                          | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Allow Drop                         | `allow-drop`                         | Enables or disables drag and drop.                                                                                                                                                                                                                                                                                                                    | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Allow Image Preview       | `allow-image-preview`       | Enable or disable preview mode when uploading an image                                                                                                                                                                                                                                                                                                                  | `boolean`                                                               | `true`                                          |
| Allow Multiple                     | `allow-multiple`                     | Enables or disables adding multiple files.                                                                                                                                                                                                                                                                                                            | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Allow Paste                        | `allow-paste`                        | Enables or disables pasting of files. Pasting files is not supported on all browesrs.                                                                                                                                                                                                                                                                 | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Allow Process                      | `allow-process`                      | Enables or disables the process button.                                                                                                                                                                                                                                                                                                               | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Allow Remove                       | `allow-remove`                       | When set to false, the remove button is hidden and disabled.                                                                                                                                                                                                                                                                                          | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Allow Reorder                      | `allow-reorder`                      | Allows users to reorder files with drag and drop interaction. **Note** that this only works in single column mode. It also only works on browsers that support pointer events.                                                                                                                                                                        | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Allow Replace                      | `allow-replace`                      | Allows drop to replace a file, only works when `allowMultiple` is `false`.                                                                                                                                                                                                                                                                            | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Allow Revert                       | `allow-revert`                       | Enables or disables the revert processing button.                                                                                                                                                                                                                                                                                                     | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Cart Item ID                       | `cartItemId`                       | Pass this property to prepare the upload URL automatically, such as uploading an attachement file in the cart item.                                                                                                                                                                                                                                                                                                     | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `undefined`                                                                                                                                        |
| Check Validity                     | `check-validity`                     | Set to `true` to enable custom validity messages. `FilePond` will throw an error when a parent form is submitted and contains invalid files.                                                                                                                                                                                                          | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Chunk Force                        | `chunk-force`                        | Forces chunks, even for files smaller than the set `chunkSize`.                                                                                                                                                                                                                                                                                       | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Chunk Retry Delays                 | --                                   | Amount of times and delayes between re-tried uploading of a chunk.                                                                                                                                                                                                                                                                                    | `number[]`                                                                                                                                                                                                                                                                                                                                                                                                                                                | `[500, 1000, 3000]`                                                                                                                           |
| Chunk Size                         | `chunk-size`                         | The size of a chunk in bytes.                                                                                                                                                                                                                                                                                                                         | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `5000000`                                                                                                                                     |
| Chunk Uploads                      | `chunk-uploads`                      | Enables chunked uploads. When enabled, it will automatically cut up files in `chunkSize` chunks before upload.                                                                                                                                                                                                                                        | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Credits                            | `credits`                            | Shows credits at the bottom of the upload element. Structure is like the following: [{label,url}].                                                                                                                                                                                                                                                    | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Disabled                           | `disabled`                           | Sets the disabled attribute to the output field.                                                                                                                                                                                                                                                                                                      | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Drop On Element                    | `drop-on-element`                    | Requires drop on the element itself to catch the file.                                                                                                                                                                                                                                                                                                | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `true`                                                                                                                                        |
| Drop On Page                       | `drop-on-page`                       | This will catch all files dropped on the webpage.                                                                                                                                                                                                                                                                                                     | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Drop Validation                    | `drop-validation`                    | When enabled, files are validated before they are dropped. A file is not added when it is invalid.                                                                                                                                                                                                                                                    | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| File ID                  | `file-id`                   | If the current file has an ID, pass it here as a property, which is to be passed back in the `removed` event.                                                                                                                                                                                                                                                                  | `number`                                                                | `undefined`                                              |
| Files                   | `files`                     | The uploaded files as json `[{url:"...", id:123}]` for delete possibility.                                                                                                                                                                                                                                                                       | `string`                                                                | `undefined`                                              |
|
| Force Revert                       | `force-revert`                       | Set to `true` to require the file to be successfully reverted before continuing.                                                                                                                                                                                                                                                                      | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Form Data                       | `formData`                       | A JSON form data that is to be injected in the submit request.                                                                                                                                                                                                                                                                      | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `"{}"`                                                                                                                                       |
| Height                       | `height`                       | The original height of the uploader, will be used to reset the height after the image is removed.                                                                                                                                                                                                                                                                      | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `undefined`                                                                                                                                       |
| Icon Process                       | `icon-process`                       | The icon used for processing actions.                                                                                                                                                                                                                                                                                                                 | `string` I                                                                                                                                                                                                                                                                                                                                                                                                                                                | `'<svg>...</svg>'`                                                                                                                            |
| Icon Remove                        | `icon-remove`                        | The icon used for removing actions.                                                                                                                                                                                                                                                                                                                   | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `'<svg>...</svg>'`                                                                                                                            |
| Icon Retry                         | `icon-retry`                         | The icon used for retrying actions.                                                                                                                                                                                                                                                                                                                   | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `'<svg>...</svg>'`                                                                                                                            |
| Icon Undo                          | `icon-undo`                          | The icon used for undoing actions.                                                                                                                                                                                                                                                                                                                    | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `'<svg>...</svg>'`                                                                                                                            |
| Ignored Files                      | --                                   | Ignored file names when handling dropped directories. **Note** that it is not supported on all browsers.                                                                                                                                                                                                                                              | `any[]`                                                                                                                                                                                                                                                                                                                                                                                                                                                   | `['.ds_store', 'thumbs.db', 'desktop.ini']`                                                                                                   |
| Instant Upload                     | `instant-upload`                     | Immediately upload new files to the server.                                                                                                                                                                                                                                                                                                           | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Item Insert Interval               | `item-insert-interval`               | The interval to use before showing each item being added to the list.                                                                                                                                                                                                                                                                                 | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `75`                                                                                                                                          |
| Item Insert Location               | `item-insert-location`               | You can either set the value to `'after'` to add files to end of list, when dropped at the top of the list or added using browse or paste, or set the value to `'before'` to add files at start of list, or set the value to a compare function to automatically sort items when added.                                                               | `"after" \| "before" \| ((a: FilePondFile, b: FilePondFile) => number)`                                                                                                                                                                                                                                                                                                                                                                                   | `'after'`                                                                                                                                     |
| Label Decimal Separator            | `label-decimal-separator`            | The decimal separator used to render numbers.                                                                                                                                                                                                                                                                                                         | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `undefined`                                                                                                                                   |
| Label Idle                         | `label-idle`                         | Default label shown to indicate this is a drop area. It will automatically bind browse file events to the element with CSS class `.filepond--label-action`.                                                                                                                                                                                           | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `${salla.lang.get('common.uploader.drag_and_drop')}<span class="filepond--label-action"> ${salla.lang.get('common.uploader.browse')} </span>` |
| Label Thousands Separator          | `label-thousands-separator`          | The thousdands number separator used to render numbers.                                                                                                                                                                                                                                                                                               | `string`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `undefined`                                                                                                                                   |
| Max Files Size                    | `max-files-size`                    | The maximum size of a file, for instance 3MB or 750KB                                                                                                                                                                                                                                                                                                      | `${number}KB}` \| `${number}MB`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `2MB`                                                                                                                                        |
| Max Files Count                    | `max-files-count`                    | The maximum number of files that can be handled.                                                                                                                                                                                                                                                                                                      | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `null`                                                                                                                                        |
| Max Parallel Uploads               | `max-parallel-uploads`               | The maxmimum number of files that can be uploaded in parallel.                                                                                                                                                                                                                                                                                        | `number`                                                                                                                                                                                                                                                                                                                                                                                                                                                  | `2`                                                                                                                                           |
| Method                  | `method`                    | The submit request method.                                                                                                                                                                                                                                                                                                                      | `string`                                                                | `'POST'`                                                 |
| Name                    | `name`                      | File input name for the native formData                                                                                                                                                                                                                                                                                                         | `string`                                                                | `undefined`                                              |
| Payload Name             | `payload-name`              | File input name in the request payload                                                                                                                                                                                                                                                                                                          | `string`                                                                | `undefined`                                              |
| Profile Image            | `profile-image`             | Set the component to be profile image uploader with a preview and a circular shape                                                                                                                                                                                                                                                              | `boolean`                                                               | `false`                                                  |
| Required                           | `required`                           | Sets the required attribute to the output field.                                                                                                                                                                                                                                                                                                      | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Server Config                      | `server-config`                      | A server configuration object describing how the feature should interact with the server.                                                                                                                                                                                                                                                             | `string \| { url?: string; timeout?: number; headers?: { [key: string]: string \| number \| boolean; }; process?: string \| ServerUrl \| ProcessServerConfigFunction; revert?: string \| ServerUrl \| RevertServerConfigFunction; restore?: string \| ServerUrl \| RestoreServerConfigFunction; load?: string \| ServerUrl \| LoadServerConfigFunction; fetch?: string \| ServerUrl \| FetchServerConfigFunction; remove?: RemoveServerConfigFunction; }` | `null`                                                                                                                                        |
| Store As File                      | `store-as-file`                      | Tells the feature to store files in hidden file input elements, so they can be posted along with normal form post. It only works if the browser supports the [DataTransfer constructor](https://caniuse.com/mdn-api_datatransfer_datatransfer), which is the case on browsers such as Firefox, Chrome, Chromium powered browsers and Safari version 14.1 and higher. | `boolean`                                                                                                                                                                                                                                                                                                                                                                                                                                                 | `false`                                                                                                                                       |
| Uploaded Image                      | `uploadedImage`                      | The uploaded image link or URL. | `string`                                                         | `undefined`                                                                                                                                       |
| URL                     | `url`                       | The url to submit the image into.                                                                                                                                                                                                                                                                                                               | `string`                                                                | `undefined`                                              |
| Value                   | `value`                     | The uploaded image link or URL                                                                                                                                                                                                                                                                                                                  | `string`                                                                | `undefined`                                              |


## Events

| Event    | Description                                                                            | Type               |
| -------- | -------------------------------------------------------------------------------------- | ------------------ |
| `added`    | Event emitted when the file has been added                                                                                  | `CustomEvent<{ error: FilePondErrorDescription; file: FilePondFile; }>` |
| `invalidInput` | This event will be fired when the input is invalid. | `CustomEvent<any>` |
| `remove` | This event will be fired when the file is about to be removed. Returns a `boolean` value. | `CustomEvent<any>` |
| `uploaded` | This event will be fired when the file has been uploaded, and a link to the file has been received from the server. | `CustomEvent<string>` |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `oppendFile` which appends an initial file.


| Method                                  | Description            | Return Type                     |
| --------------------------------------- | ---------------------- | ------------------------------- |
| `appendFile(file: FilePondInitialFile)` | Appends an intial file | `Promise<FilePondInitialFile[]` |
