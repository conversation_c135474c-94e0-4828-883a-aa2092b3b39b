Developers are empowered to develop and customize themes for Salla Stores in a swift, effortless, and robust way. These customizations might range from minor tweaks to complete redesigns. Build extraordinary theme customizations through the [Partners Portal](https://salla.partners/) and, then, publish them to the [Store Themes Marketplace](https://s.salla.sa/marketplace/themes/tag-all).


:::info[A thing to know!]
✅ Read and understand the [Directory Structure](doc-421918?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) for proper use of the Theme.
:::

## 📙 What You'll Learn

In this article, the developer will learn how to develop a theme in a local development environment and how to preview the new customization. The main outline will be:

- [Theme Development Workflow](#theme-development-workflow)
- [Twilight Watcher Plugin](#twilight-watcher-plugin)

## Theme Development Workflow

The previous step includes the [creation](doc-421877?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) of a new theme files, which means that a local directory has been created for theme's files, which were cloned from the synced GitHub repository to a local development environment on the local machine. 


:::tip[Tip]
If the theme was created using the [Partner Portal](https://salla.partners/), the developer will need to:
- Manually `git clone` the theme files from the synced GitHub repository to a local development environment on the local machine.
- Run the command `npm install` to download and install the dependencies listed in the package file 'package.json'.
- Ensure that you have full administrative access to your local files to successfully complete the installation process.
:::

Next is to step inside the theme's directory and to run the [Salla CLI command `preview`](https://docs.salla.dev/doc-422776?nav=01HNA8QHCPJTCY5VSEZ616JCAK), and open the given starter theme with an IDE:
<Tabs>
    <Tab>
```shell title="Command Line"
> cd theme_folder_name
> npm install
> salla theme preview
```
  </Tab>
    </Tabs>
    
Salla has developed an **interactive development environment** that is launched with the command, `salla theme preview`. During the development process, the [`preview`](https://docs.salla.dev/doc-422776?nav=01HNA8QHCPJTCY5VSEZ616JCAK) command will automatically handle the process of building and deploying the theme to a local server and preview browser. This will involve the following steps:

- Running a local development server to serve the local assets' directory.
- Opening a local preview browser with a selected demo store.
- Watching theme file changes. 
- Hot reloading the current previewed page each time changes in assets or views are detected. 
- Managing the preview environment including committing the changes to the synced GitHub repository if required. 
The following image shows the development workflow:

![image](https://cdn.salla.network/docs/twilight/1/develop-theme-01.png)

<br/>

This amazing workflow enables the developer to develop the theme just like any other client-side applications. This means that the developer has complete control over every step of the development process, starting with editing the theme's source code and quickly previewing any changes in the local browser.

## Twilight Watcher Plugin

The *Twilight Engine* provides a *Watcher* plugin, which performs the task of tracking any changes in the theme's files and delivering these changes to the CLI in order to be reflected into the current preview. The Watcher plugin is included in the [Webpack](https://webpack.js.org/plugins/install-webpack-plugin/#usage), which is simply a static module bundler for modern client-side applications.

When it processes the theme's files, [Webpack](https://webpack.js.org/plugins/install-webpack-plugin/#usage) creates an internal dependency structure from one or more entry points. The theme's files are then combined into one or more bundles, which are static assets that provide the theme's content by combining each module into a single file. 

The developer needs to make sure the Twilight Watcher plugin is added to the `webpack.config.js` file. This file can be found in the root theme's directory. More information about Webpack can be found [here](https://webpack.js.org/plugins/install-webpack-plugin/#usage).


:::info[Information]
The developer has the option to use any other static module bundler other than the Webpack.
:::


The **Twilight Watcher Plugin** can be added as follows:
<Tabs>
    <Tab>
        
```js title=".\webpack.config.js"
const ThemeWatcher = require('@salla.sa/twilight/watcher');
module.exports = {
    ...
  plugins: [
    ...
    new ThemeWatcher()
  ],
}
```
</Tab>
</Tabs>

<br/>

<!-- 

⏭️ **Next**, we will explore the [**Theme Setup**](doc-421879?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) step which includes managing the theme's basic information, screenshots, settings, features, custom components, and price.

> ### Development Workflow
>  ✅ `git clone`  the theme's files from the synced GitHub repository. <br/>
>  ✅ [`salla preview`](doc-422776?nav=01HNA8QHCPJTCY5VSEZ616JCAK) command to offline preview the theme's changes as per the local files during the development process. <br/>

Optional
>  ✅ `git push` the customized theme's files from the local environment to the synced GitHub repository. <br/>
>  ✅ (*Optional*) Preview the theme's files via the [Partners Portal](https://salla.partners/) to see the custom theme from the synced GitHub repository. This step is in case the developer needs to preview the theme using a demo or a live store.
-->