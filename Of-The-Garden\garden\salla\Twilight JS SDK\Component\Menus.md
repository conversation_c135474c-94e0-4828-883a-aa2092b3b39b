This endpoint retrieves menus for a specified component, such as `"header"` or `"footer"`. It is commonly used to fetch menus for a particular component in your theme, typically for dynamically rendering navigation or other UI elements. While `"header"` is the default component, you can specify `"footer"` where needed.

## Payload


<DataSchema id="3663618" />

## Response


<Tabs>
  <Tab title="Success">

<DataSchema id="3663842" />
  </Tab>
</Tabs>

## Usage

<Tabs>
  <Tab title="Header Example">
In the case of using `Header`, the developer can use the example below to receive the data.
    
```js
salla.api.component.getMenus('header')
  .then(data => {
    //...
  })
  .catch(error => {
    //...
  });
```
  </Tab>
  <Tab title="Footer Example">
On the other hand, when using `Footer`, the developer can receive the data using the example below.

```js
salla.api.component.getMenus('footer')
  .then(data => {
    //...
  })
  .catch(error => {
    //...
  });
```
  </Tab>
</Tabs>

## Events

The menu process may trigger two events during the menu process, onMenuFetched and onMenuFetchFailed.



### onMenuFetched

Triggered when the menus are successfully fetched.

```js
salla.event.component.onMenuFetched((response) => {
  console.log(response)
});
```

### onMenuFetchFailed

Triggered if there is an error during the fetching process.

```js
salla.event.component.onMenuFetchFailed((errorMessage) => {
  console.log(errorMessage)
});
```