The [`notifications page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/notifications.twig) can help display short texts/messages to notify customers with the latest updates regarding their orders.

``` shell title = "🌐 Page URL: http://www.store-domain.com/notifications"
└── src 
  ├── views
  |   ├── pages
  |   |   ├── customer
  |   |   |   ...
  |   |   |   ├── notifications.twig
              ...
```

### Example
<!--
focus: false
-->
![Customer Notifications](https://cdn.salla.network/docs/twilight/4/customer-notification-01.png)

### Variables

<DataSchema id="1383866" />

### Components
This page extends the default layout [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), and accordingly, it takes the unified look-and-feel. For example, all of the [`header's`](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and [`footer's`](https://docs.salla.dev/doc-422602?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) components will be  added automatically to this page.

In addition, the [`User`](models/user.json) model is accessible automatically on this page because it's included in the [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) layout file.

### JS Web Components
Customer's Notification page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores.

- Infinite Scroll [`<salla-infinite-scroll>`](https://docs.salla.dev/doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The [`notifications page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/notifications.twig) may call the following [hooks](https://docs.salla.dev/doc-422552) in order to inject extra information.

```js
{% hook 'customer:notifications.items.start' %}
{% hook 'customer:notifications.items.end' %}
```

### Usage
This page receives an array of `notifications` as an object. This array contains all of the notification's data for the currently logged-in user. Each notification can be displayed using a `for-loop` statement. 

A common implementation for this page, is to display each notification as a link, which the user can click to open and view the details of the notification he wants to see.


:::tip[Note]
The Salla component [`salla-infinite-scroll`](https://docs.salla.dev/doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD) has been used to ease the pagination process.
:::

```php lineNumbers=true
<salla-infinite-scroll next-page="{{ notifications.next_page }}" item=".notifications-row">
    {% for notification in notifications %}
        <div class="notifications-row">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.sub_title }}</p>
            <span> {{ notification.date }} </span>
        </div>
    {% else %}
        <div>
            <p>{{ trans('blocks.header.no_notifications') }}</p>
        </div>
    {% endfor %}
</salla-infinite-scroll>
```






