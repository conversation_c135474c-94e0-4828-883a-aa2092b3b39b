This endpoint is used to show an order details, such as the quantity, price, delivery date,and payment terms. Mainly it should be called in the [thank you](https://docs.salla.dev/doc-422577?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) page to display the order details.

## Payload `authenticated`

<DataSchema id="1427916" />

## Response

<Tabs>
  <Tab title="Success">

<DataSchema id="1427917" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of showing the order's details, the developer may call the method `send()` along with the `order_id`.

```js
salla.order.show({ id: 98789, url: "/" }).then((response) => {
  /* add your code here */
});
```

