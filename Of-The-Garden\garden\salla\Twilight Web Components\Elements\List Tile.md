The `<salla-list-tile>` web component is used to display listing items in a tile form, which is one of the popular ways used in E-commerce. List tile component allows this style of display to be applied while supporting various properties and slots to customize the component.

## Example

<!--
focus: false
-->

![List Tile](https://cdn.salla.network/docs/twilight/6/js-web-list-tile-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
 
```html
<!-- Basic List Tile component usage -->
<salla-list-tile href="https://salla.dev">
  
  <div slot="icon">
    <img class="user-avatar" src="https://salla.dev/image" alt="">
  </div>

  <div slot="title">
    <div>Title</div>
  </div>

  <div slot="subtitle">
    <div class="">Content</div>
  </div>

  <div slot="action">
    <salla-button>Learn more</salla-button>
  </div>

</salla-list-tile>
```     
  </Tab>
  
</Tabs>


## Properties

| Property | Attribute | Description                                                                                | Type                                                        | Default     |
| -------- | --------- | ------------------------------------------------------------------------------------------ | ----------------------------------------------------------- | ----------- |
| Href     | `href`    | Designates the component as anchor and applies the `href` attribute.                       | `string`                                                    | `undefined` |
| Target   | `target`  | Designates the `target` attribute. This should only be applied when using the `href` prop. | `"_blank" \| "_parent" \| "_self" \| "_top" \| "framename"` | `"_self"`   |

## Slots
The`slots` makes it customizable to modify certain labels, such as `action`.

| Slot         | Description                                   |
| ------------ | --------------------------------------------- |
| `action`   | An element to display after the title.        |
| `icon`     | An icon to display before the title.          |
| `subtitle` | Additional content displayed below the title. |
| `title`    | The primary content of the list tile.         |
