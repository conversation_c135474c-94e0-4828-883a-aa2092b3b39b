The `<salla-modal>` web component displays a dialog box or pop-up window on top of the current page. It is positioned above all other components in the application, restricting scrolling of the main page and only allowing scrolling of the modal's content. It consists of a [But<PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component to activate the modal, and that can be customized using the properties, events, methods and slots' parameters available.



## Example

![Modal Example](https://cdn.salla.network/docs/twilight/6/js-web-modal-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Button to Activate the Modal-->
<button onclick="(function(){event.preventDefault();
    document.querySelector('#subscribe-modal').open();
    return false;})();
    "data-modal-id="subscribe-modal">
        Open Modal
</button>
      
<!-- Modal Settings -->
<salla-modal id="subscribe-modal"></salla-modal>
```
      
<!--
<!-- Button to Activate the Modal-->
<salla-button onclick="modal.open()">Click Me!</salla-button>

<!-- Modal Settings -->
<salla-modal
        id="modal"
        title="Title Header"
        sub-title="Title Sub-Header"
        icon="sicon-alert-engine"
        position="middle"
        width="xs"
        is-closable="true">
    
    <!-- Some awesome content within the modal. Custom Slot for the Modal-->
    <div slot="footer">
        <salla-button width="wide" onclick="modal.close()">Close</salla-button>
    </div>
    
</salla-modal>

  
  </Tab>
   

<!--
```js
// Save reference to the Modal Component below
var modal = document.querySelector("salla-modal");

modal.addEventListener("modalVisibilityChanged", function (status) {
  if(status) {
    // Listen for open events
    console.log("Modal Opened");
  } else {
    // Listen for close events
    console.log("Modal Closed");
  }
});
``` 
-->

      <Tab title="SASS">
 
This JS web component can be targeted for styling by its `.s-modal` class. Following is a complete source code for customizing this component:

```js
.s-modal {
  &-container{

  }
  &-wrapper {

  }
  &-close {

  }
  &-header {

  }
  &-header-inner {

  }
  &-header-content {

  }
  &-header-img {

  }
  &-icon {

  }
  &-bg-normal {

  }
  &-bg-success {

  }
  &-bg-error {

  }
  &-bg-primary {

  }
  &-text-success {

  }
  &-text-error {

  }
  &-title {

  }
  &-sub-title {

  }
  &-title-below {

  }
  &-body-slot {

  }
  &-body {

  }
  &-padding {

  }
  &-spacer {

  }
  &-align-middle {

  }
  &-align-top {

  }
  &-align-bottom {

  }
  &-overlay {

  }
  &-overlay-leaving {

  }
  &-entering {

  }
  &-leaving {

  }
  &-is-center {

  }
  // modal sizes
  &-xs {

  }
  &-sm {

  }
  &-md {

  }
  &-lg {

  }
  &-xl {

  }
  &-full {

  }
}
```
     
  </Tab>
</Tabs>

## Properties

| Property        | Attribute         | Description                                          | Type                                             | Default       |
| --------------- | ----------------- | ---------------------------------------------------- | ------------------------------------------------ | ------------- |
| Cenetered       | `cenetered`       | Aligns the modal's content to be in center view      | `boolean`                                        | `'false'`     |
| Has Skeleton    | `has-skeleton`    | Opens the modal on rendering the component           | `boolean`                                        | `'false'`     |
| Icon Style      | `icon-style`      | Sets the modal's header icon style.                  | `"error" \| "success" \|"normal" \| "primary"`   | `'undefined'` |
| Is Closable     | `is-closable`     | Sets the modal to be at the closing state            | `boolean`                                        | `'true'`      |
| Is Loading      | `is-loading`      | Shows the loading state in the modal                 | `boolean`                                        | `'false'`     |
| No Padding      | `no-padding`      | Adds no Padding to the modal                         | `boolean`                                        | `'false'`     |
| Position        | `position`        | Positions the modal in a fixed part of the screen    | `"bottom" \| "middle" \| "top"`                  | `'middle'`    |
| Sub Title       | `sub-title`       | Sets the modal subheader context                     | `string`                                         | `''`          |
| Sub Title First | `sub-title-first` | Whether or not to show the subtitle before the title | `boolean`                                        | `'false'`     |
| Visible         | `visible`         | Shows the modal on rendering the component           | `boolean`                                        | `'false'`     |
| Width           | `width`           | Adjusts the size of the modal                        | `"full" \| "lg" \| "md" \| "sm" \| "xl" \| "xs"` | `'md'`        |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `close` which closes the modal dialog.


| Method                      | Description                        | Return Type            |
| --------------------------- | ---------------------------------- | ---------------------- |
| `close()`                   | Closes the modal dialog            | `Promise<HTMLElement>` |
| `loading()`                 | Loads the modal dialog             | `Promise<HTMLElement>` |
| `setTitle(modalTitle: any)` | Sets the title of the modal dialog | `Promise<HTMLElement>` |
| `open()`                    | Opens the modal dialog             | `Promise<HTMLElement>` |
| `stopLoading()`             | Stops loading the modal dialog     | `Promise<HTMLElement>` |


:::tip[Note]
To use a method, you can for instance use `open` the component via the method:

```html
onclick="salla.event.dispatch('modal::open','modal_id')"
```
and `close` the component via the method:
 ```html
onclick="salla.event.dispatch('modal::close','modal_id')"
```
:::


## Slots
The`slots` makes it customizable to modify certain labels, such as `footer`

| Slot     | Description         |
| -------- | ------------------- |
| `footer` | Footer of the modal |

## Events

| Event                    | Description                                                  | Type                   |
| ------------------------ | ------------------------------------------------------------ | ---------------------- |
| `modalVisibilityChanged` | This event will be fired when the modal visibilty is changed | `CustomEvent<Boolean>` |

<!--

## Tips

Here are some what and what nots for best practises to using the component:

:::tip[Follow]

- When you need to show material that briefly prevents users from interacting with the app's main view.
- When you need to get a user's approval before proceeding.
- When the user must execute a specific activity.
- As a technique to avoid or fix critical problems, for important alerts.

:::

:::danger[Avoid]

For non-essential content that has nothing to do with the present user flow.
To interrupt a purchasing flow in the process of it.
When the modal needs additional information to make a decision that isn't accessible in the modal.

:::

-->