This endpoint fetches the last cart created by the customer. In other words, the cart will be fetched based on the latest products.

## Response
<Tabs>
  <Tab title="Success">


<DataSchema id="1427234" />
      
  </Tab>
   <Tab title="Error">


       
<DataSchema id="1427314" />
  </Tab>
  
</Tabs>


## Usage
The `latest()` method does not receive any parameters. Simply, the developer may call this method and be able to get the last cart created by the customer.

```js
salla.cart.latest().then((response) => {
  /* add your code here */
});

```


## Events
This endpoint may trigger two events, the onLatestFetched and onLatestFailed events.

### onLatestFetched
This event is triggered when fetching the last cart created by the customer is done without having any errors coming back from the backend.

```js
salla.event.cart.onLatestFetched((response) => {
  console.log(response);
});
```

### onLatestFailed
This event is triggered when fetching the last cart created by the customer is not completed and an error has occurred.

```js
salla.event.cart.onLatestFailed((errorMessage) => {
  console.log(errorMessage);
});
```
