This [`wishlist page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/wishlist.twig) can help customers save items that they wish to buy, this can greatly help to check the offers that apply to the items the customers are interested in as well as keeping track of the stock.

``` shell title = "🌐 Page URL: http://www.store-domain.com/wishlist"
└── src 
  ├── views
  |   ├── pages
  |   |   ├── customer
  |   |   |   ...
  |   |   |   ├── wishlist.twig
              ...
```

### Example
<!--
focus: false
-->
![Customer Wishlist](https://cdn.salla.network/docs/twilight/4/customer-wishlist-01.png)


### Variables
<DataSchema id="1383885" />


### Components
This page extends the default layout [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), and accordingly, it takes the unified look-and-feel. For example, all of the [`headers`](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and [`footers`](https://docs.salla.dev/doc-422602?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) components will be  added automatically to this page.

In addition, the `User` model is accessible automatically on this page because it's included in the [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) layout file.

### JS Web Components
Customer Wishlist page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores.

- Add Product [`<salla-add-product-button>`](https://docs.salla.dev/doc-422692?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The  `wishlist page template` may call the following [hooks](https://docs.salla.dev/doc-422552?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) in order to inject extra information.

```php lineNumbers=true
{% hook 'customer:wishlist.items.start' %}
{% hook 'customer:wishlist.items.end' %}
```

### Usage
This page simply receives the `products` object which represents the list of the products that the customer has added to his own wishlist. Based on that, we use a simple `for-loop` statement to display each product. 

The developer can show a button to encourage the customer to move a product from the wishlist into the cart. For this purpose [`salla-button`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component can be used.
 

```php lineNumbers=true
{% for product in products %}
    <a href="{{ product.url }}">
        <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
    </a>
    <h3>
        <a href="{{ product.url }}">{{ product.name }}</a>
    </h3>
    <div>
        {% if product.on_sale %}
            {{ product.sale_price|money }}
            <small>{{ product.regular_price|money }}</small>
        {% else %}
            {{ product.price|money }}
        {% endif %}
    </div>

    <salla-button onclick="salla.cart.addItem({{ product.id }})">
        {{ trans('pages.cart.add_to_cart') }}
    </salla-button>

    <salla-button onclick="salla.wishlist.remove({{ product.id }})">
        <i class="sicon-cancel">Remove</i>
    </salla-button>
{% else %}
    {{ trans('pages.profile.wishlist_placeholder') }}
{% endfor %}
```



