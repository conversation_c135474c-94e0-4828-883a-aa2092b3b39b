The [`brand listing page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/brands/index.twig) is used to render the list of all of the available brands in the store. This template shows the list of the alphabet characters, which represent the first characters of each available brand. Then comes the list of the brands' logos for the chosen letter. The developer has complete control over the appearance of this page.

``` shell title = "🌐 Page URL: http://www.store-domain.com/brands"
└── src 
  ├── views
    ├── pages
    |   ├── brands
    |   |   ├── index.twig
    |   ...
    ...
```

### Example
<!--
focus: false
-->
![Single blog](https://cdn.salla.network/docs/twilight/4/brand-listing-01.png)

### Variables


<DataSchema id="1383858" />


### Components
The category page includes the [Breadcrumbs](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) component. Breadcrumbs are a set of links that indicate the current page and its "ancestors" leading back to the site's homepage.

```php
{% component 'header.breadcrumbs' %}
```

### Hooks
The [`brand listing page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/brands/index.twig) calls for the following [hooks](https://docs.salla.dev/doc-422552) in order to inject extra information:

```php
{% hook 'brands:index.items.start' %}
{% hook 'brands:index.items.end' %}
```
### Usage
This page receives the object `brands` which is a collection grouped by character, for example:

```json
{a:[{'name':'Apple',...}, b:[{'name':'Beek',...},...]}
```

Each character is for a group of brands that start with that character. Using `nested _for-loop` statements, the developer can retrive the list of the characters, and for each character, the internal loop can retriv the list of the brands.
For example, brands with the letter "a", would display the brands which names start with the letter "a" such as `Apple,Armani, etc..`.<br>

```php lineNumbers=true
{% if brands|length %}
    {% for char,brandGroup in brands %}
        {{ char }}
        {% for brand in brandGroup %}
            {{ brand.name }}
        {% endfor %}
    {% endfor %}
{% else %}
{% endif %}
```

The object `page` here is used to show the current page deatiles, for example: the page title:

```php
{{ page.title }}
```


