This component comes with many attractive elements to display products for the customers. It has Headings to describe the sections and a swiper slider to list as much products as possible.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── slider-products-with-header.twig
          ...
```



### Example
<!--
focus: false
-->
![Slider Products](https://cdn.salla.network/docs/twilight/4/pages-components-home-custom-slider-headers-01.png)


### Settings

This component is a [custom component](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). Its configuration is described in the [twilight.json](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) as follows:

```json lineNumbers
{
  "version": ...,
  "theme_name": ...,
  "repo_url": ...,
  "support_url": ...,
  ...
  "components": [
    {
      "name": "slider-products-with-header",
      "title": "Slider products with header",
      "icon": "sicon-list-play",
      "path": "home.slider-products-with-header",
      "fields": [
        {
          "id": "background",
          "type": "string",
          "format": "image"
        },
        {
          "id": "title",
          "type": "string",
          "label": "Header title (optional)"
        },
        {
          "id": "description",
          "type": "string",
          "format": "textarea",
          "label": "Header sub title (optional)"
        },
        {
          "id": "products",
          "type": "items",
          "icon": "sicon-list",
          "label": "Prducts",
          "format": "dropdown-list",
          "required": true,
          "source": "Products",
          "multichoice": true,
          "maxLength": 8
        }
      ]
    }
  ]
  
}

```
#### Theme Preview 

The components can be managed using the theme preview in the [Theme menu item](https://salla.partners/themes) of Salla Partners Portal. The developer can edit the component and enable it in the theme preview dashboard.
![Theme Preview Dashboard](https://i.imgur.com/9L0TzeF.png)

### Variables

The variables of this component are fetched from the `twilight.json` file as per the merchant settings. They are located in the `components` section's fields. 


<DataSchema id="1383702" />


### Usage
This component starts with showing the headers:
```php lineNumbers
<div style="background-image: url('{{component.background}}');">
    <h3>{{component.title}}</h3>
    <p>{{component.description}}</p>
</div>
```

Then it loops through a list of products to display them within a _slider_:

```php lineNumbers
{% for product in component.products %}
    <div class="product-item">
        <a href="{{ product.url }}">
            <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
            {% if product.promotion_title %}
                {{ product.promotion_title }}
            {% endif %}
        </a>
        <h3>
            <a href="{{ product.url }}">{{ product.name }}</a>
        </h3>
        {% if product.on_sale %}
            <h4>{{ product.sale_price|money }}</h4>
            {{ product.regular_price|money }}
        {% else %}
            <h4>{{ product.price|money }}</h4>
        {% endif %}

        <salla-add-product-button product-id="{{ product.id }}"
                                  product-status="{{ product.status }}"
                                  product-type="{{ product.type }}">
        </salla-add-product-button>
    </div>
{% endfor %}
```



