The `<salla-conditional-fields>` web component allows for hiding / displaying certain features in a product, such as size, where it only works if the product has, for instance, a specific value for the color.

## Example

![Conditional Fields](https://cdn.salla.network/docs/twilight/6/js-web-conditional-fields-01.gif)

<Tabs>
  <Tab title="HTML">
      
```html
<salla-conditional-fields>
    {% hook 'product:single.form.start' %}
         // options available
    {% hook 'product:single.form.end' %}
</salla-conditional-fields>
```
  </Tab>
  
</Tabs>


