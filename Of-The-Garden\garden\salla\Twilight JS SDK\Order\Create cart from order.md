This endpoint simply creates a new cart list for the customer, which will include the same items as any previous order. When customers are signed in to their account, they have the option to easily place a repeated order with one click. As a result, the customer will be sent to the checkout page with a new cart list that includes the previous order's items.

## Payload `authenticated`

<DataSchema id="1427901" />


## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427902" />
      
  </Tab>
   <Tab title="Error">
       
<DataSchema id="1427184" />
  
    </Tab>
    </Tabs>


## Usage
To allow the customer to place the same order again, the developer may call the `createCartFromOrder()` method along with the `order_id`. 


```js
salla.order.createCartFromOrder({ id: 12345 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.order.createCartFromOrder(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onOrderCreated and onOrderCreationFailed events.

### onOrderCreated
This event is triggered when the request of placing the same order again is done without having any errors coming back from the backend.

```js
salla.event.order.onOrderCreated((response) => {
  console.log(response)
});
```
### onOrderCreationFailed
This event is triggered when the request of placing the same order again is not completed and an error has occurred.

```js
salla.event.order.onOrderCreationFailed((errorMessage) => {
  console.log(errorMessage)
});
