This logout endpoint terminates the current session. As a result, the customer's authentication will be terminated, and it will be required to obtain a new access token in order to make further API calls.

:::tip
The *logout* endpoint has been implemented in the [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


## Response

<Tabs>
 <Tab title="Success">
     
     
   <DataSchema id="1427188" />
     
</Tab>
   <Tab title="Error">
       
       
   <DataSchema id="1427189" />
       
   </Tab>
</Tabs>

## Usage
The method `logout()` can be called in order to process the logout request. It does not require any input.

```js
salla.auth.logout().then((response) => {
    /* add your code here */
});
```

## Events
The *logout* process may trigger two events during the login process, onLoggedOut and onFailedLogout.

### onLoggedOut
This event will be triggered when the *logout* process is completed successfully without errors.
```js
salla.event.auth.onLoggedOut((response) => {
  console.log(response)
});
```

### onFailedLogout
This event will be triggered when the *logout* process is not completed, and an error has occurred.

```js
salla.event.auth.onFailedLogout((errorMessage) => {
  console.log(errorMessage)
});
```