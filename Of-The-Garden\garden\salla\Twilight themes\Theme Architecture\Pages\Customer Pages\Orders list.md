This [`orders list page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/orders/index.twig) is designated for listing customers orders where customers can check their existing orders to view the status, price, order number and make changes where applicable. 

``` shell title = "🌐    Page URL: http://www.store-domain.com/orders"
└── src 
  ├── views
  |   ├── pages
  |   |   ├── customer
  |   |   |   ├── orders
  |   |   |   |     ...
  |   |   |   |   ├── index.twig
                    ...
```

### Example
<!--
focus: false
-->
![Orders list](https://cdn.salla.network/docs/twilight/4/customer-order-list-01.png)


### Variables

<DataSchema id="1383860" />

### Components
This page extends the default layout [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), and accordingly, it takes the unified look-and-feel. For example, all of the [`header's`](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and [`footer's`](https://docs.salla.dev/doc-422602?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) components will be  added automatically to this page.

In addition, the `User` model is accessible automatically on this page because it's included in the [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) layout file.

### JS Web Components
Customer Orders List page may include the following [JS Web Components](https://docs.salla.dev/doc-422556?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores.

- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Infinite Scroll [`<salla-infinite-scroll>`](https://docs.salla.dev/doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The [`orders list page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/orders/index.twig) calls for the following [hooks](https://docs.salla.dev/doc-422552) in order to inject extra information.

```php
{% hook 'customer:orders.index.items.start' %}
{% hook 'customer:orders.index.items.end' %}
```

### Usage
Mainly, in this page's template, the list of the customer's `orders` are displyed using a `for-loop` statement. This can be done easily by using the `orders` object as we see in the following example. Note that the Salla component salla [infinite scroll](https://docs.salla.dev/doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD) has been used to ease the pagination process.

```php lineNumbers=true
{% if orders|length %}
    <salla-infinite-scroll next-page="{{ orders.next_page }}" item=".customer-row">
        <table>
            <thead>
            <tr>
                <th scope="col">{{ trans('pages.thank_you.order_id') }}</th>
                <th scope="col">{{ trans('pages.orders.total') }}</th>
                <th scope="col">{{ trans('pages.orders.date') }}</th>
                <th scope="col">{{ trans('pages.orders.status') }}</th>
            </tr>
            </thead>
            <tbody>
            {% for order in orders %}
                <tr class="customer-row">
                    <td><span>{{ trans('pages.thank_you.order_id') }}:</span> #{{ order.reference_id }}</td>
                    <td><span>{{ trans('pages.orders.total') }}:</span> {{ order.total|money }}</td>
                    <td><span>{{ trans('pages.orders.date') }}:</span> {{ order.created_at|date("l j F Y") }}</td>
                    <td>
                        <span>{{ trans('pages.orders.status') }}:</span>
                        <span> {{ order.status.name }} </span>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </salla-infinite-scroll>

{% else %}
    <div>
        <span>{{ trans('pages.orders.non_orders') }}</span>
    </div>
{% endif %}
```