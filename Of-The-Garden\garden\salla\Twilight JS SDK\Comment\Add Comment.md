The customer can add their review to the merchant's store using the *add* endpoint. The comment can be about a specific product or a specific page.

## Payload `authenticated`

<DataSchema id="1387222" />


## Response

<Tabs>
  <Tab title="Success">
 
<DataSchema id="1427929" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />

  </Tab>
  
</Tabs>



## Usage
To add the customer's comment about a specific product or a specific page, the developer may call the `add` method.

```js
salla.comment.add({
    id: 85214,
    comment: "The product's price is good",
    type: "product",
  })
  .then((response) => {
    /* add your code here */
  });

```


## Events
This endpoint may trigger two events, the onAdded and onAdditionFailed events.

### onAdded
This event is triggered when adding a new comment by the customer is done without having any errors coming back from the backend.

```js
salla.event.comment.onAdded((response) => {
  console.log(response)
});
```
### onAdditionFailed
This event is triggered when when adding a new comment by the customer is not completed and an error has occurred.

```js
salla.event.comment.onAdditionFailed((errorMessage) => {
  console.log(errorMessage)
});
```