This [`single  blog page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/blog/single.twig) is to display the content of a single article from the store's blog. The content can be a mix of text and images. This is part of the internal marketing tool for the store owner to draw the customer's attention to specific products or ideas. Each article is displayed along with its tags, in the event of having tags. This template may also show any related articles to the current single article. 


**Following is the page location and url:**
``` shell title = "🌐 Page URL: http://www.store-domain.com/blog/c6576542"
└── src 
  ├── views
    ├── pages
    |   ├── blog
    |   |   ├── single.twig
    |   ...
    ...
```

### Example
<!--
focus: false
-->
![Sigle blog](https://cdn.salla.network/docs/twilight/4/blog-single-01.png)


### Variables



<DataSchema id="1383877" />


### Components
The category page includes the [Breadcrumbs](https://docs.salla.dev/doc-422601) component. Breadcrumbs are a set of links that indicate the current page and its "ancestors" leading back to the site's homepage.

```php lineNumbers=true
{% component 'header.breadcrumbs' %}
```

### Hooks
The `single  blog page template` may call the following [hooks](https://docs.salla.dev/doc-422552?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) in order to inject more information:

```php lineNumbers=true
{% hook 'blog:single.items.start' %}
{% hook 'blog:single.items.end' %}
```
### Usage
This page receives the object `article`, which contains the full content details of that single article. For example, `article.name`, `article.created_at`, `article.author.name`, and so on. The developer has complete control over how these elements can be displayed.

```php lineNumbers=true
<p>{{ article.title }}</p>
<p>{{ article.created_at|date }}</p>
<p>{{ article.author.name }}</p>

{% if article.has_image %} 
  <img src="{{ article.image.url }}" alt="{{ article.image.alt }}" />
{% endif %}

<p>{{ article.body|raw }}</p>
```

The variable `article.tags` can be used to display the article's attached tags. Below is an example of that.

```php lineNumbers=true
{% if article.tags|length %}
  {% for tag in article.tags %} 
    {{ tag.name }} 
  {% endfor %}
{% endif %}
```

In addition, the variable `article.related` retrieves an array of any related articles, which can be listed using a _for-loop_ statement.

```php lineNumbers=true
{% if related|length %}
    <h2>Related Articles</h2>

    {% for article in article.related %}
        {{ article.name }}
        {% if article.has_image %}
            <img src="{{ article.image.url }}" alt="{{ article.image.alt }}"/>
        {% else %}
            <img src="{{ asset('images/placeholder.png') }}" alt="placeholder">
        {% endif %}
        {{ article.created_at|date }}
        {{ article.title }}
    {% endfor %}
{% endif %}
```


