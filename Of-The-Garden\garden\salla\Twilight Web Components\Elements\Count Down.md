The `<salla-count-down>` web component is used to show the amount of time left until a given date. It's perfect for tracking important events, or counting down to special occasions like birthdays and anniversaries. It displays the days, hours, minutes, and seconds left with an easy-to-read countdown timer, as well as customized color, size, and labeled text properties.



## Example

<!--focus: false -->
![Count Down](https://cdn.salla.network/docs/twilight/6/js-web-countdown-01.png)

## Usage
<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Count Down component usage -->
<salla-count-down
  color="#baf2e5"
  boxed=true
  digits="en"
  size="md">
</salla-count-down>
```
  </Tab>
  <Tab title="SASS">
 
This JS web component can be targeted for styling by its `.s-count-down` class. Following is a complete source code for customizing this component:

```css
.s-count-down{
  &-wrapper{
    
  }
  &-boxed{

  }
  &-ended{
    
  }
  &-list{

  }
  &-item{

  }
  &-item{
    &-value{

    }
    &-label{

    }
  }
  &-end-text{

  }
}
```
     
  </Tab>
  
</Tabs>


## Properties

| Property | Attribute  | Description                                                                                                | Type                             | Default     |
| -------- | ---------- | ---------------------------------------------------------------------------------------------------------- | -------------------------------- | ----------- |
| Boxed    | `boxed`    | The count down numbers will appear in boxes, if the value is set to `true`. | `boolean`                        | `undefined` |
| Color    | `color`    | The color of the count down. | `"dark" \| "light" \| "primary"` | `'dark'`    |
| Date     | `date`     | The count down date format, which is the following: MMM DD, YYYY HH:mm:ss. (Example: Jan 2, 2023 16:37:52). | `string`                         | `undefined` |
| Digits   | `digits`   | The digits language format standard to show in the count down. | `"auto" \| "en"`                 | `'auto'`    |
| End of Day  | `end-text` | the count down will end at the end of the day, if value set to `true`. | `boolean`                         | `undefined` |
| End Text  | `end-text` | The text to show when the count down ends. | `string`                         | `undefined` |
| Labeled  | `labeled`  | Shows labels for each count down number. | `boolean`                        | `undefined` |
| Size     | `size`     | The count down size. | `"lg" \| "md" \| "sm"`           | `'md'`      |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `endCountDown` which ends the cound down set by the developer.


| Method           | Description         | Return Type     |
| ---------------- | ------------------- | --------------- |
| `endCountDown()` | Ends the count down. | `Promise<void>` |