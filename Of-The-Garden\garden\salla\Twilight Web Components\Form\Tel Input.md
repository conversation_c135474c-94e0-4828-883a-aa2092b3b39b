The `<salla-tel-input>` web component is used to show a field for entering a telephone number, with country key/code prefix, and that can be customized using the properties' parameters available.


## Example

<!--
focus: false
-->

![Tel Input Example](https://cdn.salla.network/docs/twilight/6/js-web-tel-input-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Accepting Valid Telephone Input-->
<salla-tel-input
  onclick="telInput.isValid()"
  country-code="sa"
  phone="555555555">
</salla-tel-input>

<!-- getting Inputted Values -->
<salla-button fill="outline" color="primary" onclick="telInput.getValues()">
  Get Values
</salla-button>
```     
  </Tab>
 <Tab title="JS">

```js
// Save reference to the Tel Input Component below
var telInput = document.querySelector("salla-tel-input");

// Listen for the telInput for enterClicked event
telInput.addEventListener("phoneEntered", function (phone) {
  console.log("phone readt!", phone);
});
```      
  </Tab>
    
<Tab title="SASS">

This JS web component can be targeted for styling by its `.s-tel-input` class. Following is a complete source code for customizing this component:

```js
.s-tel-input {
  // the input field
  &-control {

  }
  // field error message
  &-error-msg {

  }
}
```      
  </Tab>  
</Tabs>

<!-- Auto Generated Below -->

## Properties

| Property     | Attribute      | Description          | Type     | Default                                         |
| ------------ | -------------- | -------------------- | -------- | ----------------------------------------------- |
| Country Code | `country-code` | Current country code | `string` | `'salla.config.get('user.country_code', 'SA')'` |
| Name         | `name`         | Input name value     | `string` | ``                                         |
| Phone        | `phone`        | Current phone number | `string` | `undefined`                                     |

## Events

| Event          | Description                                                          | Type               |
| -------------- | -------------------------------------------------------------------- | ------------------ |
| `phoneEntered` | This event will be fired when the user enters the telephone input field | `CustomEvent<Phone>` |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, suh as `getValues` to fetch the inputted values, and `isValid` to determine whether or not the values are valid.


| Method        | Description                                       | Return Type                                             |
| ------------- | ------------------------------------------------- | ------------------------------------------------------- |
| `getValues()` | Gets current inputted values                      | `Promise<{[x: string]: string; countryCode: string; }>` |
| `isValid()`   | Shows the validity of the current inputted values | `Promise<boolean>`                                      |
