This endpoint adds a product to the customer's wishlist. A customer's wishlist is a collection of desired products saved to the customer's account, indicating interest but not an immediate intent to buy. 
 

## Payload `authenticated`


<DataSchema id="1387278" />


## Response
<Tabs>
  <Tab title="Success">


<DataSchema id="1427827" />
  </Tab>

  
</Tabs>


## Usage
To perform the action of adding a product to the customer's wishlist, the method `add()` may be used as follows.


```js
salla.wishlist.add({ id: 12345 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.wishlist.add(12345).then((response) => {
  /* add your code here */
});
```

## Events
This endpoint may trigger two events, the onAdded and onAdditionFailed events.

### onAdded
This event is triggered when adding a product to the customer's wishlist is done without having any errors coming back from the backend.

```js
salla.event.wishlist.onAdded((response) => {
  console.log(response)
});
```
### onAdditionFailed
This event is triggered when adding a product to the customer's wishlist is not completed and an error has occurred.

```js
salla.event.wishlist.onAdditionFailed((errorMessage) => {
  console.log(errorMessage)
});