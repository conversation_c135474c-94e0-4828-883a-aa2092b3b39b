This `resend` endpoint is simply to re-send the access code to the customer if it was not received correctly. The customer is given 30 seconds to enter the received access code, so if there was an issue with receiving the access code, another new code may be re-sent.

:::tip
The *resend* endpoint has been implemented in the [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


## Payload


<DataSchema id="1387189" />


## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427198" />

  </Tab>
   <Tab title="Error">


      
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>

## Usage

The `resend()` method passes a request for a new access code to the customer. 

<Tabs>
  <Tab title="Mobile">

The method may pass the phone number and country code to where the new access code should be sent.

```js
salla.auth.resend({
  type: 'mobile', 
  phone: '555555555', 
  country_code: 'SA'
}).then((response) => {
  /* add your code here */
});
```
      
  </Tab>
   <Tab title="Email">

On the other hand, when using an email as a *login* type, the developer can receive the data using the example below and redirect the user accordingly.

```js
salla.auth.resend({
  type: 'email', 
  email: '<EMAIL>'
}).then((response) => {
  /* add your code here */
});
```      
  </Tab>
  
</Tabs>


## Events
This endpoint may trigger two events, the onCodeSent and onCodeNotSent events.

### onCodeSent
This event is triggered when the new access code generation has been done without having any errors back from the backend.

```js
salla.event.auth.onCodeSent((response) => {
  console.log(response)
});
```
### onCodeNotSent
This event is triggered when the new access code generation has failed, and no new access code has been sent to the customer.

```js
salla.event.auth.onCodeNotSent((errorMessage) => {
  console.log(errorMessage)
});
```
