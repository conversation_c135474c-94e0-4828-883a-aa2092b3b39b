
The `<salla-comments>` web component allows the user to enter textual contents as a comment for a Product or Page.

## Example


![Comments image](https://cdn.salla.network/docs/twilight/6/js-web-comments-01.png)

## Usage


<Tabs>
  <Tab title="HTML">
```html
<salla-comments
  type="product"
  item-id="154684624"
  hide-form = false>
</salla-comments>
```
  </Tab>
  <Tab title="SASS">
This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
:host {
  display: block;
}
```
  </Tab>

</Tabs>

## Properties

| Property | Attribute | Description | Type	| Default |
| -------- | --------- | ----------- | ------- | ------- |
| Block Title          | `block-title`      | Comment Block's Title         | `string`                                  | `undefined`        |
| Hide Form | `hide-form` |This allows to make the comment section non visible| `boolean` | `undefined`|
| Item Id _(required)_ | `item-id`	| A unique identifier for Page or product | `number` | `undefined`|
| Load More Text | `load-more-text` | This item allows loading additional text. | `string` | `undefined`|
| Show Form Avatar      | `show-form-avatar` | Whether to show or hide the commenter’s avatar | `boolean`                                 | `false`            |
| Type | `type`| This item is used to identify the Comment type | `CommentType.BLOG` \| `CommentType.PAGE` \| `CommentType.PRODUCT`  |
