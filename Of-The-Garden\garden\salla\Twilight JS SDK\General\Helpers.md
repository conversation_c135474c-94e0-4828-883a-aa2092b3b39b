The Twilight JS SDK is packaged with a variety of helpful functions that may be accessed and used straight within Templates. Previous articles in this documenation introduced [Twilight Flavoured Twig](https://docs.salla.dev/doc-421929?nav=01HNFTDZPB31Y2E120R84YXKCX#helpers). In this aricle we will go thourgh how to use the Helper functions using the Twilight SDK JS.

## 📙 What you'll learn about

- [Url Helper](#url-helper)
  - [Asset Helper](#asset-helper)
  - [CDN Helper](#cdn-helper)
  - [is_page Helper](#is_page-Helper)
- [Money Helper](#money-helper)
- [Number Helper](#number-helper)
- [Apple Pay Helper](#apple-pay-helper)
- [is I frame](#is-i-frame)
- [is Preview](#is-preview)


## Url Helper
In general, the `sall.url.get()` helper function will return the absolute URL for the provided route, including the scheme and host.

```js
salla.url.get(…)
```

### Asset Helper
The `salla.url.asset()` function returns the public path of the specified asset (which can be a CSS file, a JavaScript file, an image path, etc.). This function looks at where the theme was installed (for example, if the theme is accessed in a subdirectory of the host) and, if specified, the asset package base path. It needs to be supplied with the required file's path.

```js
salla.url.asset(filePath)
```


### CDN Helper
A content delivery network (CDN) referes to an external resource that can be used inside the theme. The The `salla.url.cdn()` function helps with doing that by passing the path of that external resource.

```js
salla.url.cdn(filePath)
```


### is_page Helper

This helper function determines whether if the passed route is a path, by using page slug

```js
if(salla.url.is_page('index')) {
  console.log('you arrive 💪');
}

// other exmaple
if(salla.url.is_page('product.index')) {
  console.log('you arrive product index 💪');
}
```


:::info
 You can find the page slug via views by `{{ page.slug }}`, or via browser console by`salla.config.get('page.slug')`
:::


## Money Helper

The *Money Helper* is another function provided by the **Twilight JS SDK** that allows the developer to format a numeric value as a currency. The syntax for using this helper  is `salla.money(value)`, where value is the numeric value that you want to format as per the default currency.

For example, if you want to format the number 100 as a currency, you can use the following JavaScript code:
```js
const moneyValue = salla.money(100); // formats 100 as per the selected currency
console.log(moneyValue); // output: "SAR 100"
```

If the Arabic numbers are enabled by the merchant the will be as follows:
```js
console.log(moneyValue); // output: "١٠٠ ر.س"
```

You can also pass additional parameters to the salla.money() helper to customize the currency and locale settings, such as:
```js
salla.money({amount:100, currency:"USD"}); // output: "USD100"
```

## Number Helper
This helper formats any number given to an Arabic number if merchant enables it and the current language is Arabic. It will also parse decimal number representation to a decimal point in Arabic (,):
```js
const numValue = salla.helpers.number(966567891011); 
console.log(numValue); // output: "٩٦٦٥٦٧٨٩١٠١١"
```

## Apple Pay Helper 
This helper allows checking multiple factors which are:
- Is apple pay enabled in this store?
- Does this browser support Apple pay?

The helper will return a value of `true` if Apple Pay is enabled, or `false` if it is not enabled.

```js
var applePayEnabled = salla.helpers.hasApplePay();
if (applePayEnabled) {
    console.log("Apple Pay is enabled");
} else {
    console.log("Apple Pay is not enabled");
}
```


## Is iframe 
The helper function `isIframe()` determines whether a web page is being displayed within an iframe rather than a window. This function returns either true or false based on this condition.
```js
function isIframe(){
return window.self !== window.top
}
```
## Is Preview
`isPreview()` helper function is a wrapper for the above explained `isIframe()` helper function.

```js
const isCustomizingTheme = salla.helpers.isPreview();
console.log(isCustomizingTheme); // prints true or false
```