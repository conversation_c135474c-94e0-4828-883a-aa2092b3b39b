This endpoint is used to exchange a customer's accumulated points for any preset reward, such as free goods or discounts.

:::tip
The *exchange* endpoint has been implemented in the [Loyalty](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component , and It's all setup to save developer's time and effort.
:::


## Payload `authenticated`

<DataSchema id="1387244" />

## Response

<Tabs>
  <Tab title="Success">
 
<DataSchema id="1387243" />
  
  </Tab>
   <Tab title="Error">


<DataSchema id="1427184" />
       
  </Tab>
  
</Tabs>



## Usage
To perform the action of exchanging a customer's accumulated points for any preset reward, the method `exchange()` may be called as below:

```js
salla.loyalty.exchange({ prize_id: 978, cart_id:123 }).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onExchangeSucceeded and onExchangeFailed events.

### onExchangeSucceeded
This event is triggered when performing the action of exchanging a customer's accumulated points for any preset reward is done without having any errors coming back from the backend.

```js
salla.event.rating.onExchangeSucceeded((response) => {
  console.log(response)
});
```
### onExchangeFailed
This event is triggered when performing the action of exchanging a customer's accumulated points for any preset reward is not completed and an error has occurred.

```js
salla.event.rating.onExchangeFailed((errorMessage) => {
  console.log(errorMessage)
});