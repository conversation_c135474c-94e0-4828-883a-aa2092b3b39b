The `<salla-installment>` web component is used to show a block area for the available installment payment options provided for a specific product. It consists of the supported payment in installments' options in an inline manner, and that can be customized using the properties' parameters available.

## Example

<!--
focus: false
-->

![Installment Example](https://cdn.salla.network/docs/twilight/6/js-web-instalment-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">
    
  ```js
<salla-installment price="1200">
</salla-installment>
```

      
  </Tab>
  <Tab title="SASS">

```css
#tabbyPromoWrapper {
  background: white;
  border-radius: 0.375rem;
  transition: box-shadow 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 20px;

  .salla-y &{
    border: 1px solid var(--color-grey-dark);
    border-radius: 12px
  }

  &:hover{
    box-shadow: 0 0 #0000, 0 0 #0000, 5px 10px 30px #2B2D340D;
  }

  #tabbyPromo {
    * {
      font-family: var(--font-main);
    }

    > div > div {
      max-width: none;
      //padding: 15px;
      box-shadow: none;
      border: none;
    }

    .tabby-promo-snippet {
      max-width: 100%;
      min-height: 100px;
      padding: 18px 20px;
      // border-color: var(--color-grey-dark);
      border: none !important;


      &__text, &__link {
        font-size: var(--font-sm);
        color: var(--color-text) !important;
      }

      &__link {
        font-weight: bold;
      }
    }
  }
}

.tabby-promo-wrapper {
  #tabby-promo {
    font-family: var(--font-main) !important;

    .tabby-promo {
      &__feature-title {
        font-size: var(--font-md);
      }

      &__feature-desc {
        font-size: var(--font-sm);
        line-height: 20px;
      }
    }
  }
}

/*
* Installment::Tamara & Spotii
*/
.tamara-product-widget{
  margin-bottom: 20px;
}

.tamara-product-widget,
.spotii-wrapper {
  min-height: 100px;
  position: relative;
  color: var(--color-text);
  font-size: var(--font-sm);
  line-height: 1.25;
  padding: 18px 20px 18px 100px !important;
  background: white;
  border-radius: 0.375rem;
  transition: box-shadow 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  .salla-y &{
    border-radius: 12px;
    border: 1px solid var(--color-grey-dark);
  }

  &:hover{
    box-shadow: 0 0 #0000, 0 0 #0000, 5px 10px 30px #2B2D340D;
  }
  .spotii-logo {
    float: left;
    margin: 0 0 0 -75px;
  }
}
.ltr .tamara-product-widget,
.ltr .spotii-wrapper{
  text-align: left;
  padding: 18px 100px 18px 20px !important;
  .spotii-logo {
    float: right;
    margin: 0 -75px 0 0;
  }
  .spotii-product-widget {
    text-align: left !important;
  }
}

.spotii-wrapper {
  margin-bottom: 20px;
  .spotii-promo {
    font-size: var(--font-md);
  }

  .spotii-product-widget {
    font-size: var(--font-sm) !important;
    margin-top: 10px;
  }
}

.tamara-product-widget{
  .tamara-logo {
    position: absolute;
    left: 20px;
    top: 18px;
    margin: 0 !important;

    .ltr &{
      right: 20px;
      left: auto;
    }
  }

  span {
    font-family: var(--font-main);
    font-size: var(--font-sm);
    color: var(--color-text);

    &:last-child {
      display: block;
      position: relative;
      margin-top: 8px;
    }
  }
}

.tamara-popup {
  &__wrap {
    overflow: auto !important;
  }
}
```
      
  </Tab>  
</Tabs>


## Properties

| Property   | Attribute  | Description           | Type     | Default                                  |
| ---------- | ---------- | --------------------- | -------- | ---------------------------------------- |
| Country  | `country`  | Country code identifier          | `string` | `salla.config.get('user.country_code')`  |
| Currency | `currency` | Installment price currency         | `string` | `'salla.config.get('user.currency_code')'` |
| Language | `language` | Current installment language         | `string` | `'salla.config.get('user.language_code')'` |
| Price    | `price`    | Current product price | `string` | `undefined`                              |

## Settings

This component relay on the store settings in twilight config, if you are using thie comonent outside store pages, you can set the available installment payment options via `salla.config` 

```js
salla.config.set('store.settings.installments', {
  tamara_installment: true,
  tabby_installment: true,
  spotii_pay: true
})
```

:::info[Information]
Because the installment method is based on third-party components, there are **no custom classes** - _like there are for other components_ - to style such classes.
:::
