This endpoint is used to add a product that has been gifted to the shopping cart, after which the customer can be redirected to the shopping cart page in order to complete the purchase of that product as a solo.

:::tip
The *add gift to cart* endpoint has been implemented in the [Gifting](https://docs.salla.dev/doc-422705?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::

## Payload `authenticated`

<DataSchema id="1427536" />


## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427537" />
      
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of adding a product that has been gifted to the shopping cart, the developer may call the method `addGiftToCart` as follows.


```js
salla.product.addGiftToCart({
  product_id: 258741,
  payload: Object,
  withRedirect: false
}).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onAddGiftToCartSucceeded and onAddGiftToCartFailed events.

### onAddGiftToCartSucceeded
This event is triggered when the action of adding a product that has been gifted to the shopping cart is done without having any errors coming back from the backend.

```js
salla.product.event.onAddGiftToCartSucceeded((response) => {
  console.log(response)
});
```
### onAddGiftToCartFailed
This event is triggered when the action of adding a product that has been gifted to the shopping cart is not completed and an error has occurred.

```js
salla.product.event.onAddGiftToCartFailed((errorMessage) => {
  console.log(errorMessage)
});

