(()=>{"use strict";function t(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function i(t){var i=function(t){if("object"!=n(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var o=i.call(t,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==n(i)?i:i+""}function o(t,n){for(var o=0;o<n.length;o++){var a=n[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,i(a.key),a)}}function a(t,n,i){return n&&o(t.prototype,n),i&&o(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function e(t){return e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},e(t)}function s(t,n){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},s(t,n)}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}function r(t){var n="function"==typeof Map?new Map:void 0;return r=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(n){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(t))return n.get(t);n.set(t,i)}function i(){return function(t,n,i){if(c())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,n);var a=new(t.bind.apply(t,o));return i&&s(a,i.prototype),a}(t,arguments,e(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),s(i,t)},r(t)}function l(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(l=function(){return!!t})()}(function(){return a((function n(){t(this,n)}),[{key:"onReady",value:function(){}},{key:"registerEvents",value:function(){}},{key:"initiate",value:function(t){if(t&&!t.includes(salla.config.get("page.slug")))return app.log("The Class For (".concat(t.join(","),") Skipped."));this.onReady(),this.registerEvents(),app.log("The Class For (".concat((null==t?void 0:t.join(","))||"*",") Loaded🎉"))}}])}()).initiateWhenReady=function(){var t,n=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;"ready"===(null===(t=window.app)||void 0===t?void 0:t.status)?(new this).initiate(i):document.addEventListener("theme::ready",(function(){return(new n).initiate(i)}))};var d=function(i){function o(){return t(this,o),i=this,a=e(a=o),function(t,i){if(i&&("object"==n(i)||"function"==typeof i))return i;if(void 0!==i)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(i,l()?Reflect.construct(a,[],e(i).constructor):a.apply(i,s));var i,a,s}return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),n&&s(t,n)}(o,i),a(o,[{key:"connectedCallback",value:function(){var t,n=this;this.product=this.product||JSON.parse(this.getAttribute("product")),"ready"===(null===(t=window.app)||void 0===t?void 0:t.status)?this.onReady():document.addEventListener("theme::ready",(function(){return n.onReady()}))}},{key:"onReady",value:function(){var t=this;this.fitImageHeight=salla.config.get("store.settings.product.fit_type"),this.placeholder=salla.url.asset(salla.config.get("theme.settings.placeholder")),this.getProps(),this.source=salla.config.get("page.slug"),"landing-page"==this.source&&(this.hideAddBtn=!0,this.showQuantity=window.showQuantity),salla.lang.onLoaded((function(){t.remained=salla.lang.get("pages.products.remained"),t.donationAmount=salla.lang.get("pages.products.donation_amount"),t.startingPrice=salla.lang.get("pages.products.starting_price"),t.addToCart=salla.lang.get("pages.cart.add_to_cart"),t.outOfStock=salla.lang.get("pages.products.out_of_stock"),t.render()})),this.render()}},{key:"initCircleBar",value:function(){var t=100-this.product.quantity/(this.product.quantity>100?2*this.product.quantity:100)*100;this.querySelector(".s-product-card-content-pie-svg-bar").style.strokeDashoffset=t}},{key:"formatDate",value:function(t){var n=new Date(t);return"".concat(n.getFullYear(),"-").concat(n.getMonth()+1,"-").concat(n.getDate())}},{key:"getProductBadge",value:function(){var t,n,i;return this.product.promotion_title?'<div class="s-product-card-promotion-title">'.concat(this.product.promotion_title,"</div>"):this.showQuantity&&null!==(t=this.product)&&void 0!==t&&t.quantity?'<div\n        class="s-product-card-quantity">'.concat(this.remained," ").concat(salla.helpers.number(null===(i=this.product)||void 0===i?void 0:i.quantity),"</div>"):this.showQuantity&&null!==(n=this.product)&&void 0!==n&&n.is_out_of_stock?'<div class="s-product-card-out-badge">'.concat(this.outOfStock,"</div>"):""}},{key:"getPriceFormat",value:function(t){return t&&0!=t?salla.money(t):salla.config.get("store.settings.product.show_price_as_dash")?"-":""}},{key:"getProductPrice",value:function(){var t,n="";if(this.product.is_on_sale)n='<div class="s-product-card-sale-price">\n                <h4>'.concat(this.getPriceFormat(this.product.sale_price),"</h4>\n                <span>").concat(this.getPriceFormat(null===(t=this.product)||void 0===t?void 0:t.regular_price),"</span>\n              </div>");else if(this.product.starting_price){var i;n='<div class="s-product-card-starting-price">\n                  <p>'.concat(this.startingPrice,"</p>\n                  <h4> ").concat(this.getPriceFormat(null===(i=this.product)||void 0===i?void 0:i.starting_price)," </h4>\n              </div>")}else{var o;n='<h4 class="s-product-card-price">'.concat(this.getPriceFormat(null===(o=this.product)||void 0===o?void 0:o.price),"</h4>")}return n}},{key:"getAddButtonLabel",value:function(){return"sale"===this.product.status&&"booking"===this.product.type?salla.lang.get("pages.cart.book_now"):"sale"===this.product.status?salla.lang.get("pages.cart.add_to_cart"):"donating"!==this.product.type?salla.lang.get("pages.products.out_of_stock"):salla.lang.get("pages.products.donation_exceed")}},{key:"getProps",value:function(){this.horizontal=this.hasAttribute("horizontal"),this.shadowOnHover=this.hasAttribute("shadowOnHover"),this.hideAddBtn=this.hasAttribute("hideAddBtn"),this.fullImage=this.hasAttribute("fullImage"),this.minimal=this.hasAttribute("minimal"),this.isSpecial=this.hasAttribute("isSpecial"),this.showQuantity=this.hasAttribute("showQuantity")}},{key:"render",value:function(){var t,n,i,o,a,e,s,c,r,l,d,u,p,h,f,g,v,m,y,b,w,_=this;this.classList.add("s-product-card-entry"),this.setAttribute("id",this.product.id),!(this.horizontal||this.fullImage||this.minimal)&&this.classList.add("s-product-card-vertical"),this.horizontal&&!this.fullImage&&!this.minimal&&this.classList.add("s-product-card-horizontal"),this.fitImageHeight&&!this.isSpecial&&!this.fullImage&&!this.minimal&&this.classList.add("s-product-card-fit-height"),this.isSpecial&&this.classList.add("s-product-card-special"),this.fullImage&&this.classList.add("s-product-card-full-image"),this.minimal&&this.classList.add("s-product-card-minimal"),null!==(t=this.product)&&void 0!==t&&t.donation&&this.classList.add("s-product-card-donation"),this.shadowOnHover&&this.classList.add("s-product-card-shadow"),null!==(n=this.product)&&void 0!==n&&n.is_out_of_stock&&this.classList.add("s-product-card-out-of-stock"),this.isInWishlist=!salla.config.isGuest()&&salla.storage.get("salla::wishlist",[]).includes(this.product.id),this.innerHTML='\n        <div class="'.concat(this.fullImage?"s-product-card-image-full":"s-product-card-image",'">\n          <a href="').concat(null===(i=this.product)||void 0===i?void 0:i.url,'">\n            <img class="s-product-card-image-').concat(salla.url.is_placeholder(null===(o=this.product)||void 0===o||null===(o=o.image)||void 0===o?void 0:o.url)?"contain":this.fitImageHeight?this.fitImageHeight:"cover",' lazy"\n              src=').concat(this.placeholder,"\n              alt=").concat(null===(a=this.product)||void 0===a||null===(a=a.image)||void 0===a?void 0:a.alt,"\n              data-src=").concat((null===(e=this.product)||void 0===e||null===(e=e.image)||void 0===e?void 0:e.url)||(null===(s=this.product)||void 0===s?void 0:s.thumbnail),"\n            />\n            ").concat(this.fullImage||this.minimal?"":this.getProductBadge(),"\n          </a>\n          ").concat(this.fullImage?'<a href="'.concat(null===(c=this.product)||void 0===c?void 0:c.url,'" aria-label=').concat(this.product.name,' class="s-product-card-overlay"></a>'):"","\n          ").concat(this.horizontal||this.fullImage?"":'<salla-button\n              shape="icon"\n              fill="outline"\n              color="light"\n              name="product-name-'.concat(this.product.id,'"\n              aria-label="Add or remove to wishlist"\n              class="s-product-card-wishlist-btn animated ').concat(this.isInWishlist?"s-product-card-wishlist-added pulse-anime":"not-added un-favorited",'"\n              onclick="salla.wishlist.toggle(').concat(this.product.id,')"\n              data-id="').concat(this.product.id,'">\n              <i class="sicon-heart"></i>\n            </salla-button>'),'\n        </div>\n        <div class="s-product-card-content">\n          ').concat(this.isSpecial&&null!==(r=this.product)&&void 0!==r&&r.quantity?'<div class="s-product-card-content-pie">\n              <span>\n                <b>'.concat(salla.helpers.number(null===(l=this.product)||void 0===l?void 0:l.quantity),"</b>\n                ").concat(this.remained,'\n              </span>\n              <svg xmlns="http://www.w3.org/2000/svg" viewBox="-2 -1 36 34" class="s-product-card-content-pie-svg">\n                <circle cx="16" cy="16" r="15.9155" class="s-product-card-content-pie-svg-base" />\n                <circle cx="16" cy="16" r="15.9155" class="s-product-card-content-pie-svg-bar" />\n              </svg>\n            </div>'):"",'\n\n          <div class="s-product-card-content-main ').concat(this.isSpecial?"s-product-card-content-extra-padding":"",'">\n            <h3 class="s-product-card-content-title">\n              <a href="').concat(null===(d=this.product)||void 0===d?void 0:d.url,'">').concat(null===(u=this.product)||void 0===u?void 0:u.name,"</a>\n            </h3>\n\n            ").concat(null!==(p=this.product)&&void 0!==p&&p.subtitle&&!this.minimal?'<p class="s-product-card-content-subtitle opacity-80">'.concat(null===(h=this.product)||void 0===h?void 0:h.subtitle,"</p>"):"","\n          </div>\n          ").concat(null===(f=this.product)||void 0===f||!f.donation||this.minimal||this.fullImage?"":"<salla-progress-bar donation=".concat(JSON.stringify(null===(g=this.product)||void 0===g?void 0:g.donation),'></salla-progress-bar>\n          <div class="s-product-card-donation-input">\n            ').concat(null!==(v=this.product)&&void 0!==v&&null!==(v=v.donation)&&void 0!==v&&v.can_donate?'<label for="donation-amount-'.concat(this.product.id,'">').concat(this.donationAmount,' <span>*</span></label>\n              <input\n                type="text"\n                onInput="').concat((function(t){salla.helpers.inputDigitsOnly(t.target),_.addBtn.donatingAmount=t.target.value}),'"\n                id="donation-amount-').concat(this.product.id,'"\n                name="donating_amount"\n                class="s-form-control"\n                placeholder="').concat(this.donationAmount,'" />'):"","\n          </div>"),'\n          <div class="s-product-card-content-sub ').concat(this.isSpecial?"s-product-card-content-extra-padding":"",'">\n            ').concat(null!==(m=this.product)&&void 0!==m&&null!==(m=m.donation)&&void 0!==m&&m.can_donate?"":this.getProductPrice(),"\n            ").concat(null!==(y=this.product)&&void 0!==y&&null!==(y=y.rating)&&void 0!==y&&y.stars?'<div class="s-product-card-rating">\n                <i class="sicon-star2 before:text-orange-300"></i>\n                <span>'.concat(this.product.rating.stars,"</span>\n              </div>"):"","\n          </div>\n\n          ").concat(this.isSpecial&&this.product.discount_ends?'<salla-count-down date="'.concat(this.formatDate(this.product.discount_ends),'" end-of-day=',!0," boxed=",!0,"\n              labeled=",!0," />"):"","\n\n\n          ").concat(this.hideAddBtn?"":'<div class="s-product-card-content-footer gap-2">\n              <salla-add-product-button fill="outline" width="wide"\n                product-id="'.concat(this.product.id,'"\n                product-status="').concat(this.product.status,'"\n                product-type="').concat(this.product.type,'">\n                ').concat("sale"==this.product.status?'<i class="text-base sicon-'.concat("booking"==this.product.type?"calendar-time":"shopping-bag",'"></i>'):"","\n                <span>").concat(this.product.add_to_cart_label?this.product.add_to_cart_label:this.getAddButtonLabel(),"</span>\n              </salla-add-product-button>\n\n              ").concat(this.horizontal||this.fullImage?'<salla-button \n                  shape="icon" \n                  fill="outline" \n                  color="light" \n                  id="card-wishlist-btn-'.concat(this.product.id,'-horizontal"\n                  aria-label="Add or remove to wishlist"\n                  class="s-product-card-wishlist-btn animated ').concat(this.isInWishlist?"s-product-card-wishlist-added pulse-anime":"not-added un-favorited",'"\n                  onclick="salla.wishlist.toggle(').concat(this.product.id,')"\n                  data-id="').concat(this.product.id,'">\n                  <i class="sicon-heart"></i> \n                </salla-button>'):"","\n            </div>"),"\n        </div>\n      "),this.querySelectorAll('[name="donating_amount"]').forEach((function(t){t.addEventListener("input",(function(t){t.target.closest(".s-product-card-content").querySelector("salla-add-product-button").setAttribute("donating-amount",t.target.value)}))})),null===(b=document.lazyLoadInstance)||void 0===b||b.update(this.querySelectorAll(".lazy")),null!==(w=this.product)&&void 0!==w&&w.quantity&&this.isSpecial&&this.initCircleBar()}}])}(r(HTMLElement));customElements.define("custom-salla-product-card",d)})();