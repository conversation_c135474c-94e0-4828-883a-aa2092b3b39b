A Merchant's brand identity is greatly influenced by its Theme, which acts as both its interactive and visual public face. The Theme should have a clear direction, strong design elements, and flexibility so that Merchants have total control over the way their brands are perceived.\n\nThis balance guarantees that the Theme skillfully handles important elements like layout, typography, art direction, consistency, and user experience (UX) for the customer.\n\nAll Themes are mandated to support the following features:\n\n### 1.0. Theme Header and Footer \n\nThese components are used for the Store header and footer and in this section you can show how the developed Theme displays the elements.\n\n![Theme Header](https://cdn.salla.network/docs/twilight/1/uiux-review-01.png)\n\n### 2.0. Theme Custom Design for Product Card \n\nThis component shows how the Theme displays the product card. It can demonstrate different modes, like night and day mode, or how the product card looks when an item is out of stock. It helps the Merchant see how the Theme interacts with users in different situations.\n\n![Product Card](https://cdn.salla.network/docs/twilight/1/uiux-review-02.png)\n\n### 3.0. Theme Home Components\n\nThe [Home Page components](https://docs.salla.dev/doc-422556?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) section enables you to demonstrate how the Theme showcases the main page components with utmost clarity and visual appeal. \n\nAs a developer you can be creative in building as much components as you like, but here is list of all components that needs to be built in you Theme by your style: \n\n- [ **3.1.** Store Features](https://docs.salla.dev/doc-422587?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nThis **_pre-defined component_** is responsible for showcasing the store features such as payment methods, shipping methods and so on.\n\n![Home Components](https://cdn.salla.network/docs/twilight/1/uiux-review-03.png)\n\n\n- **3.2.** Flash Discount Component\n\nThis components renders the products that have an offer with a near expiry date\n\n![Flash Discount](https://cdn.salla.network/docs/twilight/1/uiux-review-04.png)\n\n-  [ **3.3.** Fixed Products](https://docs.salla.dev/doc-422589?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nUse this **_pre-defined component_** to display a group of products that has no scrolling effect. Their location is fixed.\n\n![Fixed Product](https://cdn.salla.network/docs/twilight/1/uiux-review-05.png)\n\n\n-  [**3.4.** Slider Products ](https://docs.salla.dev/doc-422598?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nThis component comes with many attractive elements to display products for the customers. It has Headings to describe the sections and a swiper slider to list as much products as possible.\n\n![Slider Product](https://cdn.salla.network/docs/twilight/1/uiux-review-06.png)\n\n\n-  [ **3.5.** Featured Products](https://docs.salla.dev/doc-422592?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nFeatured products list is a collection created in a specific design to draw customers' attention to see a certain collection of products primarily. This component is a **_pre-defined component_** Twilight comes with three pre-styled featured products components, and this is the _style-2_ component. This style is mixd of `tabbed` and `slider` views.\n\n\n![Featured Product](https://cdn.salla.network/docs/twilight/1/uiux-review-07.png)\n\n-  [ **3.6.** Testimonials](https://docs.salla.dev/doc-422584?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nThis **_pre-defined component_** displays testimonials, which are feedback given by customers. The display order is set as per newest.\n\n![Testimonials](https://cdn.salla.network/docs/twilight/1/uiux-review-08.png)\n\n\n- [ **3.7.** Brands](https://docs.salla.dev/doc-422594?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nThe store brands' logos are displayed using this component. Developers may use any design to style it because this is a significant section to draw the customers' attention.\n\n![Brands](https://cdn.salla.network/docs/twilight/1/uiux-review-09.png)\n\n\n-  [**3.8.** Blog ](https://docs.salla.dev/doc-422567?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nThe[ blog listing page template](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/blog/index.twig) is used for rendering the list of all of the available blogs' articles. This template will show an excerpt for each blog article along with the article title, summary, image, and author name. The developer has complete control over the appearance of this page.\n\n![Blog](https://cdn.salla.network/docs/twilight/1/uiux-review-10.png)\n\n\n-  [**3.9.** Youtube](https://docs.salla.dev/422582m0?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nThis **_pre-defined component_** is responsible for displaying Youtube videos that the developer preselects.\n\n![YouTube](https://cdn.salla.network/docs/twilight/1/uiux-review-11.png)\n\n\n- [**3.10.** Banners](https://docs.salla.dev/doc-422583?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)\n\nA fixed banner is a **_pre-defined component_** which is in charge of displaying a banner that is fixated on the Home Page.\n\n\n![Banners](https://cdn.salla.network/docs/twilight/1/uiux-review-12.png)\n\n\n## 4.0. Theme Page Requirements Review\n\nThe Theme contains pages to display the Store information, each page should contain a list of requirements. The following list illustrates each page and its’ requirement:\n\n\n### 4.1. Product Page\n\nThe Product page must contain the following product information:\n\n- **4.1.1.** Title of product (not abbreviated)\n- **4.1.2.** Cost of the product\n- **4.1.3.** Product.description: Every product photo has to be seen and presented. The arrangement shouldn't be broken by varying picture ratios.\n- **4.1.4.** When the corresponding variation is chosen, variant pictures have to be displayed.\n- **4.1.5.** Product pages have to utilise carts.When a shop uses tax-inclusive pricing, taxes included will show as an indicator that taxes are included in the price.\n- **4.1.6.** The capacity to choose a value.\n- **4.1.7.** An \"Add to cart\" button, which is frequently inactivated or changed when a sold-out or unavailable variation combination is chosen.\n- **4.1.8.** A callback function that updates the sold-out, compare-at-price, and price messages for the version that is presently chosen.\n- **4.1.9.** On a page, the first variation that is accessible loads.\n- **4.1.10.** Product endorsements\n- **4.1.11.** Dynamic checkout buttons, which by default need to be activated\n- **4.1.12.** Social Media Sharing: Sharing\n- **4.1.13.** Social Media Sharing: A product page should provide sharing options, such as a direct link, Twitter account, or WhatsApp account.\n- **4.1.14.** Allow the usage of alternative pictures by Themes so that retailers may link an image to a different version of a product.\n- **4.1.15.** Rich product media: Enhance a Theme's product page with rich product media, such YouTube videos, embedded videos, and 3D models.\n\n- **4.1.16.** Requirements for the collection page:\n  Features of the object collection that need to be output:\n    - **a.** Title of collection (not abbreviated)\n    - **b.** Description of the collection\n    - **c.** Picture of the collection\n- **4.1.17.** The following characteristics of the product object output must be included in the products when they are shown in a grid or list:\n    - **a.** Product title (links to product.url and is not truncated)\n    - **b.** Cost of the product\n    - **c.** Pictures of products\n    - **d.** A minimum of one product-related media item\n    - **e.** Product photos with different aspect ratios must not cause the product grid to collapse.\n    - **f.** When applicable, the Sale badge is shown.\n    - **g.** If there are no goods in a collection, an error message must appear.\n\n\n### 4.2. Cart Page Requirements\n\n#### 4.2.1. Cart Page must display details of the items object, including:\n\n- **a.** Title\n- **b.** Unit price\n- **c.** Image\n- **d.** Final price\n- **e.** Quantity\n- **f.** The cart total_price must be visible.\n- **g.** The cart page must use cart.taxes included to display an indication that taxes are included in the price when a store is using tax-inclusive prices.\n- **h.** Must include a checkout button that submits the cart form.\n- **i.** Must refresh all line items when the quantity is updated to ensure the total updates correctly.\n- **j.** Must provide the ability to change the quantity of each line item.\n\n#### 4.2.2. The Cart Page must support the following features:\n\n- **a.** Cart notes\n- **b.** Automatic discount codes\n- **c.** Dynamic checkout buttons\n\n\n### 5.0 Search Page Requirements\n\nSearch page of the Theme should have the following:\n\n- **5.1.** Must return a message if there are no search results.\n- **5.2.** Must have the ability to return different object types (products, blogs, pages). The object_type must be used when displaying\n- **5.3.** 404 page requirements\n- **5.4.** Must have a clear message stating that the page wasn't found.\n- **5.5.** Must have options for how to proceed, such as a search bar or a link to the homepage.\n\n### 6.0. Customer Page Requirements\n\nCustomer page of the Theme should include the following:\n\n- **a.** First name\n- **b.** Last name\n- **c.** Birthdate\n- **d.** Gender\n- **e.** Delete account\n- **f.** Email\n- **j.** Mobile number\n- **k.** Marketing email option\n