

The `<salla-advertisement>` web component allows you to displays Advertisement items such as icons, URLs, target, description, with the ability to edit the Advertisement background and text color.## Example

<!--
focus: false
-->

![Alt text](https://cdn.salla.network/docs/twilight/6/js-web-advertisement-01.png)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<salla-advertisement slot="icon"></salla-advertisement>
```
</Tab>
  <Tab title="SASS">

This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```css
:host {
  display: block;
}
```
  </Tab>
</Tabs>


## Slots
The`slots` makes it customizable to modify certain labels, such as `adv`.

| Slot   | Description |
| ------ | ----------- |
| `adv` | Replaces the advertisment component with any of the following slots: `{icon}`, `{url}`, `{target}`, `{description}`, `{bg_color}`, and `{text_color}`.    	|
