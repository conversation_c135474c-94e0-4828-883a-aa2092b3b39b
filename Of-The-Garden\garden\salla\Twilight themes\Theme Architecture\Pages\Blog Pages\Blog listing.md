The [`blog listing page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/blog/index.twig) is used for rendering the list of all of the available blogs' articles. This template will show an excerpt for each blog article along with the article title, summary, image, and author name. The developer has complete control over the appearance of this page.


**Following is the page location and url:**

```shell title="🌐 Page URL: http://www.store-domain.com/blog"
└── src 
  ├── views
    ├── pages
    |   ├── blog
    |   |   ├── index.twig
    |   ...
    ...
```

### Example
<!--
focus: false
-->
![Blog listing](https://cdn.salla.network/docs/twilight/4/blog-listing-01.png)


### Variables


<DataSchema id="1383856" />


### Components
The blog page includes the [Breadcrumbs](doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) component. Breadcrumbs are a set of links that indicate the current page and its "ancestors" leading back to the site's homepage.

```php lineNumbers=true
{% component 'header.breadcrumbs' %}
```

### JS Web Components
The Blog Listing page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores:

- Infinite Scroll [`<salla-infinite-scroll>`](doc-422706?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Slider [`<salla-slider>`](https://docs.salla.dev/doc-422735?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The `blog listing page template` allows calling the following [hooks](https://docs.salla.dev/doc-422552?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) in order to inject more information:


```php lineNumbers=true
{% hook 'blog:index.items.start' %}
{% hook 'blog:index.items.end' %}

```
### Usage
The blog listing page receives several objects containing the details of the whole blog. These objects are `slides`, `articles`, `page`, and `categories`.

Using the `categories` object, the developer may start by listing all of the available categories for the blog.

```php lineNumbers=true
<div>Blog Categories</div>
{% for category in categories %}
    <li {{ category.is_current ? ' class="style-1"' : '' }}>
        <a href="{{ category.url }}">
            {{ category.name }}
        </a>
    </li>
{% endfor %}
```

In the case of the availability of the object `slides`, which can be checked by `slides.count`, the blogs' short information can be displayed in form of slider. This can be used to display only the article `image`, `summary`, and `author name` for each blog article.

```php lineNumbers=true
{% if slides|length %}
    <div class="slider-style">
        {% for article in slides %}
            <div class="slider-item">
                {% if article.has_image %}
                    <img src="{{ article.image.url }}" alt="{{ article.image.alt }}"/>
                {% else %}
                    <img  src="{{ asset('images/placeholder.png') }}" alt="placeholder">
                {% endif %}
                <span>{{ article.created_at|date }}</span>
                <span>{{ article.author.name }}</span>
                <span>{{ article.title }}</span>
                <span>{{ article.summary }}</span>
            </div>
        {% endfor %}
    </div>
{% endif %}
```

The core object for this page is the `article` object. Using the _for-loop_ statement, the developer can display an excerpt for each blog article.

```php lineNumbers=true
{% if articles.count %}
    {% for article in articles %}
        {% if article.has_image %}
            <img src="{{ article.image.url }}" alt="{{ article.image.alt }}"/>
        {% else %}
            <img src="{{ asset('images/placeholder.png') }}" alt="placeholder">
        {% endif %}
        <span>{{ article.created_at|date }}</span>
        <span>{{ article.author.name }}</span>
        <span>{{ article.title }}</span>
        <span>{{ article.summary }}</span>
        {% if article.tags is not empty %}
            {% for tag in article.tags %}
                <a href="{{ tag.url }}">
                    <span>{{ tag.name }}</span>
                </a>
            {% endfor %}
        {% endif %}
    {% endfor %}
{% else %}
    {{ trans('pages.blog_categories.no_articles') }}
{% endif %}
```

