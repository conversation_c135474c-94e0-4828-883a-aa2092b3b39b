The `<salla-localization-modal>` web component shows the menu for the store's available languages and currencies. It consists of a [Modal](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD) activated by the [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component that shows the menu for the store's available languages and currencies, and that can be customized using the properties' parameters.




:::caution[Alert]
The `<salla-localization-modal>` **must not** be called more than once at the same page.
:::
 

:::tip[Note]
Available API Endpoints for the Localization component are:

- [Get Language](https://docs.salla.dev/doc-422614?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Currencies](https://docs.salla.dev/doc-422612#get-sdks-configurations)
- [List Currencies](https://docs.salla.dev/doc-422680?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Change Currency](https://docs.salla.dev/doc-422679?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::
## Example

<!--focus: false -->
![Localization](https://cdn.salla.network/docs/twilight/6/js-web-localization-01.gif)

<!-- <img src="https://cdn.salla.network/docs/twilight/6/js-web-localization-01.gif" width="100%"> -->

<!--
focus: false
-->

<!-- ![Localization Example](https://cdn.salla.network/docs/twilight/6/js-web-localization-01.gif) -->

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Button to open localization modal -->
<salla-button onclick="salla.event.emit(`localization::open`)">
  Currencies & languages
</salla-button>

<salla-localization-modal language="ar" currency="sa">
  <!-- you can customize the currency dom item -->
  <!-- <div slot="currency">{name} {currency} {country_code}</div>-->

  <!-- you can customize the language dom item -->
  <!-- <div slot="language">{name} {currency} {country_code}</div>-->
</salla-localization-modal>
```      
  </Tab>

   <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-localization-modal` class. Following is a complete source code for customizing this component:

```js

.s-localization-modal {
  &-title {

  }
  &-section {

  }
  &-section-inner {

  }
  &-item {

  }
  &-input {

  }
  &-label-slot {

  }
  &-label {

  }
  &-flag {

  }
  &-currency {

  }
  // dropdwon select, displys if the itmes length is more thant 5 items.
  &-select {

  }
}
```
      
  </Tab>  
    
</Tabs>



## Properties

| Property | Attribute  | Description                                                                    | Type     | Default                                    |
| -------- | ---------- | ------------------------------------------------------------------------------ | -------- | ------------------------------------------ |
| Language | `language` | Enabling the current localization attribute, either existing or newly selected | `string` | `'salla.config.get('user.language_code')'` |
| Currency | `currency` | Enabling the current currency attribute, either existing or newly selected     | `string` | `'salla.config.get('user.currency_code')'` |

## Methods
The pre-defined `methods` allow for calling to call functions built by Salla to carry out certain actvities, such as `close` / `open` the modal component or activating the action `submit`.


| Method     | Description                         | Return Type            |
| ---------- | ----------------------------------- | ---------------------- |
| `close()`  | Closes the localization's component | `Promise<HTMLElement>` |
| `open()`   | Opens the localization's component  | `Promise<boolean>` |
| `submit()` | Submits the language/currency data  | `Promise<void>`        |


## Slots
The `slots` makes it customizable to modify certain labels, such as `currency` & `language`, as well as adding `header` & `footer` extended features.


| Slot       | Description                                                                                    |
| ---------- | ---------------------------------------------------------------------------------------------- |
| `currency` | Replaces currency label with replaceable props which are `{name}`, `{code}`, `{country_code}`. |
| `language` | Replaces language label with replaceable props which are `{name}`, `{code}`, `{country_code}`. |
| `header`   | Replaces the top of the modal.                                                                 |
| `footer`   | Replaces the bottom of the modal.                                                              |
