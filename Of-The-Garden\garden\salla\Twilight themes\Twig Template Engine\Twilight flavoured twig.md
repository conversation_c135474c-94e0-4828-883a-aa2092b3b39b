Although <PERSON><PERSON> already provides a large list of helper functions and filters, <PERSON> also has added a selection of useful helpers and filters to make theming easier.

:::tip[Note]
Twig helper functions are called directly with any parameters passed in via parenthesis.
:::

## 📙 What you'll learn
In this article, you'll learn how to use:
- [Twilight flavoured helper functions.](#helpers)
- [Twilight flavoured filter.](#filters)
<hr>

## Helpers
In general, helpers are Twig functions by which we can implement functionality in Twig. 

Following are the helper functions available on all Twilight `.twig` files.

| Helper Available                  | Description                                        |
| --------------------------------- | -------------------------------------------------- |
| [is_current_url](#is_current_url) | Checks the link.                                   |
| [is_page](#is_page)               | Displays the page name.                            |
| [link](#link)                     | Creates links.                                     |
| [old](#old)                       | Keeps previous inputs.                             |
| [pluralize](#pluralize)           | Transforms the word into singular or plural forms. |
| [page](#page)                     | Creates a link to the page.                        |
| [trans](#trans)                   | Translates texts to the current store language.    |

### is_current_url
Checks if the current link matches the passed pattern.
```js
is_current_url(string $pattern)
```

Examples:
```js
{% if is_current_url('orders',{refund:'true'}) %}
    ...
{% endif %}
```

### is_page

This helper displays the page name on the page.

Example:

```js
{% if is_page('product.single') %}
{%endif%}
```

### link
Combine strings to generate a full link.
```js
link(?string $url, ?array $parameters)
```

Examples:
```js
<!-- basic use -->
link("page_url", {by:"test"}) 
<!-- https://my_store.com/page_url?by=test --> 
```

### pluralize
Similar to trans, it takes a `$count` argument to change the message in plural or singular form in-store language.
```js
pluralize(string $key, int $count)
```
Examples:
```js
<!-- basic use -->
{{ pluralize('blocks.comments.comment', comments.total)|number }}
<!-- <span>21</span> Comment --> 
```

### page
This helper generates the link to a specific page as follows:

Example
```js
<a href={{ page('cart') }}>Cart</a>
```

The generated link will be the cart's page url for the store, as follows:
```https://www.my-store.com/cart```

### old
This helper function keeps previous input from one request during the next request. This feature is particularly useful for re-populating forms after detecting validation errors.

Examples:
```js
old("name")
```

Example
```js
<input type="text" name="username" value="{{ old('username') }}">
```

### trans
This helper translates the passed key to the current store langauge. 
Here are more information about [Localization](https://docs.salla.dev/doc-422553?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
```js
trans(string $key, $replace = [], $default = null)
```
Examples:
```js
<!-- simple key -->
<span>{{ trans('common.titles.orders') }}</span>

<!-- key with variable -->
<span>{{ trans('pages.gift.sent_you_a_gift', ['name' => 'Mohammed']) }}</span>

<!-- key with enforced locale/language -->
<!-- this will always print the result of key in English even if the store has different default language -->
<span>{{ trans('common.titles.orders', [], en) }}</span>
```

## Filters

These special filters are available on all `.twig` files. Use the the pipe sign, `|`, to use them.

Following are the filters available by Twilight.


| Filters Available                   | Description |
| --------------------------------- | ----------- |
| [asset](#asset)                   | Creates links to Salla's theme files.         |
| [camel_case](#camel_case)         | Replaces punctations and spaces with letters in uppercase.       |
| [cdn](#asset)                     | Retrives links of pre-defined files such as `font` and `saicon`. |
| [currency](#currency)             | Adds currency sign.        |
| [date](#date)                     | Displays the dates in the current page language.      |
| [is_placeholder](#is_placeholder) | Checks if image url is placeholder or image.          |
| [kebab_case](#kebab_case)         | Replaces the spaces with a dash.         |
| [money](#money)                   | An alias for currency filter.         |
| [number](#number)                 | Parses numbers into Arabic numbers.       |
| [snake_case](#snake_case)         | Replaces spaces in texts with underscore and keeps first letter in lowercase.|
| [studly_case](#studly_case)       | Capilaizes first letters in each subword.        |
| [time_ago](#time_ago)             | Shows the time of and action execution.        |


### asset

This filter creates links to Salla theme files. 

```js
'dist/app.css' | asset
```

### camel_case
This filter removes each spaces or punctuation in a given string, indicating the separation of words with a single capitalized letter, and the first word starting with either case. Common examples include "iPhone" and "eBay".
```js
<!-- phone.brand is 'i phone' -->
{{ phone.brand | camel_case }}
<!-- the result is: iPhone -->
```

### cdn

This filter retrives links of pre-defined files such as `font` and `saicon`.

```js
'images/maroof-footer.png' | cdn 
```

### currency
This filter add the currency sign 'SAR' to the passed value.
```js
<!-- order.total is '100' -->
{{ order.total | currency }}
<!-- the result is: 100SAR -->
```

### date
This filter overrides the default [date filter](https://twig.symfony.com/doc/3.x/filters/date.html) given with Twig. It formats a date to a given format with concidering the page's selected language.

```js
<!-- order.created_at -->
{{ order.created_at | date("'j F Y'") }}
<!-- the result is: ١٧ مارس ٢٠٢١ -->
```

### is_placeholder

This filter is used to know if the image url is placeholder or actual image

```js
<!-- is_placeholder -->
{{product.image.url|is_placholder}}

<!-- the result is: Photo is placed -->

```

:::tip[Tip]
We have default placeholder image, developer may want to use another image, it can be replaced by calling:
```js
<!-- is_placeholder -->
{{theme.set('placeholder','images/img_placeholder.png')}}
<!-- the result is: the path images/img_placholder.png is inside assets folder-->
```
:::


### kebab_case
This filter replaces the spaces between words with a dash.
```js
<!-- user.name is 'Mohamed Ali' -->
{{ user.name | kabab_case }}
<!-- the result is: Mohamed-Ali -->
```


### money 
This an alias for currency filter.
```js
<!-- order.total is '100' -->
{{ order.total | money }}
<!-- the result is: 100SAR -->
```

### number
This filter will always parse any number given to Arabic number if merchants enables it and the current language is Arabic. It will also parse decimal number representation to a decimal point in Arabic (,)
```js
<!-- user.mobile is '966567891011' -->
{{ user.mobile | number }}
<!-- the result is: ٩٦٦٥٦٧٨٩١٠١١ -->

<!-- decimal.number is '100.10' -->
{{ decimal.number | number ({“search”:”replacewith”})}}

<!-- the result is: ١٠٠.١٠ -->
```

### snake_case
This filter replaces each space in a given string with an underscore character, and the first letter of each word written in lowercase.
```js
<!-- user.code is 'This is a Test' -->
{{ user.code | snake_case }}
<!-- the result is: this-is-a-test -->
```


### studly_case
Also known as [PascalCase](https://techterms.com/definition/pascalcase), which implies that the first capital of each subword is capitalized.
```js
<!-- phone.brand is 'i phone' -->
{{ phone.brand |  studly_case }}
<!-- the result is: iPhone -->
```

### time_ago

This filter shows the time of and action execution, for example, "Comment created 10 minutes ago."

```js
<!-- time_ago -->
{{ comment.created_at|time_ago }}
<!-- the result is: Comment created 10 min ago -->
```
