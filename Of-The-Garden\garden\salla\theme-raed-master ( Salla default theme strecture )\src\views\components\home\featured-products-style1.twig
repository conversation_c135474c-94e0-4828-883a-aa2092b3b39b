{#
| Variable                 | Type      | Description                                                         |
|--------------------------|-----------|---------------------------------------------------------------------|
| items                    | array     |                                                                     |
| items[].title            | string    | Section title                                                       |
| items[].name             | string    | Alias for title                                                     |
| items[].type             | string    | category,most_sales,latest_products,chosen_products                 |
| items[].featured_product | Null      |                                                                     |
| items[].products[]       | Product[] |                                                                     |
| items[].limit            | Int       | Number of products to be shown                                      |
| items[].id               | string    | Section id                                                          |
| main_product             | array     |                                                                     |
| main_product.id          | string    | string product id                                                   |
| main_product.title       | string    | Offer title                                                         |
| main_product.value       | string    | Product Name                                                        |
| main_product.product     | Product   |                                                                     |
| position                 | int       | sorting block                                                       |
#}
{% set component_id='featured-products-style1-' ~ position %}
<section class="s-block s-block-tabs s-block--special-products container" id="{{ component_id }}">
    <div class="s-block__title"> 
      <div class="right-side">
        <h2>{{ main_product.title }}</h2>
      </div>
      <div class="tabs hide-scroll hidden lg:flex">
          {% for section in items %}
              <salla-button shape="link"
                      data-target="{{ component_id }}-{{ section.id }}"
                      data-component-id="{{ component_id }}"
                      class="tab-trigger {{ loop.first?'is-active' : '' }}">
                  {{ section.title }}
              </salla-button>
          {% endfor %}
      </div>
    </div>

    <div class="grid grid-cols-1 {{ main_product?'lg:grid-cols-2':''}} gap-8">
        {% if main_product %}
            <div class="flex flex-col">
                <custom-salla-product-card shadow-on-hover product="{{main_product.product}}" isSpecial></custom-salla-product-card>  
            </div>
        {% endif %}
        <div class="flex flex-col">
            <div class="tabs mb-4 sm:mb-8 hide-scroll lg:hidden">
                {% for section in items %}
                    <salla-button shape="link"
                            data-target="{{ component_id }}-{{ section.id }}"
                            data-component-id="{{ component_id }}"
                            class="tab-trigger {{ loop.first?'is-active':'' }}">
                        {{ section.title }}
                    </salla-button> 
                {% endfor %}
            </div>

            <div class="tabs-wrapper flex flex-1">
                {% for section in items %}
                    <div id="{{ component_id }}-{{ section.id }}" class="tabs__item grid-cols-2 {{ main_product?'':'lg:grid-cols-4'}} {{ loop.first?'is-active':'' }}">
                        {% for product in section.products|slice(0,main_product?4:8) %}
                            <custom-salla-product-card product="{{product}}"></custom-salla-product-card>
                        {% endfor %}
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</section> 
