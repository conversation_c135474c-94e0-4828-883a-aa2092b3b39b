Using **Twilight**, it's easy to change how the **user interfaces (UI)** of the JS Web Components look. This is to enable better user interface consistency throughout the theme and to prevent data from being hard-coded. The user interface can be customized in a variety of ways, including changing the colors, fonts, layouts, and sizes of its components.

In this article, we'll explore how the components' UI customization works, Tailwind installation and configuration, as well as the different ways to modify the look-and-feel of the JS Web Components.

## 📙 What you'll learn
- How it works
- Tailwind Installation and Confiuragtion
- Components UI customization

## How it works

**Twilight Web Components** are based on the basis and configuration of the [Tailwind CSS Framework](https://tailwindcss.com/). This ensures that all web components are easily customizable and that all output CSS styles comply with [Tail<PERSON>'s configuration](https://tailwindcss.com/docs/configuration).

In order to make a general customization of the theme's overall look-and-feel, the file `tailwind.config.js` can be modified. The following is the default source code for this file, from which we can see how to change the values related to the theme's `colors`, `font`, and more.

:::tip[Note]
More information about this configuration file can be found [here](https://tailwindcss.com/docs/configuration).
:::

```js title="tailwind.config.js"
module.exports = {
...
  theme: {
    screens: {
      sm: '480px',
      md: '768px',
      lg: '976px',
      xl: '1440px',
    },
    colors: {
      'blue': '#1fb6ff',
      'purple': '#7e5bef',
      'pink': '#ff49db',
      'orange': '#ff7849',
      'green': '#13ce66',
      'yellow': '#ffc82c',
      'gray-dark': '#273444',
      'gray': '#8492a6',
      'gray-light': '#d3dce6',
    },
    fontFamily: {
      sans: ['Graphik', 'sans-serif'],
      serif: ['Merriweather', 'serif'],
    },
    extend: {
      spacing: {
        '128': '32rem',
        '144': '36rem',
      },
      borderRadius: {
        '4xl': '2rem',
      }
    }
  }
...
}
```


## Custom CSS framework

Twilight Web Components use the same [CSS Variables](https://docs.salla.dev/doc-421945?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) that have already been introduced. That means the developer also has the flexibility to use self-developed CSS styles away from the [Tailwind CSS Styles](https://tailwindcss.com/docs/utility-first).

The core idea here is that each component has its own CSS class, which allows the developer to implement the style as he wishes. For example, the JS Web Component [Button](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) comes with the following CSS class:

```css
.s-button{
  &-wrap{
    
  }
  &-element{
    
  }
  &-link{
    
  }
  &-icon{
    
  }
....
}
```
In the above example, developers can add customizable styles to change, for example, the button's `link` and `icon`. In the coming articles of this documentation, we will explore how the UI for each JS Web Component can be modified using its `.scss` file.