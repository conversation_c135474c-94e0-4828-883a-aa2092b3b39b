The `<salla-map>` web component displays geographical maps from various sources. It supports multiple layers, tiled and full-image sources, adding markers, and interaction through events.



## Example

<!--
focus: false
-->

![Map](https://cdn.salla.network/docs/twilight/6/js-web-map-01.jpeg)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Salla Map component Usage -->
<salla-map
  api-key="gPAnYXyivuAtzOiE2tYXGpaxxxx"
  theme="light"
  searchable="false">
</salla-map>

<!-- Using the readonly attribute in the Single Order Page -->
<salla-map
  api-key="gPAnYXyivuAtzOiE2tYXGpaxxxx"
  theme="light"
  readonly="false">
</salla-map>

```
  </Tab>      
  <Tab title="SASS">
      

This JS web component can be targeted for styling by its `.s-map-modal-wrapper` class. Following is a complete source code for customizing this component:

```css
.s-map-wrapper {
  .s-map-location-button {
    .s-map-location-icon {
      margin-right: 10px;
    }
  }
}
.s-map-modal-wrapper {
  .s-map-modal-body {
    padding: 0;
  }

  .s-map-title {
    position: relative;
    margin-top: -0.5rem;
    margin-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .s-map-modal-body {
    overflow: hidden;
    position: relative;
    .s-map-element {
      height: 400px;
      width: 100%;
    }
    .s-map-search-wrapper {
      position: absolute;
      top: 0px;
      z-index: 11;
      z-index: 1;
      margin: 20px;
      width: -webkit-fill-available;
      input {
        width: 100%;
        height: 40px;
        padding: 10px;
      }
      .s-map-search-results {
        height: 100px;
        padding: 10px;
        margin-top: 1px;
        display: flex;
        flex-direction: column;
      }
    }
    .s-loading-container {
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 5rem;
    }
    .s-map-my-location-icon {
      position: absolute;
      bottom: 1.5rem;
      margin: 10px;
    }
  }
}
```
  
  </Tab>
  
</Tabs>

## Properties

| Property   | Attribute   | Description                     | Type      | Default     |
| ---------- | ----------- | ------------------------------- | --------- | ----------- |
| API Key    | `api-key`   | Valid Google Maps API Key.      | `string`  | `undefined`      |
| Latitude   | `lat`       | Sets the map's latitude.        | `number`  | `undefined` |
| Longitude  | `lng`       | Sets the map's longitude.                                | `number`  | `undefined`  |
| Modal Title | `modal-title` | Modal Title                                             | `string`  | `undefined` |
| Name       | `name`        | File input name for the native formData                 | `string`  | `'location'`              |
| Read Only | `readonly` | Whether or not the actions are disabled or enabled. | `boolean` | `false`     |
| Required   | `required`    | Set if the location input is required or not            | `boolean` | `false`                   |
| Searchable | `seachable` | Whether or not to show the search bar. | `boolean` | `false`     |
| Theme      | `theme`       | Sets map style.                                         | `string`  | `'light'`                 |
| Zoom       | `zoom`      | Sets the inital map zoom.            | `number`  | `10`        |

## Events

| Event                    | Description                                                 | Type               |
| ------------------------ | ----------------------------------------------------------- | ------------------ |
| `currentLocationChanged` | This event will be fired when the current location is selected. | `CustomEvent<any>` |
| `invalidInput` | This event will be fired when the input is invalid. | `CustomEvent<any>` |
| `mapClicked`             | This event will be fired when the map is clicked. | `CustomEvent<any>` |
| `selected`               | This event will be fired when the location is selected. | `CustomEvent<any>` |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `open` which opens the map component.

| Method   | Description             | Return Type            |
| -------- | ----------------------- | ---------------------- |
| `open()` | Open the map component | `Promise<HTMLElement>` |
