The store brands' logos are displayed using this component. Developers may use any design to style it because this is a significant section to draw the customers' attention.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── brands.twig
          ...
```



### Example

<!--
focus: false
-->
![Brands Components](https://cdn.salla.network/docs/twilight/4/pages-components-home-custom-brands-01.png)

### Settings

This component is a [custom component](doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). Its configuration is described in the [twilight.json](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) as follows:

```json lineNumbers
{
  "version": ...,
  "theme_name": ...,
  "repo_url": ...,
  "support_url": ...,
  ...
  "components": [
    {
      "name": "brands",
      "title": "Brands",
      "icon": "sicon-award-ribbon",
      "path": "home.brands",
      "fields": [
        {
          "id": "brands",
          "type": "items",
          "label": "Brand",
          "format": "dropdown-list",
          "multichoice": true,
          "required": true,
          "source": "Brands"
        }
      ]
    }
  ]
  
}

```
#### Theme Preview 

The components can be managed using the theme preview in the [Theme menu item](https://salla.partners/themes) of Salla Partners Portal. The developer can edit the component and enable it in the theme preview dashboard.
![Theme Preview Dashboard](https://i.imgur.com/lC1NlTE.png)

### Variables
The variables of this component are fetched from the twilight.json file as per the merchant settings. They are located in the components section's fields.


<DataSchema id="1383686" />
  

### Usage
This component lists the brands related to the merchant's store using a _loop_. Based on that, a brand _url_ and _logo_ will be displayed as per the developer style.


```php lineNumbers
<h2>{{ trans('blocks.home.browse_brands') }}</h2>
<a href="{{ link('brands') }}"> {{ trans('blocks.home.display_all') }} </a>
{% for brand in component.brands %}
    <a href="{{ brand.url }}">
        <img src="{{ brand.logo }}" alt=""/>
    </a>
{% endfor %}
```



