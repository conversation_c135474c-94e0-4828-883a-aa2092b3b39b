In general, the authentication API allows a developer to control all aspects of a user's identity. It has endpoints for logging in, logging out, and using APIs, among other things. This *login* endpoint is used to authenticate a user. Either the email or the phone number can be used as a login identifier, which means that the user has two *types* of login process, `email` and `phone`. 

:::tip
The *login* endpoint has been implemented in the [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


```mermaid
sequenceDiagram
    autonumber
    Customer->>Login: Enters email or phone to login
    Login->>Backend: Sends payload with email/phone, and requests sending access code
    Backend-->>Customer: Sends access code to the customer
    Note left of Backend: The access code will sent via email or phone as per customer entry
    loop verify (access code check)
        Customer->>Login: Enters access code
        Note right of Customer: The customer needs to enter the correct code within 30 seconds
    end
    Login->>Customer: The customer auth successfully
```

## Payload


<DataSchema id="1387184" />

## Response

<Tabs>
  <Tab title="Success">


<DataSchema id="1427183" />

</Tab>
<Tab title="Error">



<DataSchema id="1427184" />

</Tab>
</Tabs>


## Usage

<Tabs>
  <Tab title="Mobile">
  
In the case of using the phone number as a *login* type, the developer can use the example below to receive the data, and redirect the user accordingly.

```js
salla.auth.login({
  type: 'mobile', 
  phone: '555555555', 
  country_code: 'SA'
}).then((response) => {
  /* add your code here */
});
```
      
  </Tab>
  <Tab title="Email">

On the other hand, when using an email as a *login* type, the developer can receive the data using the example below and redirect the user accordingly.

```js
salla.auth.login({
  type: 'email', 
  email: '<EMAIL>'
}).then((response) => {
  /* add your code here */
});
```
      
  </Tab>
</Tabs>

## Events
The *login* process may trigger two events during the login process, onCodeSent and onCodeNotSent.

### onCodeSent
This event will be triggered when the login process is completed successfully and the loing code has been sent.

```js
salla.event.auth.onCodeSent((response) => {
  /* add your code here */
  console.log(response);
});
```

### onCodeNotSent
This event will be triggered when a failure occurs in receiving and setting the access code.

```js
salla.event.auth.onCodeNotSent((errorMessage) => {
  /* add your code here */
  console.log(errorMessage)
});
```