This **_pre-defined component_** simply displays square shape photos in form of a grid. The first photo will be the __ leading __ photo and will be bigger in size.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├──── home
   |  |  ├──── square-photos.twig
      ...
   ...
```


### Example
<!--
focus: false
-->
![Square Photos](https://cdn.salla.network/docs/twilight/4/pages-components-home-square-photos-01.png)

### Variables


<DataSchema id="1383703" />


### Usage
This component takes a list of photos as a collection of `items[]`. Each item in this collection has `url`, `link_type`, `image.url`, and `image.alt`. The first item represents the first image that will be the __leading__ photo and will be bigger in size, then a **for-loop** will be used to list the rest of the photos.

```php lineNumbers
{% for item in items %}
    <a href="{{ item.url }}" {% if item.link_type=='external_link' %} target="_blank" {% endif %}>
        <img src="{{ items[0].image.url }}" alt="{{ item.text }}">
        {% if item.text %}
            <h3>{{ item.text }}</h3>
        {% endif %}
    </a>
{% endfor %}
```




