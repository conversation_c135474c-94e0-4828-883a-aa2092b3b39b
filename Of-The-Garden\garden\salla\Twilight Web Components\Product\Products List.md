The `<salla-products-list>` web component is used to display a group of related products with some information, such as products' names, prices, and other relevant information in an organized way. Users can interact with the component by clicking on a product to view its details.


## Example

<!--
focus: false
-->

![Products List](https://cdn.salla.network/docs/twilight/6/js-web-product-list-01.jpg?v=1-10-2022)


## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Product List component usage -->
<salla-products-list
  source="offers"
  limit="5"
  horizontal-cards="true">
</salla-products-list>
```
      
  </Tab>
  
</Tabs>


:::tip[Note]
This component, `salla-product-list`, uses inherently the **default** [`salla-product-card`](https://docs.salla.dev/doc-422718?nav=01HNFTE06J4QC24T0D5BPRYKMD) component. If you want to further customize the component, feel free to read more details from [here](https://docs.salla.dev/doc-422718#custom-salla-product-card-component).
:::


## Properties

| Property               | Attribute                | Description                                                                                                                                                                                                                                                                                                                                                                                                                                   | Type                                                                                                          | Default                       |
| ---------------------- | ------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------- | ----------------------------- |
| Autoload               | `autoload`               | Whether or not to autoload the next page's content when scrolling to its view                                                                                                                                                                                                                                                                                                                                                                 | `boolean`                                                                                                     | `'false'`                     |
| Filters Results        | `filters-results`        | Display the filteration results. The events `salla-filters::changed` and `salla-filters::re-render` should be listened to in order for this property to function properly                                                                                                                                                                                                                                                                     | `boolean`                                                                                                     | `undefined`                   |
| Horizontal Cards       | `horizontal-cards`       | Shows the product lists in a horizontal cards' way                                                                                                                                                                                                                                                                                                                                                                                            | `boolean`                                                                                                     | `undefined`                   |
| Includes             | `includes`               | Specifies additional data to be included in the product fetch response. The value can be an array of strings or a JSON string. <br>Example:<br> - As an array:<br> &nbsp; - `includes=["options", "images"]`<br> - As a JSON string:<br> &nbsp; - `includes='["options", "images"]'` <br> The array or JSON string can include product options, `"options"`, in the response.<br>| `string \| string[]`                                                                                          | `undefined`                   |
| Limit                  | `limit`                  | Limit the number of products in the list.                                                                                                                                                                                                                                                                                                                                                                                                     | `number`                                                                                                      | `undefined`                   |
| Product Card Component | `product-card-component` | Custom Card Component for the Salla Products List.  This property  allows you to [customize](doc-422718#custom-salla-product-card-component) the appearance of individual product cards within a Salla Products List.                                                                                                                                                                                                                         | `string`                                                                                                      | `'custom-salla-product-card'` |
| Row Cards             | `row-cards`              | Display product cards in a vertical list, with each row containing only one product card                                                                                                                                                                                                                                                                                                                                                                                                                | `boolean`                                                                                                     | `undefined`                   |
| Sort By                | `sort-by`                | Sorting the list of products.                                                                                                                                                                                                                                                                                                                                                                                                                 | `string`                                                                                                      | `undefined`                   |
| Source                 | `source`                 | The Products List's source                                                                                                                                                                                                                                                                                                                                                                                                                    | `"brands" \| "categories" \| "json" \| "latest" \| "offers" \| "related" \| "search" \| "selected" \| "tags"` | `undefined`                   |
| Source Value           | `source-value`           | The source value, which could be consisting of different values such as the following: • array of IDs when `source` in `['categories', 'brands', 'tags', 'selected']` \| • keyword when `source` = `'search'` \| • products payload when `source` = `'json'` \| • product_id when `source` = `'related'`                                                                                                                                      | `string`                                                                                                      | `undefined`                   |



## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `reload` which reloads the product list view.


| Method                   | Description                                           | Return Type            |
| ------------------------ | ----------------------------------------------------- | ---------------------- |
| `reload()`                 | Reloads the product list view                                   | `Promise<any>`         |
| `setFilters(filters: any)`                 | Sets the filters value                                   | `Promise<any>`         |












