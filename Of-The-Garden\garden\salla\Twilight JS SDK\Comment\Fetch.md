The *fetch* endpoint allows you to retrieve comments related to a particular object or resource within the platform. By making a request to this endpoint, you can retrieve comments made by users or customers, providing valuable insights, feedback, and discussions related to the specific object.

## Payload 

<DataSchema id="1387223" />

## Response
<Tabs>
  <Tab title="Success">
 
<DataSchema id="1427930" />

     
  </Tab>
   <Tab title="Error">

<DataSchema id="1427314" />
  </Tab>
  
</Tabs>




## Usage
To fetch the customer's comment about a specific product or a specific page, the developer may call the `fetch` method.

```js
salla.comment.fetch({
    id: 85214,
    comment: "The product's price is good",
    type: "product",
  })
  .then((response) => {
    /* add your code here */
  });

```


## Events
This endpoint may trigger two events, the onFetch and onFetchedFailed events.

### onFetch
This event is triggered when fetching a comment by the customer is done without having any errors coming back from the backend.

```js
salla.event.comment.Fetch((response) => {
  console.log(response)
});
```
### onFetchedFailed
This event is triggered when when fetching a new comment by the customer is not completed and an error has occurred.

```js
salla.event.comment.onFetchedFailed((errorMessage) => {
  console.log(errorMessage)
});
```