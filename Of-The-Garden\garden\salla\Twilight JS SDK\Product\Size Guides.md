This endpoint is used to fetch the size guide for a specific product in an online store. The endpoint takes a `product_id` parameter, which is the ID of the product for which the size guide is being fetched.

:::tip
The *Size Guides* endpoint has been implemented in the [Product Size Guide](https://docs.salla.dev/doc-422721?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::
## Payload


<DataSchema id="1387260" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1387261" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>



## Usage
To perform the action of retrieving the size guide for any product, the developer may call the method `getSizeGuides()` and pass in the product ID as an argument as follows:

```js
// Call the getSizeGuides method with a product ID
salla.product.getSizeGuides(12345)
  .then(response => {
    // Do something with the size guide data
    console.log(response);
  })
  .catch(error => {
    // Handle any errors that occur
    console.error(error);
  });
```


## Events
This endpoint may trigger two events, the `onSizeGuideFetched` and `onSizeGuideFetchFailed` events.

### onSizeGuideFetched
This event is triggered when the action of retrieving the size guide for any product is done without having any errors coming back from the backend.

```js
salla.product.event.onSizeGuideFetched((response) => {
  console.log(response)
});
```
### onSizeGuideFetchFailed
This event is triggered when the action of retrieving retrieving the size guide for any product is not completed and an error has occurred.

```js
salla.product.event.onSizeGuideFetchFailed((errorMessage) => {
  console.log(errorMessage)
});

