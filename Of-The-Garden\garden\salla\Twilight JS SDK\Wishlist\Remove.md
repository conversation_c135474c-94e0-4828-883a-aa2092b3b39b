This endpoint removes a product to the customer's wishlist. A customer's wishlist is a collection of desired products saved to the customer's account, indicating interest but not an immediate intent to buy. 

## Payload `authenticated`


<DataSchema id="1387279" />


## Response
<Tabs>
  <Tab title="Success">
 
<DataSchema id="1427828" />
  </Tab>
 
</Tabs>

## Usage
To perform the action of removing a product to the customer's wishlist, the method `remove()` may be used as follows.

```js
salla.wishlist.remove({ id: 12345 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.wishlist.remove(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onRemoved and onRemovingFailed events.

### onRemoved
This event is triggered when removing a product to the customer's wishlist is done without having any errors coming back from the backend.

```js
salla.event.wishlist.onRemoved((response) => {
  console.log(response)
});
```
### onRemovingFailed
This event is triggered when removing a product to the customer's wishlist is not completed and an error has occurred.

```js
salla.event.wishlist.onRemovingFailed((errorMessage) => {
  console.log(errorMessage)
});