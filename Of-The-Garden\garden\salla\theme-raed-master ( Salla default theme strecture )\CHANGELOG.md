# Changelog

On this page, you will find all about <PERSON><PERSON>'s Theme Raed updates, including frequent updates, bug fixes, new features, and deprecated elements. We will be displaying only released updates on [Theme Read's ChangeLog](https://github.com/SallaApp/theme-raed/blob/master/CHANGELOG.md) here on GitHub

> 📝 Note
> 
> Make sure to visit this page regularly for updates before working on your Theme based on Theme Raed, as we will be documenting any notable changes here.

> ℹ️ Info
>The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/).

# 🏅 [1.190.0](https://github.com/SallaApp/theme-raed/compare/1.189.0...1.190.0)(23-02-2025)
### Feature
- Support the new SAR currency symbol

# ⛵ [1.189.0](https://github.com/SallaApp/theme-raed/compare/1.187.0...1.189.0)(29-12-2024)
### Feature

- Support the `is_default` property for homepage components as well as default data for some components
- Add preview images for each component in Theme Raed using the image key in the `twilight.json` file

# 🧶 [1.187.0](https://github.com/SallaApp/theme-raed/compare/1.185.0...1.187.0)(17-12-2024)
### Enhancement

- Upgrade the `twilight-components` to fix the style of product's options

# 🪐 [1.185.0](https://github.com/SallaApp/theme-raed/compare/1.184.0...1.185.0)(09-10-2024)
### Enhancement
- Remove the `advertisement.twig` file, and render its content automatically
#### Developers Actions:
- Leverage New Feature<br>
Remove the `advertisement.twig` file so the [`salla-advertisement`](https://docs.salla.dev/doc-478502/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component renders automatically, and ensure you test the styles.

# 🌪️ [1.184.0](https://github.com/SallaApp/theme-raed/compare/1.183.0...1.184.0)(25-09-2024)
### Feature
- Support Blog Interaction with Comments and Likes:
  - Support for liking and unliking on the blog single page.
  - Display likes count and comments count on blog cards.
  - Enable comments and replies on the blog post page. 

# 🌂 [1.183.0](https://github.com/SallaApp/theme-raed/compare/1.182.0...1.183.0)(25-09-2024)
### Feature
- Support comments and like in the Merchant blog

# 🎓 [1.182.0](https://github.com/SallaApp/theme-raed/compare/1.181.0...1.182.0)(24-09-2024)
### Bug Fixes
- Display rating in the `product-card`

# 👔 [1.181.0](https://github.com/SallaApp/theme-raed/compare/1.180.0...1.181.0)(24-09-2024)
### Bug Fixes
- Display rating in the `product-card`

# 💈 [1.180.0](https://github.com/SallaApp/theme-raed/compare/1.179.0...1.180.0)(31-08-2024)
### Enhancement
- Enhancements in the Menus

# 🪡 [1.179.0](https://github.com/SallaApp/theme-raed/compare/1.178.0...1.179.0)(30-08-2024)
### Bug Fixes
- Avoid storing menu items in the browser

# 👣 [1.178.0](https://github.com/SallaApp/theme-raed/compare/1.177.0...1.178.0)(29-08-2024)
### Bug Fixes
- Fix align of text in the My Account page


# 🃏 [1.177.0](https://github.com/SallaApp/theme-raed/compare/1.176.0...1.177.0)(19-08-2024)
### Bug Fixes
-  Fix memory leak error in the `product-card`

# 🔅 [1.176.0](https://github.com/SallaApp/theme-raed/compare/1.175.0...1.176.0)(12-08-2024)
### Bug Fixes
- Fix the error message style when a user uploads an avatar with size more than 2MB

# 🧮 [1.175.0](https://github.com/SallaApp/theme-raed/compare/1.174.0...1.175.0)(08-08-2024)
### Enhancement
- Enhance the reviews system 

# 📊 [1.174.0](https://github.com/SallaApp/theme-raed/compare/1.173.0...1.174.0)(06-08-2024)
### Bug Fixes
- Fix unavailable options

# 🪞 [1.173.0](https://github.com/SallaApp/theme-raed/compare/1.172.0...1.173.0)(05-08-2024)
### Bug Fixes
- Update Product Price with unavailable Option

# 🔮 [1.172.0](https://github.com/SallaApp/theme-raed/compare/1.171.0...1.172.0)(01-08-2024)
### Bug Fixes
- Upgrade to the `twilight-components`

# 🖼️ [1.171.0](https://github.com/SallaApp/theme-raed/compare/1.170.0...1.171.0)(01-08-2024)
### Bug Fixes
- Fix the product's image in the product's details page

# 🔦 [1.170.0](https://github.com/SallaApp/theme-raed/compare/1.169.0...1.170.0)(25-07-2024)
### Bug Fixes
- Upgrade twilight-components package


# 🖲️ [1.169.0](https://github.com/SallaApp/theme-raed/compare/1.168.0...1.169.0)(23-07-2024)
### Enhancements 
- Upgrade Twilight & Twilight components

#💈[1.168.0](https://github.com/SallaApp/theme-raed/compare/1.167.0...1.168.0)(17-07-2024)
### Enhancements 
- Handle protected digital files

# ⛓️‍💥 [1.167.0](https://github.com/SallaApp/theme-raed/compare/1.165.0...1.167.0)(10-07-2024)
### Features
- Support Digital Product Option

# 💈 [1.165.0](https://github.com/SallaApp/theme-raed/compare/1.164.0...1.165.0)(02-07-2024)
### Enhancements
- Support order option book appointment field in the `salla-booking-field` JS Web Component

# 🖼️ [1.164.0](https://github.com/SallaApp/theme-raed/compare/1.163.0...1.164.0)(29-06-2024)
### Bug Fixes
- Update price based on product's options
  
# 🛎️ [1.163.0](https://github.com/SallaApp/theme-raed/compare/1.162.0...1.163.0)(27-06-2024)
### Bug Fixes
- Fix the product options validation

# 🎁 [1.162.0](https://github.com/SallaApp/theme-raed/compare/1.161.0...1.162.0)(12-06-2024)
### Bug Fixes
- Fix console errors due to hover action on the `menu-item` variable

# 🎈 [1.161.0](https://github.com/SallaApp/theme-raed/compare/1.160.0...1.161.0)(12-06-2024)
### Bug Fixes
- Fix Style of the product's description

# 📎 [1.160.0](https://github.com/SallaApp/theme-raed/compare/1.160.0...1.161.0)(20-06-2024)
### Enhancements
- Enhancements for the Testimonials Page

# 🧲 [1.158.0](https://github.com/SallaApp/theme-raed/compare/1.154.0...1.158.0)(29-05-2024)
### Bug Fixes
 - Eager Loading on the WishlistCard

# 🏷️ [1.154.0](https://github.com/SallaApp/theme-raed/compare/1.150.0...1.154.0)(23-05-2024)
### Enhancements
 - Support the [`salla-comments`](https://docs.salla.dev/doc-482455/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component in the product single page.
 - Cart options fixes and enhancements.
 - Use the [`salla-products-list`](https://docs.salla.dev/doc-422719/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component on the Wishlist page using custom components.
 
### Features
 - Support new component, the [`salla-conditional-offer`](https://docs.salla.dev/doc-537931/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component
   
# 📰 [1.149.0](https://github.com/SallaApp/theme-raed/compare/1.149.0...1.150.0)(21-05-2024)
### Enhancement
- Custom Component enabled for the Product Card on the Wishlist page
### Bug Fixes
- Typographical error found on the Wishlist page

# 💎 [1.148.0](https://github.com/SallaApp/theme-raed/compare/1.148.0...1.150.0)(21-05-2024)
### Features
- Support the [`salla-comments`](https://docs.salla.dev/doc-482455/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web Component
- Support cart options total on both cart and order pages
### Bug Fixes
- Fix the cart options price issue on the order page

# 🏹 [1.147.0](https://github.com/SallaApp/theme-raed/compare/1.147.0...1.150.0)(16-05-2024)
### Enhancements
- Unused files removed
  - Footer's files 
    - contacts.twig
    - menu.twig
    - mobile-apps.twig
    - payment-methods.twig
    - social.twig
  - Header's files 
    - menu-item.twig
    - breadcrumbs.twig
    - menu.twig
  - Product's file
    - offer.twig
      
# 🛠 [1.146.0](https://github.com/SallaApp/theme-raed/compare/1.145.0...1.147.1)(06-05-2024)
### Bug Fixes
- Fixes and enhancements on the [`salla-reviews`](https://docs.salla.dev/doc-508226/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component
- Support `salla-notifications` component

# ✨ [1.145.0](https://github.com/SallaApp/theme-raed/compare/1.143.0...1.145.0)(29-04-2024)
### Features:
- Support `main menus` via API
- Support the [`salla-reviews`](https://docs.salla.dev/doc-508226/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component
- Support the [`salla-breadcrumb`](https://docs.salla.dev/doc-482370/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component
- Support the [`salla-order`](https://docs.salla.dev/doc-508225/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component

# 🏎️ 1.144.0 (2024-04-25)
### Features
- Validate all product options before updating the price request with better words such as "`Don't call update price request unless all product options are valid.`"
- Make quantity input as `readonly` when the maximum quantity is equal to the value `1`
  
# 🖌️ [1.143.0](https://github.com/SallaApp/theme-raed/compare/1.142.0...1.143.0) (27-03-2024)
### Features
- Support new Twilight Components using `salla.config` and Ajax requests.

# 🚀 [1.142.0](https://github.com/SallaApp/theme-raed/compare/1.141.0...1.142.0) (21-03-2024)
### Features
- Support the [`salla-offer`](https://docs.salla.dev/doc-440408/?nav=01HNFTE06J4QC24T0D5BPRYKMD) JS Web component

# 🌟 [1.141.0](https://github.com/SallaApp/theme-raed/compare/1.140.8...1.141.0) (19-03-2024)
### Features
- Support product specifications.

# 🔖 [1.140.8](https://github.com/SallaApp/theme-raed/compare/1.140.2...1.140.8) (13-02-2024)
### Features
- Support infinite scroll in the wishlist.
- Replace 'images/s-empty.png' asset to 'images/s-empty.png' CDN.
- Disable loading on the submit button and open the login modal directly if the user is a guest on the cart page.
### Bug Fixes
- Cover missed case of price update on the Product details page.
- Fix off-screen dropdown sub-menus.
  
# ⚡ [1.140.2](https://github.com/SallaApp/theme-raed/compare/1.140.0...1.140.2) (05-03-2024)
### Features
- Enhance best practices score for SEO.

# 🗳️ [1.140.0](https://github.com/SallaApp/theme-raed/compare/1.139.0...1.140.0) (15-02-2024)
### Bug Fixes
- Fix product option price with discount.

# 📜 [1.139.0](https://github.com/SallaApp/theme-raed/compare/1.138.7...1.139.0) (14-02-2024)
### Enhancements
- Enhance Cart options.

# 🔧 [1.138.7](https://github.com/SallaApp/theme-raed/compare/1.138.3...1.138.7) (11-02-2024)
### Features
- Twilight Upgrade, supporting Apple Pay with required shipping property.

# 🌐 [1.138.3](https://github.com/SallaApp/theme-raed/compare/1.138.0...1.138.3) (08-02-2024)
### Features
- Card options feature released.

<!-- # 🔥 [1.137.43](https://github.com/SallaApp/theme-raed/compare/1.137.39...1.137.43) (07-02-2024)
### Added
- Release New Feature: Card options. -->

# 🔄 [1.137.39](https://github.com/SallaApp/theme-raed/compare/1.8.0...1.137.39) (29-01-2024)
### Features
- Start of documentation
