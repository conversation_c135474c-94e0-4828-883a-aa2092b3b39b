This endpoint returns the price of a product. It only needs the id of the product whose price is on need.

## Payload

<DataSchema id="1427476" />

## Response
<Tabs>
  <Tab title="Success">
      
<DataSchema id="1427477" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>

## Usage
To perform the action of getting a product's price, the developer may call the method `getPrice()` along with the product's id.

#### Simple Product

```js
salla.product.getPrice({
    id: 1234,
    quantity: 1,
    notes: "please i need to get the red color"
}).then((response) => {
    /* add your code here */
});
```


#### Variable product

```js
salla.product.getPrice({
    id: 1234,
    quantity: 1,
    options: {
      117414452: 11232214, // option value id (select choice)
      117414412: 11232514, // option value id (select choice)
    },
    notes: "please i need to get the red color"
}).then((response) => {
    /* add your code here */
});
```

## Events
This endpoint may trigger two events, the onPriceUpdated and onPriceUpdateFailed events.

### onPriceUpdated
This event is triggered when getting a product's price is done without having any errors coming back from the backend.

```js
salla.event.product.onPriceUpdated((response) => {
  console.log(response)
});
```
### onPriceUpdateFailed
This event is triggered when getting a product's price is not completed and an error has occurred.

```js
salla.event.product.onPriceUpdateFailed((errorMessage) => {
  console.log(errorMessage)
});
