A coupon is a code that consists of a special series of characters, or string, used by customers to get a discounted price or limited offer on the cart's items. This *add* endpoint is used for this purpose.

## Payload


<DataSchema id="1427417" />

## Response
<Tabs>
  <Tab title="Success">
      
<DataSchema id="1427410" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>


## Usage
To perform the action of applying a coupon to the cart's items, the developer may call the `add()` as shown below.


```js
salla.cart.addCoupon({ coupon: "Free_Shipping") }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.cart.addCoupon('Free_Shipping').then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onCouponAdded and onCouponAdditionFailed events.

### onCouponAdded
This event is triggered when applying a new coupon by the customer is done without having any errors coming back from the backend.

```js
cart.event.cart.onCouponAdded((response) => {
  console.log(response)
});
```
### onCouponAdditionFailed
This event is triggered when applying a new coupon by the customer is not completed and an error has occurred.

```js
salla.event.cart.onCouponAdditionFailed((errorMessage) => {
  console.log(errorMessage)
});
