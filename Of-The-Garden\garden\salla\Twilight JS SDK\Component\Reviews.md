This endpoint retrieves reviews from various sources such as store, products and more.

## Payload



<DataSchema id="3663867" />

## Response


<Tabs>
  <Tab title="Success">

<DataSchema id="3663617" />
  </Tab>
</Tabs>

## Usage

The method `getReviews` takes in 3 parameters: The maximum number of items to return per page `per_page`, The review's source, `type`, which could be one of the allowed enum values, as well as `items`, which is the response data.

```js
salla.api.component.getReviews({ per_page :5, type: "product", items:"testimonials"})
```

## Events

The menu process may trigger two events during the menu process, reviewsFetched and reviewsFetchFailed.

### reviewsFetched

This event will be triggered when the reviews are successfully fetched.

```js
salla.event.component.reviewsFetched((response) => {
  console.log(response)
});
```

### reviewsFetchFailed

This event will be triggered if there is an error during the fetching process.

```js
salla.event.component.reviewsFetchFailed((errorMessage) => {
  console.log(errorMessage)
});
```