This endpoint is used to update a customer's contact information. The profile contains information such as the customer's `phone`, `country_code`, and `email`.

## Payload `authenticated`

<DataSchema id="1427940" />


## Response
<Tabs>
  <Tab title="Success">


<DataSchema id="1427941" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To update the contact informtion for the customer, the developer may call the methos `updateContact()` along with the customer's new information as below.

```js
salla.profile.updateContacts({
  "phone": "59874654",
  "country_code": "966",
  "email": "<EMAIL>"
}).then((response) => {
  /* add your code here */
});
```

### Verification
An additional endpoint is required to be used here for handling the verification process. This is to ensure the user's confirmation of the changes. It functions similarly to the [login verification](https://docs.salla.dev/doc-422620?nav=01HNFTDZPB31Y2E120R84YXKCX) endpoint. 

The `verification` status will change if the profile's phone, country code, or email are modified. It takes the values pending and success as inputs. The pending state implies that the user must confirm the modifications by an OTP code issued to his phone or email. The success status indicates that the profile update was completed successfully and that the user does not need to take any additional action.

This endpoint sends the entered access code to the backend and waits for a response. If an affirmative response is received, the profile is updated. If the verification process doesn't work, the customer is told to send the right access code again.

<!-- theme: success -->
> 💡 **Tip:**
> The *profile verify* endpoint has been implemented in the [Verify](https://docs.salla.dev/doc-422620?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, , and It's all setup to save developer's time and effort.

```mermaid
sequenceDiagram
    autonumber
    Customer->>Profile Update: Enters email or phone to update
    Profile Update->>Backend: Posts payload with email/phone, and requests sending access code
    Backend-->>Customer: Gets access code for the customer
    loop Access code check
        Customer->>Profile Update: Enters access code
    end
    Profile Update->Backend: Sends the recived access code
    Backend->Backend: Verifies the recived access code
    Backend->>Profile Update: Sends verification response
    alt wrong access code
        Profile Update->>Customer: The profile update has failed.
    else correct access code
        Profile Update->>Customer: Success with profile updates.
    end
```

An additional endpoint is required to be used here for handling the verification process. This is to ensure the user's confirmation of the changes. It functions similarly to the [login verification](https://docs.salla.dev/doc-422620?nav=01HNFTDZPB31Y2E120R84YXKCX) endpoint. 

This endpoint sends the entered access code to the backend and waits for a response. If an affirmative response is received, the profile is updated. If the verification process doesn't work, the customer is told to send the right access code again.

The `salla.profile.verify()` passes the customer access code to the backend in order to proceed with the verification process. In the case of using the phone number method to receive the access code, this method will pass the received access code along with the customer's phone number and the country code.


#### With web componenet


in case the user change the phone/email a OTP required to complete the changes, you can take advancige of `salla-verify-modal` to hanlde the OTP verifation

```html
<script type="javascript">
salla.profile.updateContacts({
  "phone": "5555555",
  "country_code": "966",
  "email": "<EMAIL>"
}).then((response) => {
  /* add your code here */

  // in case the mobile/email has been change
  // a event will disaptch to `salla-verify-modal` to show 
  // and colloct the OTP and complete the verifation process
});
</script>

<salla-verify-modal></salla-verify-modal>
```

<!-- theme: success -->
> 💡 **Tip:**
> The *profile verify* endpoint has been implemented in the [Verify](doc-422620?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, , and It's all setup to save developer's time and effort.

#### Without web componenet


```js
salla.profile.updateContacts({
  "phone": "5555555",
  "country_code": "966",
  "email": "<EMAIL>"
}).then((response) => {
  /* add your code here */

  // in case the mobile/email has been change
  // a otp required to complete the changes 
  if(response.data.verification.status === 'pending') {
    // you have to colloct the OTP from the user and submit it again like this
    let payload = {
      type: response.data.verification.type, 
      code: '1111' // the OTP from the customer form
    };

    if(payload.type === 'phone') {
      payload.phone = response.data.phone.number;
      payload.country_code = response.data.phone.country;
    } else {
      payload.email = response.data.email;
    }

    salla.profile.verify(payload).then((response) => {
      // phone/email has been changed
    }).catch((error) => {
      // OTP incorrect
    });
  }
});
```


## Events
This endpoint may trigger two events, which are the `onVerificationCodeSent`and `onUpdateContactsFailed` events.

### onVerificationCodeSent
This event may happen will be triggered when the verification process fails and the backend sends error codes. In other words, the received response status is not 200.
```js
salla.event.profile.onVerificationCodeSent((errorMessage) => {
  console.log(errorMessage)
});
```

### onUpdateContactsFailed
This event is triggered when updating the contact information for the customer is not completed and an error has occurred.

```js
salla.event.profile.onUpdateMobileFailed((errorMessage) => {
  console.log(errorMessage)
});
```


