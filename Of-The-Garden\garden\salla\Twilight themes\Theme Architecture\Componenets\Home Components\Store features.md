This **_pre-defined component_** is responsible for showcasing the store features such as payment methods, shipping methods and so on.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── store-features.twig
          ...
```

### Example
<!--
focus: false
-->
![Store Features](https://cdn.salla.network/docs/twilight/4/pages-components-home-store-features-01.png)

### Variables


<DataSchema id="1383706" />


### Usage 
This component is a simple component that takes the content of the store features as `icon`, `title`, and `text`. Then a **for-loop** to display each feature.

```php lineNumbers
{% for item in items %}
    <i class="{{ item.icon }}"></i>
    <h4>{{ item.title }}</h4>
    <p>{{ item.text }}</p>
{% endfor %}
```
:::tip[Educational Clip]
<Video src="https://youtu.be/nm0ja6rTqpo?si=nKZWYI6dTi86xyPl"></Video>
:::

