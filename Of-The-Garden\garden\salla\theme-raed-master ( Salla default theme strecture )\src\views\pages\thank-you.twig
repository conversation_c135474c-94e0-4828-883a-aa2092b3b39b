{#
| Variable            | Type   | Description                            |
|---------------------|--------|----------------------------------------|
| page                | object |                                        |
| page.title          | string |                                        |
| page.slug           | string |                                        |
| order               | Order  | @see pages/customer/orders/single.twig |
| thank_you           | string |                                        |
| share_message       | string |                                        |
| short_share_message | string |                                        |
#}
{% extends "layouts.master" %}

{% block content %}
    {% hook 'thank-you:start' %}
    <div class="h-52 -mb-52 w-full bg-primary rtl:right-0 ltr:left-0 relative -z-1">
        <svg class="text-primary absolute top-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
            <path class="fill-current" fill-opacity="1"
                  d="M0,96L120,112C240,128,480,160,720,165.3C960,171,1200,149,1320,138.7L1440,128L1440,0L1320,0C1200,0,960,0,720,0C480,0,240,0,120,0L0,0Z"></path>
        </svg>
    </div>
    <div class="container mb-20">
        <nav class="w-full py-5">
            <ol class="list-reset flex items-center text-sm text-primary-reverse">
                <li><a href="{{ link('/') }}" class="fix-align text-primary-reverse">{{ trans('common.titles.home') }}</a></li>
                <li><i class="sicon-keyboard_arrow_left mx-2"></i></li>
                <li><span class="fix-align">{{ thank_you_title }}</span></li>
            </ol>
        </nav>
        <div class="bg-white p-4 md:p-8 rounded-md mb-6 md:mb-8">
            <div class="flex flex-col items-center pt-16 pb-10">
                <div class="rtl:-scale-x-100">
                  <img src="{{ 'images/delivery-bro.svg' | asset }}" alt="{{ thank_you_title }}"
                     class="thanks-item opacity-0 w-40 mb-8">
                </div>
                <h1 class="font-bold text-lg mb-2.5 thanks-item opacity-0">{{ thank_you_title }}</h1>
                <p class="thanks-item opacity-0 text-gray-500 max-w-xs text-center text-sm leading-6 mb-6">
                    {{ trans('pages.thank_you.order_id') }}
                    <salla-button class="relative" onclick="app.copyToClipboard(event)" color="dark" shape="link" data-content="{{ order.reference_id }}">
                        <span>#{{ order.reference_id }}</span>
                        <i class="copy-icon sicon-swap-stroke pointer-events-none rtl:mr-1 ltr:ml-1"></i>
                    </salla-button>
                </p>
                <salla-button onclick="salla.order.show({order_id:{{ order.id }}, url:'{{ order.url }}'})">
                  <i class="sicon-newspaper rtl:ml-1 ltr:mr-1"></i>
                  {{ trans('pages.thank_you.order_details') }}
                </salla-button>
            </div>

            {% if order.instructions %}
                <article class="article article--main article--small text-center">
                    {{ order.instructions|raw }}
                </article>
            {% endif %}

            {% for message in messages %}
                <div class="flex flex-col items-center pt-1 pb-10 justify-start rtl:space-x-reverse space-x-2">
                    <span class="font-bold">{{ message }}</span>
                </div>
            {% endfor %}

            {% hook 'thank-you:items.start' %}

            {% hook 'thank-you:items.end' %}
        </div>

        <div class="lg:flex rtl:space-x-reverse lg:space-x-8">
            {% if order.email_sent %}
                <div class="duration-500 hover:shadow-default flex-1 bg-white p-8 rounded-md mb-6 md:mb-10 flex flex-col items-center justify-center">
                    <div class="bg-gray-100 rounded-icon w-12 h-12 mb-2.5">
                        <i class="sicon-newspaper-alt text-xl"></i>
                    </div>
                    <p class="text-sm mb-2.5">{{ trans('pages.thank_you.email_sent') }}</p>
                    <b class="text-sm">{{ order.customer.email }}</b>
                </div>
            {% else %}
                <div id="invoice-form" class="thankyou-block">
                    <div class="bg-gray-100 rounded-icon w-12 h-12 mb-2.5">
                        <i class="sicon-newspaper-alt text-xl"></i>
                    </div>
                    <p class="text-sm mb-2.5">{{ trans('pages.thank_you.resend_email') }}</p>
                    <form onsubmit="return salla.form.onSubmit('order.sendInvoice', event)">
                        <div class="flex max-w-3xl w-full">
                            <input type="email" class="form-input rtl:rounded-l-none ltr:rounded-r-none rtl:border-l-0 ltr:border-r-0 !w-[160px] sm:!w-[220px]" placeholder="<EMAIL>" required name="email">
                            <salla-button type="submit" loader-position="center" class="rtl:rounded-r-none ltr:rounded-l-none lg:w-24 h-10">
                              <span class="btn__text">{{ trans('blocks.comments.submit') }}</span>
                            </salla-button>
                        </div>
                    </form>
                </div>
            {% endif %}

            <div class="thankyou-block">
                <div class="bg-gray-100 rounded-icon w-12 h-12 mb-2.5">
                    <i class="sicon-mail-letter text-xl"></i>
                </div>
                <p class="text-sm mb-2.5">{{ trans('common.titles.support') }}</p>

                <div class="flex rtl:space-x-reverse space-x-6">
                    {% if store.contacts.mobile %}
                        <div class="text-sm unicode flex items-center">
                            <i class="sicon-phone"></i>
                            <a href="tel:{{ store.contacts.mobile }}" class="hover:text-primary transition"><b
                                        class="unicode mx-1.5">{{ store.contacts.mobile }}</b></a>
                        </div>
                    {% endif %}

                    {% if store.social.whatsapp %}
                        <div class="text-sm unicode flex items-center">
                            <i class="sicon-whatsapp"></i>
                            <a href="{{ store.contacts.whatsapp }}" class="hover:text-primary transition"><b
                                        class="unicode mx-1.5">{{ store.social.whatsapp }}</b></a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% hook 'thank-you:end' %}
    </div>
{% endblock %}

{% block scripts %}
    <script defer src="{{ 'checkout.js' | asset }}"></script>
{% endblock %}
