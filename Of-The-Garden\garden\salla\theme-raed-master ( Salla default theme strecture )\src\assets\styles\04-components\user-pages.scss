.single-order-header-item{
  @media (max-width: 768px) {
    @apply flex justify-between items-center;
  }
}

.order-file {
  .s-button-text {
    @apply flex gap-2
  }
}

/* tags */
.tag {
  @apply px-3 pt-0.5 pb-1 inline-block text-sm rounded-2xl border border-gray-200;

  &--primary {
    @apply text-primary border-primary;
  }
}

/* Profile Header section */
.profile-header {
  @apply relative lg:h-48 overflow-hidden;

  .breadcrumbs {
    @screen lg {
      @apply mt-2.5;
    }

    a {
      @apply text-primary-reverse hover:opacity-80;
    }

    span {
      @apply text-primary-reverse opacity-80;
    }

    .arrow {
      @apply text-primary-reverse opacity-70;
    }
  }
}

.s-file-upload-profile-image .s-file-upload-has-error .filepond--file-status{
  @apply ltr:-left-1/2 rtl:right-1/2;
}


.rating-header {
  @apply relative from-primary to-primary-d p-5 text-primary-reverse rounded-md;
}

/* Profile additional fields */
.form--user-profile{
  .s-file-upload-wrapper{
    min-height: 120px;
    .filepond--list-scroller{
      overflow-y: inherit !important;
    }
    .filepond--drop-label {
      min-height: 120px !important;
      @apply bg-white cursor-pointer border-dashed rounded-md border border-gray-200;
    }
    .profile-filepond-placholder{
      @apply flex flex-col justify-center items-center;
      &-icon {
        @apply mb-1;
        i {
          color: #7c8082;
          @apply text-xl;
        }
      }
    }

  }
}


/* 
* thank you page 
*/
.thankyou-block{
  @apply duration-500 hover:shadow-default flex-1 bg-white p-8 rounded-md mb-6 md:mb-8 flex flex-col items-center justify-center;

  &.sent{
    @apply text-green-500 font-bold;

    &:before{
      @apply w-16 h-16 bg-green-100 text-green-500 flex items-center justify-center text-2xl rounded-full mb-2;
      content: "\ea9d";
      font-family: 'sallaicons';
    }
  }
}


/* 
* Order rating actions
*/

.rating-actions{
  @apply flex items-center gap-2;
  salla-button {
    button {
      @apply rounded mx-0 w-8 h-8 #{!important}
    }
  }
}

/* 
* Blog single page
*/
.blog-single .s-comments-item-user-info-name{
  @apply mx-0
}