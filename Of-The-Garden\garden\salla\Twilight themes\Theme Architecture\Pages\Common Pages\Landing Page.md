A `landing page template` for a Twilight theme is a pre-suggested web page that serves as the entry point for the online store. It provides a foundation for developers to build upon  and typically includes an offer with a countdown timer to encourage conversions. It should be visually appealing, user-friendly, and align with your goals and target audience.

**Following is the page location and url:**

```shell title = "🌐 Page URL: http://www.store-domain.com/"
└── src
    ├── views
      ├── pages
      |   ...
      |   ├── landing-page.twig
      |   ...
      ...
```

## Example 
<!--
focus: false
-->
![Landing Page](https://cdn.salla.network/docs/twilight/4/landing-page-01.png?v=1-10-2022)


## Variables 


<DataSchema id="1383864" />


## Components 
Besides extending the master layout to show the common header and footer, this page may display different sort of Product components such us [`home.store-features`](https://docs.salla.dev/doc-422587?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), [`home.testimonials`](https://docs.salla.dev/doc-422584?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), [`components.home.fixed-products`](https://docs.salla.dev/doc-422583?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), and [`components.home.products-slider`](https://docs.salla.dev/doc-422590?nav=01HNFTD5Y5ESFQS3P9MJ0721VM):

```php
{% component 'home.store-features' %}
{% component 'home.testimonials' with{type:landing.testimonials_type} %}
{% include 'components.home.fixed-products' %}
{% include 'components.home.products-slider' %}
```


## JS Web Component

The Landing Page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores:

- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Count Down [`<salla-count-down>`](https://docs.salla.dev/doc-422701?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Products Slider [`<salla-products-slider>`](https://docs.salla.dev/doc-422722?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Products List [`<salla-products-list>`](https://docs.salla.dev/doc-422719?nav=01HNFTE06J4QC24T0D5BPRYKMD)

## Usage 
This page template receives the `landing` object, which contains all of the information related to the landing page. If this object was not received, the developer may show the 404 not found error page:

```php lineNumbers
{% if not landing %}
<div>
  <header>
    <div><a href="{{ store.url }}"><img src="{{ store.logo }}" alt="logo" /></a>
      <h1>{{trans('common.errors.404')}}</h1>
      <div>{{trans('common.elements.back_home')}}</div>
    </div>
  </header>
</div>
```

If the `landing` object has been received, the developer may check different thing such as if the Landing Page offer `is_expired` or not:

```php lineNumbers
{% if landing.is_expired %}
<div>
  <header>
    <div><a href="{{ store.url }}"><img src="{{ store.logo }}" alt="logo" /></a>
      <h1>{{trans('pages.offer.offer_finished')}}</h1>
      <p>{{ trans('pages.offer.offer_expired_message') }}</p>
      <div>{{trans('common.elements.back_home')}}</div>
    </div>
  </header>
```

On the other hand, if the `landing` object has been received and there is ongoing offer, the developer would need to set the values of the following in order to start showing the related products:

```php
{% set products=landing.products %}
{% set hide_add_btn=true %}
{% set show_quantity=landing.show_quantity %}
{% set title=trans('pages.offer.included_products') %}
```

Besed on the above, the developer may show the products as follows:

```php lineNumbers
{% if landing.is_slider %}
  {% include 'components.home.products-slider' %}
{% else %}
  {% include 'components.home.fixed-products' %}
{% endif %}
```