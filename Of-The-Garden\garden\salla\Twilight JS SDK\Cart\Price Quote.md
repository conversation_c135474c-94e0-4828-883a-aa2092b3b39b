The priceQuote endpoint calculates and returns a price quote for the items in a cart. This endpoint takes into account any discounts, taxes, shipping costs, and other factors that may affect the final price, and returns the calculated price along with a breakdown of the individual costs. The price quote can be used to inform the user of the total cost of their purchase and to facilitate the checkout process.

## Payload

<DataSchema id="1427456" />


## Response
<Tabs>
  <Tab title="Success">
    
<DataSchema id="1427457" />
      
  </Tab>
   <Tab title="Error">
 
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of calculating and returning a price quote for the items in a cart, the developer may call the `priceQuote()` as shown below.

```js
salla.cart.priceQuote({cart_id: 5432}).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onPriceQuoteSucceeded and onPriceQuoteFailed events.

### onPriceQuoteSucceeded
This event is triggered when of calculating and returning a price quote for the items in a cart is done without having any errors coming back from the backend.

```js
ssalla.cart.event.onPriceQuoteSucceeded((response) => {
  console.log(response)
});
```
### onPriceQuoteFailed
This event is triggered when of calculating and returning a price quote for the items in a cart is not completed and an error has occurred.

```js
salla.cart.event.onPriceQuoteFailed((errorMessage) => {
  console.log(errorMessage)
});
```