This endpoint returns the current status of a cart, including whether it is active or not, and the next step to take. The next step can be either to refresh the cart, prompt the user to login, or proceed to checkout, and may include an optional URL. This endpoint is used for monitoring and managing the status of a cart during the checkout process.

## Payload


<DataSchema id="1427312" />


## Response
<Tabs>
  <Tab title="Success">

     
<DataSchema id="1427313" />
  </Tab>
   <Tab title="Error">

    
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of returning the current status of a cart, the developer may call the `status()` as shown below.

```js
salla.cart.status({cart_id: 5432}).then((response) => {
  /* add your code here */
});
```
