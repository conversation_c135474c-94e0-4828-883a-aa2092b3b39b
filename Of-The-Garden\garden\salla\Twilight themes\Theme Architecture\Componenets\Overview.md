In general, _components_ are independent and reusable bits of code. **Twilight** theme defines a number of _components_ in [`src/views/components/`](https://github.com/SallaApp/theme-raed/tree/master/src/views/components) that have properties with strict typing and default values. A property is only required if you mark it required. You can set default values otherwise.

## 📙 What you'll learn
This article lists _all_ of the components shipped with the Twilight theme engin.

:::tip[Tip]
 The developer has the flexibility of developing and including his own components. This mean that the developer can create new files for any desired new component he may need to add to his theme.
:::

## Components 
Twilight main pages include many components that aim to deliver the best shipping experience for the end user.
In this article we will walk you through the main pages' components, which are:

||||||
|---|--|----|---|---|
|[Home components](#home-components)|[Header components](#header-components)|[Footer components](#footer-components)|[Products components](#products-components)|[Comments component](#comments-component)|



### Home Components 
Home components [`src/views/components/home/<USER>//github.com/SallaApp/theme-raed/tree/master/src/views/components/home) are found in the home page and it's significant in giving the store front- display. Each component plays a role in exhibiting the store main details such as, Youtube videos of the store, customer testimonials and other components that sets the store tone. More on home components and it's usage in home page [here](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

#### Home Components Location

```shell
└── src
  ├── views 
    ├── components
    |     ...
    |     ├──── home
    |     ...
    ...
```
Home components are: 
- [Youtube](https://docs.salla.dev/doc-422582?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Fixed banner](https://docs.salla.dev/doc-422583?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Testimonials ](https://docs.salla.dev/doc-422584?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- Random testimonials
- [Parallax background](https://docs.salla.dev/doc-422585?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Photos slider](https://docs.salla.dev/doc-422586?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Store features](https://docs.salla.dev/doc-422587?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Fixed products](https://docs.salla.dev/doc-422589?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Products slider](https://docs.salla.dev/doc-422590?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Featured products style 1](https://docs.salla.dev/doc-422591?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Featured products style 2](https://docs.salla.dev/doc-422592?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [Featured products style 3](https://docs.salla.dev/doc-422593?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- [latest products](https://docs.salla.dev/doc-422599?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
- Vertical menue with slider
### [Header Components](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
The header components [`src/views/components/header/`](https://github.com/SallaApp/theme-raed/tree/master/src/views/components/header) are found on all pages of the store and considered to be used frequently so for ease of access it's postioned on the top area of the page. More about Header components [here](https://docs.salla.dev/doc-422601).
#### [Header Components Location](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

```sh
└── src 
    ├── components
    |     ...
    |     ├──── header
    |     ...
    ...
```

Header components are :
- Header
- Advertisement
- Breadcrumbs
- Main Menu
- User Menu


### [Footer Components ](https://docs.salla.dev/doc-422602?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

Footer components [`src/views/components/footer/`](https://github.com/SallaApp/theme-raed/tree/master/src/views/components/footer) are also commonly used and usually found in the footer area of the page, such are contacts and payment methods. More about Footer components [here](https://docs.salla.dev/doc-422602?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

#### Footer Components Location
```sh
└── src 
    ├── components
    |     ...
    |     ├──── footer
    |     ...
    ...
```
Footer components are:
- Footer
- Contacts
- Copyrights
- Mobile Apps
- Pages
- Payment Methods
- Social

### Products Components 

Products components [`src/views/components/product/`](https://github.com/SallaApp/theme-raed/tree/master/src/views/components/product) are grouped into 2 groups:

- Essentials
- Options

##### Products Components Location
```sh
└── src 
    ├── components
    |     ...
    |     ├──── products
    |     ...
    ...
```

#### [Essentials](https://docs.salla.dev/doc-422604?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)
Essentials components are the main components related to the product, more about Essentials components [here](https://docs.salla.dev/doc-422604?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

Essentials components are: 
  - Donation progress bar
  - Offer
  - Notify availability
  - Quick access
  - Similar products


<!--#### [Offer](4.2.4-Product-components/4.2.4.02-Offers-Components.md)
Offer components are focused on the offers related tothe products, more about Offer components [here](4.2.4-Product-components/4.2.4.02-Offers-Components.md).
Offers components are:
  - Offer
  - Offer-popup
-->
#### [Options](https://docs.salla.dev/doc-422605?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

These components help to add more options to the product components to make it  versatile by using color options also, adding images using image option can give the used a better view of the product. More about Options components [here](https://docs.salla.dev/doc-422605?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

Options components are:
  - Color
  - Date
  - Datetime
  - Donation
  - Image
  - Multiple Options
  - Number
  - Single Option
  - Splitter
  - Text
  - Textarea
  - Thumbnail
  - Time

### [Comments component](https://docs.salla.dev/doc-422603?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

Comment component is used to display comments made by useres on [Single page](https://docs.salla.dev/doc-422578?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) or [Single product page](https://docs.salla.dev/doc-422561?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). More about Comments component [here](https://docs.salla.dev/doc-422603?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

##### Comments component location 

``` shell
└── src 
    ├── components
    |   ...
    |   └── Comments      
    |   ...
    ...

```

