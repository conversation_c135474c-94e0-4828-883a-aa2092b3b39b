The product page template consists of several essential components that give a look at the product details. For example, the product's brands, similar products, and product tags components.

In this article, we will explore the essential components for every product template page.

**Following is the location of the header components:**

```shell
└── src 
  ├── views
    ├── components
    |   ...
    |   └── product
    |   |   ├── donation-progress-bar.twig
    |   |   ├── offer.twig
    |   |   └── similar-products.twig                            
    |   ...
    ...
```


Following are the essential components which can be used within product page template:
- [Donation Progress Bar](#donation-progress-bar)
- [Offer](#offer)
- [Similar Products](#similar-products)

<hr/>

## Donation Progress Bar
The donation progress bar is used to show a donation's amount up to date, as well as how much time is left in which a user can contribute towards the fundraising campaign.

### Example
<!--
focus: false
-->
![Essentials Components](https://cdn.salla.network/docs/twilight/4/pages-components-products-essentials-01.png)



### Variables


<DataSchema id="1384771" />


### Usage

The `donation` object contains all of the donation related values and messages. For example;

- `product.donation.collected_amount`: shows the collected amount up to date.
- `product.donation.target_amount`: shows the targeted amount to be collected.
- `product.donation.target_percent`: shows the percentage of the targeted amount.

```js
{% if product.donation.target_message %}
    <span>{{ product.donation.target_message }}</span>
{% else %}
    <div class="...">
        <span>{{ product.donation.collected_amount|money }}</span>
        <span>{{ product.donation.target_amount|money }}</span>
    </div>
    <div class="...">
        <div class="..." style="width: {{ product.donation.target_percent }}%">
        </div>
    </div>
    {% if product.donation.target_end_date %}
        <small class="...">{{ pages.products.donation_target_date }} {{ product.donation.target_end_date|date }}</small>
    {% endif %}
{% endif %}
```

## Offer 
This component shows a slider of products that have special offers. In this way, the customer is able to check all of the offers related to the currently being viewed product in one place.

### Example
<!--
focus: false
-->
![Footer](https://cdn.salla.network/docs/twilight/4/pages-components-products-essentials-02.png)


### Variables


<DataSchema id="1384784" />

### Usage
For this component, the main variable is the `offer` object. Using this component, the developer can show both `offer.name` and `offer.description`. It contains a list of the related products that include offers. Using _for-loop_ statement such as `{% for product in offer.products %}` , the list of the offered products can be retrieved and displayed using a slider with cards, for example. 

```js
<div>
    <p>{{ offer.name }}: {{ offer.description|raw }}</p>
    {% if offer.products|length %}
        <div>
            <div>
                {% for product in offer.products %}
                    <div>
                        {% include 'pages.partials.product.card' %}
                    </div>
                {% endfor %}
            </div>

        </div>
    {% else %}
        {% for category in offer.categories %}
            <a href="{{ category.url }}">
                <span>{{ category.name }}</span>
            </a>
        {% endfor %}
    {% endif %}
    <div>
        <div> Special Offer' </div>
    </div>

</div>
```
<hr/>


## Similar Products
This component displays a list of similar and related products to the currently beviewed product. This is to assist the customers in discovering and buying products that best meet their needs. Showing similar products helps to find complementary products to the ones the customer is viewing, `Cross-selling`, or offer a better and more expensive alternative product, `Upsells`.


### Example
<!--
focus: false
-->
![image](https://cdn.salla.network/docs/twilight/4/pages-components-products-essentials-03.png)

### Variables


<DataSchema id="1384787" />


### Usage
In order to display the list of similar products, a for-loop is used to iterate through the `products` objects, and then display the information for each product in the list.

```js
{% for product in products %}
<div class="...">
    {% include 'pages.partials.product.card' %}
</div>
{% endfor %}
```





