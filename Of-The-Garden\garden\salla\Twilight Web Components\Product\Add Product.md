The `<salla-add-product-button>` web component allows controllability over button text labels and behaviours based on the `product-status` and `product-type` properties. It consists of [Product Availability](https://docs.salla.dev/doc-422717?nav=01HNFTE06J4QC24T0D5BPRYKMD) component and [Button](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component.

:::tip[Note]
Available API Endpoints for the Add Product component are:

- [Product Availability](https://docs.salla.dev/doc-422642?nav=01HNFTE06J4QC24T0D5BPRYKMD)
:::

## Example

<!--
focus: false
-->

![Add Product Example](https://cdn.salla.network/docs/twilight/6/js-web-add-to-cart-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Add Product Button component usage -->
<salla-add-product-button width="wide" product-id="2233214522">
  Add to Cart
</salla-add-product-button>
```      
  </Tab>
    
<Tab title="JS">
        
```js
const AddProductButton = document.querySelector('salla-add-product-button');

AddProductButton.addEventListener('failed', event => {
  salla.logger.warn('Add to cart api request failed', event);
})

AddProductButton.addEventListener('success', event => {
  salla.logger.warn('Add to cart api request done', event);
})
```      
  </Tab>  
    <Tab title="SASS">

```css
:host {
  display: block;
}

salla-add-product-button[width=wide] {
  width: 100%;
}
```      
  </Tab>  
  
</Tabs>

## Properties

| Property        | Attribute         | Description                                                                                                                                                                                                                              | Type                                                                                         | Default     |
| --------------- | ----------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------- | ----------- |
| Channels        | `channels`        | Available notification channels                                                                                                                                                                                                          | `string`                                                                                     | `null` |
| Donating Amount | `donating-amount` | Donation amount value                                                                                                                                                                                                                    | `number`                                                                                     | `'0'`       |
| Notify Options Availability | `notify-options-availability` | Listen to Product Options availability.                 | `boolean`                         
| Product ID      | `product-id`      | Product ID under which the merchant can sign up for an availability notice                                                                                                                                                               | `any`                                                                                        | `undefined` |
| Product Status  | `product-status`  | Product status based on the allowed type values.                                                                                                                                                                                         | `"out" \| "out-and-notify" \| "sale"`                                                        | `'sale'`    |
| Product Type    | `product-type`    | Product type based on the allowed type values.                                                                                                                                                                                           | `"codes" \| "digital" \| "donating" \| "food" \| "group_products" \| "product" \| "service"` | `'product'` |
| Quantity        | `quantity`        | Passing custom quantity number to be injected within the component, which affects `sale` product statuses as the [Quantity Input](doc-422724?nav=01HNFTE06J4QC24T0D5BPRYKMD) component will be customized with the number passed. | `number`                                                                                     | `'0'`       |
| Quick Buy                  | `quick-buy`                   | Support for the Quick Pay Button                                | `boolean`                                                                                                 | `undefined` |
| Subscribed Options         | `subscribed-options`          | Shows the Subscribed Options ex: "[[139487,2394739],[1212,1544]]" | `string`                                                                                                  | `undefined` |
| Support Sticky Bar          | `support-sticky-bar`          | Support for themes that have a sticky bar                   | `boolean`                                                                                                 | `undefined` |

:::tip[Note]
- The Product Status `out` refers to the product being out of sale where a disabled button is labeled as "Sold Out". The `out-and-notify` type is where the [Product Availbility component](https://docs.salla.dev/doc-422717?nav=01HNFTE06J4QC24T0D5BPRYKMD) takes place as Merchants are allowed to subscribe when the specific product becomes available.
- The Product Status `sale` is where the specified product is currently under sale which will allow for a [Button component](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) to appear and be labeled as "Add to Cart".
- Each Product Type customizes how certain button labels appearance. For instance, if `product-type` is set to `donating`, then the label `Donate` will appear when `product-status` is set to `sale`.
:::


## Events

| Event     | Description                                                                      | Type               |
| --------- | -------------------------------------------------------------------------------- | ------------------ |
| `failed`  | This event will be fired when the Add Product to the cart fails    | `CustomEvent<any>` |
| `success` | This event will be fired when the Add Product to the cart succeeds | `CustomEvent<any>` |


:::tip[Fast Checkout Feature]

Salla has made buying easier by adding several payment options directly to the product details page. 
Now, customers can complete their transactions using most common payment methods without needing to go to a different page. This approach aims to provide a seamless and convenient shopping experience in Salla Merchants’ stores for their customers.

The payment interface is displayed in a pop-up modal, enabling customers to:
- View cart summary,  price details, including discounts, shipping costs, and more.
-  Apply / remove discount coupons.
- Select an existing delivery address or add a new one.
- Choose a preferred shipping company.
- Pay using one of four different payment methods, which are:
    • Apple Pay
    • Credit cards (Visa, MasterCard, or Mada)
    • Installment plans (Tabby and Tamara)
    • Cash on delivery
:::

:::caution[Alert]
This component is not allowed to be customized by the developer and can only be called to the Theme. Only three properties can be customized using their CSS variables, height, width, border-radius:

```
salla-mini-checkout-widget {  
--salla-fast-checkout-button-height: 2.5rem;
  --salla-fast-checkout-button-width: 100%;
  --salla-fast-checkout-button-border-radius: 0.375rem;
}
```
:::


