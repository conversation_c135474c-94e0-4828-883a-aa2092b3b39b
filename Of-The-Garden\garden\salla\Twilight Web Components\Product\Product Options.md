The `<salla-product-options>` web component is used to show to the Merchant all the fields that are customizable to curate the experience of personalizing a product prior to ordering it. Read more details on the proper use of each element of the component from [here](https://docs.salla.dev/doc-422605?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

:::tip[Note]
Available API Endpoints for the Product Options component are:

- [Product Details](https://docs.salla.dev/doc-422649?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Upload Gift Image](https://docs.salla.dev/doc-422648?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::


## Example

<!--
focus: false
-->

![Product Options](https://cdn.salla.network/docs/twilight/6/js-web-product-options-01.jpg?v=1-10-2022)

## Usage

<Tabs>
  <Tab title="HTML">
 
      
   ```html
<!-- Basic Product Options component usage in the Product Page - The product object will be given by default -->
{% if product.options|length %}
  <salla-product-options
    options="{{ product.options }}"
    product-id="{{ product.id }}">
  </salla-product-options>
{% endif %}
```
    
  </Tab>
    
  <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-product-options` class. Following is a complete source code for customizing this component:

```css
.s-product-options{
  &-wrapper{

  }
  &-option-container{

  }
  &-option{

  }
  &-option-label{

  }
  &-option-content{

  }
  &-colors-wrapper{

  }
  &-date-element{

  }
  &-time-element{
    
  }
  &-datetime-element{

  }
  &-image-input{

  }
  &-multiple-options-wrapper{

  }
  &-splitter{

  }
  &-text{

  }
  &-textarea{

  }
  &-thumbnails-wrapper{

  }
}
```      
  </Tab>  
  
</Tabs>



## Properties

| Property   | Attribute    | Description                                                  | Type     | Default                       |
| ---------- | ------------ | ------------------------------------------------------------ | -------- | ----------------------------- |
| Options    | `options`    | Product detail information.                                  | `string` | `undefined`                   |
| Product ID | `product-id` | The Product ID to which the all options will be fetched for. | `number` | `salla.config.get('page.id')` |

## Events

| Event                        | Description                                                             | Type                    |
| ---------------------------- | ----------------------------------------------------------------------- | ----------------------- |
`changed`              | This event will be fired when the product options is changed.             | `CustomEvent<any>`      |

:::info[Information]
The `changed` event in the `salla-product-options` component, contains some data about the changed options, which is explained in the following structure and example.
:::

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `getOption` which gets a specific option by its ID.

| Method   | Description             | Return Type            |
| -------- | ----------------------- | ---------------------- |
| `getOption(option_id: any)` | Gets a specific option by its ID.  | `Promise<Option>` |
| `getSelectedOptions()` | Gets all selected options. | `Promise<any[]>` |
| `getSelectedOptionsData()` | Gets the IDs of the selected options. | `Promise<{}>` |
| `hasOutOfStockOption()` | Returns a `true` value if there is any out of stock options that are selected and vise versa. | `Promise<boolean>` |
| `reportValidty()` | Reports the options' form validty. | `Promise<boolean>` |


## JSON Code Structure

#### Structure


| Name     | Description                                            |
| -------- | ------------------------------------------------------ |
| `event`  | The native change event for each option               . |
| `option` | The entire changed option data                        . |
| `detail` | The specific item that has been selected in the option. |


#### Example

<details>

  <summary> <tt>detail</tt> </summary>
  <code>{id: 123456789, name: 'XL', additional price: 0, option_value: null, image: null, ...}</code> 

  </details>

  <details>
  <summary><tt>event</tt> </summary>
  <code>{isTrusted: true, type: 'change', target: select.s-form-control, currentTarget: null, eventPhase: 0 ...}</code> 

  </details>

  <details>
  <summary><tt>option</tt> </summary>
  <code>{id: 1234567899, name: "المقاس", required: true, type: 'single-option', placeholder: 'اختر' ...}</code> 

</details>

