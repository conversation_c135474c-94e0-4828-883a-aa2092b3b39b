The `<salla-quick-order>` web component allows customers to request a call from the store owner for assistance with making their order. Customers provide their contact information through this component and the store owner is notified of the request. The store owner can then contact the customer directly to provide assistance with their order. This web component is an easy and convenient way for customers to receive help and support from the store owner, improving the overall customer experience. 
It may consist of one or more components, such as [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD), [`<salla-modal>`](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD), [`<salla-tel-input>`](https://docs.salla.dev/doc-422739?nav=01HNFTE06J4QC24T0D5BPRYKMD) and more.

<!-- Available API Endpoint for the Quick Buy component is:

- [Get Quick Checkout Setting]() -->

## Example

<!--focus: false -->
![Quick Order Order](https://cdn.salla.network/docs/twilight/6/js-web-quick-order-01.png?v=1-10-2022)


## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Quick Order component usage -->
<salla-quick-order
  is-email-required="true"
  product-id="207865447"
  quick-order-style="white">
</salla-quick-order>
```      
  </Tab>
<Tab title="SASS">
 

This JS web component can be targeted for styling by its `.s-quick-order` class. Following is a complete source code for customizing this component:

```css
.s-quick-order {
    .s-quick-order-container {
        padding: 15px;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        background: #f3f3f3;

        &.gray {
            background: #f3f3f3;
            border: 1px solid #d0d0d0;
        }

        &.white {
            background: #fff;
            border: 1px solid #dfdfdf;
        }

        .s-quick-order-button-cont {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
        }

        .s-quick-order-checkbox-container {
            display: flex;
            margin: 10px 0;

            input {
                padding: 10px;
                width: 20px;
                border: 1px solid #eee;
                border-radius: 5px;
                margin: 6px;
            }
        }

        .s-quick-order-expandable {
            visibility: hidden;
            height: 0;
            transition: height 1s ease;

            &.show {
                visibility: visible;
                height: auto;
            }
        }

        input {
            padding: 10px;
            width: 100%;
            border: 1px solid #eee;
            border-radius: 5px;
        }
    }
}
```

    
  </Tab>    
</Tabs>



## Properties

| Property                 | Attribute                  | Description                                                                | Type      | Default                                                        |
| ------------------------ | -------------------------- | -------------------------------------------------------------------------- | --------- | -------------------------------------------------------------- |
| Agreement Text           | `agreement-text`           | Shows an agreement text from server or from props                          | `string`  | `salla.lang.get(     'pages.checkout.show_full_agreement'   )` |
| Confirm Pay Button Title | `confirm-pay-button-title` | Displays a confirmation button text of the "quick order confirm pay" label | `string`  | `'اشتر الآن'`                                                        |
| Is Email Required        | `is-email-required`        | Whether or not the email is required                                       | `boolean` | `false`                                                        |
| Pay Button Title         | `pay-button-title`         | Customizes the "pay title" button context                                  | `string`  | `'اطلب المنتج'`                                                        |
| Product ID               | `product-id`               | Fetches the Product ID from either local or from a specific page           | `string`  | `undefined`                                                    |
Quick Order Style        | `quick-order-style`        | Styles the Quick Order component                                           |`\| "default" \| "grey" \| "white" \|` | `'default'`    
| Quick Order Title        | `quick-order-title`        | Customizes the Quick Order title text                                      | `string`  | `'ليش تنتظر؟'`                                                      |
| Sub Title                | `sub-title`                | Customizes the Quick Order sub title text                                  | `string`  | `'احصل على المنتج مباشرة الآن'`                                                  |
| Thanks Message           | `thanks-message`           | Displays a Thank You message after the purchase is completed               | `string`  | `undefined`                                                    |


## Events

| Event                | Description                                                       | Type               |
| -------------------- | ----------------------------------------------------------------- | ------------------ |
| `quickOrderSubmited` | This event will be fired when the order gets submitted successfully. | `CustomEvent<any>` |
