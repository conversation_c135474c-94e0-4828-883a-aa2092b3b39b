Using the [Partners Portal](https://salla.partners/), the developer has the ability to manage the theme's setup. This includes managing the theme's basic information, screenshots, settings, features, custom components, and price. Besides that, the developer can `preview`, `delete`, `publish`, or *withdraw* any theme.

## 📙 What You'll Learn

In this article, you will learn how to setup a theme using the [Partners Portal](https://salla.partners/). We will also go through the following:

- [Setting up twilight.json](#setting-up-twilight.json)
- [Setup via the Partners Portal](#setup-via-the-partners-portal)

<hr>

## Setting up twilight.json

:::info[About twilight.json]
The Theme's setup can be modified directly from the [twilight.json](https://docs.salla.dev/doc-421921?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) file, which is synced in the [Github](https://github.com/) account connected with the [Partners Portal](https://salla.partners/). Explore more on the **twilight.json** file by reading this [guide](https://docs.salla.dev/doc-421921?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
:::


Upon [creating / importing a theme](https://docs.salla.dev/doc-421877?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), the [twilight.json](doc-421877?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) file will be existed in the root directory. Within that file, a developer is able to specify Theme Features, which are the [pre-defined](https://docs.salla.dev/doc-421921?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#theme-features) theme components, and Theme Components, which are the [custom](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#components) components that are built by the developer. As changes are made to the file, they can be easily synced in the [Github](https://github.com/) account connected to the [Partners Portal](https://salla.partners/). Explore more about *twilight.json* [here](https://docs.salla.dev/doc-421918?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

## Setup via the Partners Portal
On the [Salla Partners Portal](https://salla.partners) dashboard, the developer needs to click on *My Themes* in the main navigation menu. This will show the list of available themes. The developer needs to first select a theme and then start with the setup process.

There are two parts for Theme setup which are Basic Settings and Listing Information



![](https://cdn.salla.network/docs/twilight/1/theme-set-up-01.png?=ve)

## Basic Settings

The Basic Settings will be explained in the coming sections and Listing Information will be further explained in the Theme Publish [article](https://docs.salla.dev/doc-421880?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).



#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">1</span> Basic Information
To start click on edit Theme Name to edit Theme name and category
<!--focus: false-->

![image](https://cdn.salla.network/docs/twilight/1/theme-set-up.png)
<br>

Here you can also update basic information about the Theme, such as Theme icon, name and category.

<!--focus: false-->

![image](https://cdn.salla.network/docs/twilight/1/theme-set-up-011.png?=ve)


#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">2</span> Theme Settings

Personalize the experience via the Theme Settings section by adding custom fields and parameters, which allow for significant control over the Theme.

<!--
focus: false
-->
![Settings Page](https://cdn.salla.network/docs/twilight/1/set-up-theme-12new.png)

In there, the drag & drop four fields (String, Numeric, Boolean, and List) bring a unified, straightforward experience for Salla Merchants. A fifth field is Collection, which allows for creating a group of fields that has a mix of the four fields.

<!--
focus: false
-->
![Settings Page Details](https://cdn.salla.network/docs/twilight/1/set-up-theme-13new.png)



#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">3</span> Theme Features

Mark the Theme features' checkboxes that make up the theme. That said, it is to be customizable from the Merchant side. Explore more about Theme Features [here](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#theme-features-and-theme-components).

<!--
focus: false
-->

![Theme Features](https://cdn.salla.network/docs/twilight/1/set-up-theme-14new.png)

#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">4</span> Extra Features

If the developed Theme provides extra features, add each of them in a separate line in the field available.
<!--
focus: false
-->
![Extra Features](https://cdn.salla.network/docs/twilight/1/set-up-theme-15new.png)


#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">5</span> Theme Components

Add a [custom component](doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#components) by clicking on "Add Custom Component". This kind of component is developed by the developer.

<!--
focus: false
-->
![Hi](https://cdn.salla.network/docs/twilight/1/set-up-theme-16new.png)

Provide the details of the custom component in terms of the title, icon, and file path of the component.
<!--
focus: false
-->
![Add Component](https://cdn.salla.network/docs/twilight/1/set-up-theme-17new.png)

That will result in having the [custom component](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#components) added.
<!--
focus: false
-->
![Result](https://cdn.salla.network/docs/twilight/1/set-up-theme-18new.png)

Edit and update the previously inputted basic information of the [custom component](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#components), such as the title, icon, and file path. The pop dialog will showcase the fields to be updated.
<!--
focus: false
-->
![Custom Component Basic Info](https://cdn.salla.network/docs/twilight/1/set-up-theme-19new.png)

By going to the Settings page of the [custom component](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#components), several setting fields can be added for that specific component.
<!--
focus: false
-->
![Settings Page](https://cdn.salla.network/docs/twilight/1/set-up-theme-20new.png)

The custom components setting page will appear as follows, where it is easy to add the fields that customize the component in a drag & drop way, each with its own properties and functionalities that will be reflected on the Merchant side.

<!--
focus: false
-->
![Settings Page Details](https://cdn.salla.network/docs/twilight/1/set-up-theme-21new.png)


#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">6</span> Theme Preview

The [Partners Portal](https://salla.partners) provides this feature to preview the themes in a demo store for quality assurance, before submitting a publication request. Explore what is suitable and enjoy the progress so far while developing the theme.

<!--
focus: false
-->
![Test & Preview Options](https://cdn.salla.network/docs/twilight/1/setup-theme-15.png?)

Create a new [demo store](https://salla.partners/demo-stores), unless one is already created, and click on the "Preview Theme".

<!--
focus: false
-->
![Preview Theme](https://cdn.salla.network/docs/twilight/1/setup-theme-16.png?)

On the demo dashboard, the theme will then be in the preview mode to which all the changes made to the theme, in terms of components, [pre-defined](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#theme-features-and-theme-components) and [custom](doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM#theme-features-and-theme-components), and settings, will be reflected.



:::tip[A thing to know!]
🖥️ [**Salla CLI**](https://github.com/SallaApp/Salla-CLI), which is a command-line tool developed by Salla team, can be used to [preview](https://docs.salla.dev/doc-422776?nav=01HNA8QHCPJTCY5VSEZ616JCAK) a theme.
:::



#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">7</span> Theme Details

Write details about the Theme to grab the attention of the Merchants, which will be apparent in the [Store Themes Marketplace](https://s.salla.sa/marketplace/themes/tag-all).
<!--
focus: false
-->
![](https://cdn.salla.network/docs/twilight/1/Theme-details.png)
#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">8</span> Theme Preview in Live Store

You can preview the theme on live stores by adding the store link then clicking on "sending the installation request" button.

<!--
focus: false
-->
![Theme Details](https://cdn.salla.network/docs/twilight/1/theme-set-up-live.png)

#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">9</span> Theme Installation

The developer can decide the Theme installation methods as follows:

a. By ticking this option, the devloper indicates that the theme is public an available for all stores.
b. By ticking this option,the theme is beta and under development.
c. By ticking this option, the theme is private and can only be installed by.installation link
d. In this box, the developer can copy the theme installation link for store to install it via installation link


![Live Store Request](https://cdn.salla.network/docs/twilight/1/setup-theme-18.png?eh)

By getting here you have successfully completed theme setup.