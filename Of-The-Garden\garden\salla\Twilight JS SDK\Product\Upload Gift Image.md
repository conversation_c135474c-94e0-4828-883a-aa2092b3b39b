This endpoint is used to upload a product image associated with the gifted product.

:::tip
The *upload gift image* endpoint has been implemented in the [Gifting](https://docs.salla.dev/doc-422705?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::

## Payload


<DataSchema id="1387242" />


## Response
<Tabs>
  <Tab title="Success">
      
<DataSchema id="1427796" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>


## Usage
To perform the action of uploading a product image associated with a gifted product, the developer may call the method `uploadGiftImage()` as follows.


```js
salla.product.uploadGiftImage({ multipartPayload: my_form }).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onGiftImageUploadSucceeded and onGiftImageUploadFailed events.

### onGiftImageUploadSucceeded
This event is triggered when uploading a product image associated with a gifted product is done without having any errors coming back from the backend.

```js
salla.product.event.onGiftImageUploadSucceeded((response) => {
  console.log(response)
});
```
### onGiftImageUploadFailed
This event is triggered when uploading a product image associated with a gifted product is not completed and an error has occurred.

```js
salla.product.event.onGiftImageUploadFailed((errorMessage) => {
  console.log(errorMessage)
});

