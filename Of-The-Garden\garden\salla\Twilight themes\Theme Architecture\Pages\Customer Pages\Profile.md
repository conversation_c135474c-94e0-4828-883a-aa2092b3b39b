This [`profile template page`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/profile.twig) is for the customer's profile, which is utilised to show customer-related information including, name, email and phone number. The customer can also modify and update their information on this page. 

``` shell title = "🌐 Page URL: http://www.store-domain.com/profile"
└── src 
  ├── views
  |   ├── pages
  |   |   ├── customer
  |   |   |   ...
  |   |   |   ├── profile.twig
              ...
```

### Example
<!--
focus: false
-->
![Customer Profile](https://cdn.salla.network/docs/twilight/4/customer-profile-01.png)

### Variables


<DataSchema id="1383861" />


### Components
This page extends the default layout [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), and accordingly, it takes the unified look-and-feel. For example, all of the [`header's`](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and [`footer's`](https://docs.salla.dev/doc-422602?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) components will be  added automatically to this page.

### JS Web Components
Customer's Profile page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores.
- Date Time Picker [`<salla-datetime-picker>`](https://docs.salla.dev/doc-422702?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Tel Input [`<salla-tel-input>`](https://docs.salla.dev/doc-422739?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- File Upload [`<salla-file-upload>`](https://docs.salla.dev/doc-422703?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- User Settings [`<salla-user-settings>`](https://docs.salla.dev/doc-422741?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Verify [`<salla-verify>`](https://docs.salla.dev/doc-422742?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The  [`profile template page`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/profile.twig) calls for the following [hooks](https://docs.salla.dev/doc-422552) in order to inject extra information. These hooks are mainly used for processing the user profile `<form>`. Similarly, to add a submit  button to the form, the developer may simply use [`salla-button`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component.

```php
{% hook 'customer:profile.form.start' %}
{% hook 'customer:profile.form.fields.start' %}
{% hook 'customer:profile.form.fields.end' %}
{% hook 'customer:profile.form.submit.start' %}
{% hook 'customer:profile.form.submit.end' %}
{% hook 'customer:profile.form.end' %}
```

### Usage
The customer's profile page receives all of the `User` model information, which is accessible automatically on this page because it's included in the [`master.twig`](https://docs.salla.dev/doc-421944?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) layout file. This information can be displayed as per the developer's needs. Finally, to verify the form's input, the [`salla-verify-modal`](https://docs.salla.dev/doc-422742?nav=01HNFTE06J4QC24T0D5BPRYKMD) component can be used.
The following is a full example for all of the above.

For example, here we display customer's first name:

```php
<p> Welcome back {{ user.first_name }}! </p>
```

This page includes a form, by which the customer's profile can be updated. Below is an example for that. Note that the developer may utilize the [`salla-tel-input`](https://docs.salla.dev/doc-422739?nav=01HNFTE06J4QC24T0D5BPRYKMD) component to display the user's mobile field. 

```php lineNumbers=true
<form onsubmit="return salla.form.submit('profile.update')">
    <div>
        <label for="first-name"> {{ trans('pages.profile.first_name') }} </label>
        <input name="first_name" required="" type="text" value="{{ user.first_name }}"/>
    </div>
    <div>
        <label for="last-name"> {{ trans('pages.profile.last_name') }} </label>
        <input name="last_name" required="" type="text" value="{{ user.last_name }}"/>
    </div>
    <div>
        <label for="birthday"> {{ trans('pages.profile.birthday') }} </label>
        <input name="birthday" required="" type="text" value="{{ user.birthday }}"
               placeholder="{{ trans('pages.profile.birthday_placeholder') }}"/>
    </div>
    <div>
        <label for="email"> {{ trans('common.elements.email') }} </label>
        <input name="email" required="" type="email" value="{{ user.email }}"/>
    </div>

    <div>
        <label for="gender"> {{ trans('pages.profile.gender') }} </label>
        <select name="gender" required="">
            <option value="">
                {{ trans('pages.profile.gender_placeholder') }}
            </option>
            <option value="male">{{ trans('pages.profile.male') }}</option>
            <option value="female">{{ trans('pages.profile.female') }}</option>
        </select>
    </div>

    <div>
        <label for="international-mobile">
            {{ trans('common.elements.mobile') }}
        </label>
        <salla-tel-input mobile="{{ user.mobile }}"></salla-tel-input>
    </div>

    <salla-button type="submit" loader-position="end" class="w-full">
        {{ trans('common.elements.save') }}
    </salla-button>
</form>
{# its required in case the customer change his mobile when submit the form #}
<salla-verify-modal></salla-verify-modal>
```








