The [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig) is the default "layout" which comes with Twilight theme and applies it on all of the theme's pages. It calls many of the main [global variables](https://docs.salla.dev/doc-421938?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). This is to set the main look-and-feel settings for the theme. Below is its location inside the "layouts" folder:

```shell title="src\views\layouts\master.twig"
└── src
  ├── views
  |   ├── layouts
  |   |   ...
  |   |   ├── master.twig
          ...
```

## 📙 What you'll learn

By the end of this article, you will learn about:
- Global variables
- The main blocks of the [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig) layout
- Example of using [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig) layout

<hr>

## Global Variables
The [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig) file is considered the file that sets the shared layout and look-and-feel of the whole website. As a result of that, the developer may call within this master view any [global variable](https://docs.salla.dev/doc-421938?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) or theme setting variable. These values will be used on all of the pages that extend this layout. 

The theme settings variables are part of the [`twilight.json`](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) file as we can see in the [theme settings](https://docs.salla.dev/doc-421879?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) section. For example, we have a setting defined as `topnav_is_dark`. The developer may use any of the following methods to retrieve, get, its value: 


```js
...
{{ theme.settings.get("topnav_is_dark") }}
...
```
<hr>

## Main Blocks

The developer has the option to create a new theme's layout. However, it's essential to be inspired by the default master.twig layout file because it shows the main blocks that the developer should include with any new main layout.

The default [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig) layout file includes predefined blocks  used by any page that extends this layout. These blocks are:

- Styles Block: used in pages to inject the needed css-style.
```js
{% block styles %}{% endblock %}
```

- Head Block: used in pages to inject the needed code to be added into the `<head>` section.

```js
{% block head %}{% endblock %}
```

- Content Block: used in pages to inject the needed code to be added into the <body> section.

```js
{% block content %}{% endblock %}
```

- Script Block: used in pages to inject the needed js-script.

```js
{% block scripts %}{% endblock %}
```
<br/>

In addition to the above requirements, it is a must to add some [hooks](https://docs.salla.dev/doc-422552) blocks to the master layout. In general, hooks are Twig tags that can have content injected into the Twilight theme. For example, the following are hooks responsible for adding the SEO-related meta data to the page header section:


```js
<head>
...
{% hook 'head:start' %}
...
{% hook 'head' %}
...
{% hook 'head:end' %}
    
</head>
```

## Usage

By exploring the [`src/views/layouts/master.twig
`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/index.twig) file, which is the [Home Page](https://docs.salla.dev/doc-422558) is of the theme, we can see the following:

```js lineNumbers
{% extends "layouts.master" %}
{% block content %}
    {% component home %}
{% endblock %}
{% block scripts %}
    <script type="text/javascript" defer src="{{ home.js |asset('dist/home.js') }}"></script>
{% endblock %}
```

- Line #1: extending the "layouts.master", which is [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig).
- Lines #2 to #4: injecting the `{% component home %}` inside the block `{% block content %}`. This mean that all of the home-releadted components will be insterted inside the block content as per the layout design of [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/layouts/master.twig).
- Lines #5-#7: injecting a javascript codes to the page inside this block.


<!---
## Customization
The [`master.twig`](https://github.com/SallaApp/theme-raed/blob/master/views/layouts/master.twig) layout is considered The file that sets the main values used within the main layout. For example, the theme language, colors, fonts, etc. Below is a part of the example code:

```js
{{ theme.set('topnav_bg', '') }}
{{ theme.set('topnav_text_color', '') }}
{{ theme.set('topnav_link_hover', '') }}
{{ theme.set('topnav_bg_gradient', false) }}
{{ theme.set('topnav_bg_gradient_from', '#0093E9') }}
{{ theme.set('topnav_bg_gradient_to', '#80D0C7') }}
    

{{ theme.set('mainnav_is_dark', false) }}
{{ theme.set('mainnav_bg', '') }}
{{ theme.set('mainnav_text_color', '') }}
{{ theme.set('mainnav_link_hover', '') }}
```
-->





