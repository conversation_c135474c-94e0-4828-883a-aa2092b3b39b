The `<salla-product-size-guide>` web component is used to enable the merchant to add product measurements of height, weight, depth and other metrics for the customer to visualize the actual product size in real life. The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `open` and `close` the Product Size Guide component.

:::tip[Note]
Available API Endpoints for the Product Size Guide component are:

- [Get Size Guides](https://docs.salla.dev/doc-422651?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::


## Example

<!--focus: false -->

![Product Size Guide Image](https://cdn.salla.network/docs/twilight/6/js-web-product-size-01.png)

## Usage

<Tabs>
  <Tab title="HTML">

      
```html
<!-- Basic Product Size Guide for a specific product -->
<salla-button
  shape="link"
  color="primary"
  onclick="salla.event.dispatch('size-guide::open', '1153090815')"
>
  Show Size Guide
</salla-button>

<!-- product size guide modal -->
<salla-product-size-guide></salla-product-size-guide>
```
      
  </Tab>
  
</Tabs>



## Methods
The pre-defined `methods` allow for calling functions built by <PERSON>la to carry out certain actvities, such as `close` which hides the size-guide pop-up modal window. 


| Method                     | Description                              | Return Type            |
| -------------------------- | ---------------------------------------- | ---------------------- |
| `close()`                  | Hides the size-guide pop-up modal window | `Promise<HTMLElement>` |
| `open(product_id: number)` | Show the size-guide pop-up modal window  | `Promise<any>`         |

## Slots
The`slots` makes it customizable to modify certain labels, such as `footer`.
| Slot       | Description                                                      |
| ---------- | ---------------------------------------------------------------- |
| `footer` | The bottom section of the component. Value is "empty" by default |
| `header` | The upper section of the component. Value is "empty" by default  |
