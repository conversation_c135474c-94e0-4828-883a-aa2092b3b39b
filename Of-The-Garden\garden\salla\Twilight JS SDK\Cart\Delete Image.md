This endpoint removes an image file attached to an item already added to the cart by the customer.

## Payload

<DataSchema id="1387202" />


## Response

<Tabs>
  <Tab title="Success">

<DataSchema id="1427405" />

  </Tab>
   <Tab title="Error">


<DataSchema id="1427314" />
      
  </Tab>
  
</Tabs>

## Usage

To delete an image file attached to an item already added to the cart, the developer may call the method `deleteItem` as follows:

```js
salla.cart.deleteItem({ file_id: 12345} ).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.cart.deleteItem(12345).then((response) => {
  /* add your code here */
});
```



## Events
This endpoint may trigger two events, the onImageDeleted and onImageNotDeleted events.

### onImageDeleted
This event is triggered when removing an image file attached to an item already added to the cart is done without having any errors coming back from the backend.

```js
salla.cart.event.onImageDeleted((response) => {
  console.log(response)
});
```

### onImageNotDeleted
This event is triggered when removing an image file attached to an item already added to the cart is not completed and an error has occurred. For example, the id of the image file was not found.

```js
salla.cart.event.onImageNotDeleted((errorMessage) => {
  console.log(errorMessage)
});
```