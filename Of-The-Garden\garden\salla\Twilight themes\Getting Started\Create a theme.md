Now that you have the necessary skills to begin creating fantastic [**Twilight**](https://github.com/SallaApp/theme-raed) themes for [Salla's Store](https://salla.sa/), you can move on to the next phase and construct your theme.


:::info[A thing to know!]
- [Salla Partners Portal](https://salla.partners) and [Salla CLI](https://github.com/SallaApp/Salla-CLI) are two alternatives tools provided by Salla for creating and setting up Twilight Themes for the developers.
- [Salla CLI documentation](project-451700?nav=01HNA8QHCPJTCY5VSEZ616JCAK) provides a full guide for Salla CLI commands.
:::


## 📙 What you'll learn

By the end of this article, you will have successfully created a theme using [Salla Partners Portal](https://salla.partners). 

Creating a Theme via [Salla Partners Portal](https://salla.partners), includes:
  - [Prerequisites](#prerequisites)
  - [Initiating a theme](#initiating-a-theme)
  - [Connecting with GitHub](#connecting-with-GitHub)
    - [Create a new theme](#create-a-new-theme)
    - [Import an existing theme](#import-an-existing-theme)

<hr>

## Creating Theme via Salla Partners Portal

Developers can use the [Salla Partners Portal](https://salla.partners/) to integrate their themes with Salla's [eCommerce Platform](https://salla.sa/). It gives them the ability to create custom themes for Salla Merchants by giving them a set of tools to help them explore their creativity.

By using this tool, developers will be able to build big projects on the platform in a simple, organized, and monitored way. Learn more about Salla Partners Portal by visiting this [blog](https://salla.dev/blog/welcome-to-salla-partners-portal/).

### Prerequisites
- [Salla partner account](https://salla.partners/) account.
- [Github](https://github.com/) account, to sync the theme's files that are being developed with the [Salla partner account](https://salla.partners/).

### Initiating a theme
On [Salla Partners Portal](https://salla.partners/) dashboard, click on *My Themes* on the main navigation menu. The page will be redirected to the theme management page. From there, click on *Create your first theme*. If you have already created your first theme, click on the *Create theme* button.
<!--
focus: false
-->
![Salla Partners Portal](https://cdn.salla.network/docs/twilight/1/create-theme-01.png?=)


:::info[A thing to know!]

[**Salla CLI**](https://github.com/SallaApp/Salla-CLI), is a command-line tool developed by Salla team, can be used to [create](https://docs.salla.dev/doc-422775?nav=01HNA8QHCPJTCY5VSEZ616JCAK) Salla themes.
:::

### Connecting with GitHub
This step is crucial in order for **Twilight** to access a [GitHub](https://github.com/) account and start crafting the theme's files there, it needs to verify the developer's GitHub identity. This requires going through the authorization process in order to authorize interactions with the data on GitHub on the developer's behalf. 
<!--
focus: false
-->
![GitHub](https://cdn.salla.network/docs/twilight/1/create-theme-02.png?v)

After setting the [GitHub](https://github.com/) authorization, the page will give two options:

- Create a New Theme
- Import a Theme

Both methods will be explained respectively. 


#### Create a new theme
By clicking on *Create theme* button, the theme setting file will be created. More about theme's files [here](https://docs.salla.dev/doc-421918?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

<!--
focus: false
-->
![Create theme options](https://cdn.salla.network/docs/twilight/1/create-theme-03.png?=N)
 
Next, fill in the new theme basic information:

<br>

<!--
focus: false
-->
![Theme information](https://cdn.salla.network/docs/twilight/1/create-theme-04.png?n)

Then, click on *Create theme*. The table below explains the new theme details required in the above photo.

|||
|--|--|
|1- Theme icon for the new theme| Icon is an image that describes your theme.|
|2- Name| The name of the theme|
|3- Theme Category (optional)|This option allows the theme to be categorized in the available categories for ease of access by merchants. |
|4- Theme website (optional)| This option provides the theme website for the merchant to display the theme.|
|5- Support email|Email address to support the theme.|


🎉 **Well done!** You have successfully created a theme using *create theme* on [Salla Partners Portal](https://salla.partners/). 


Next, we'll create a theme by importing it.


#### Import an existing theme
The other method for creating a theme from the [Salla Partners Portal](https://salla.partners) is to import an existing theme that already exists in the developer's GitHub account. This method will detect the theme setting file [twilight.json](doc-421921?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and craft the new theme based on it. Here is a complete article about the [twilight.json](doc-421921?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) file.

On the theme management page click on *import theme* button.

<!--
focus: false
-->
![Import theme](https://cdn.salla.network/docs/twilight/1/create-theme-05.png?)

<br/>

Next, fill in these details:


<!--
focus: false
-->
![Import information](https://cdn.salla.network/docs/twilight/1/create-theme-06.png?=i)

Then, click on *import theme*. The following table explains the information required from the developer to create the theme using import theme option.
|||
|--|--|
|1- Icon for the imported theme| Icon is an image that describes your theme.|
|2- The Github account |The GitHub account linked to the partners account. |
|3- The theme repository |Imported theme repository, from the developer's remote GitHub account. |
|4- Theme Categories| The Theme category |
|5- Theme screenshots (optional)| Images of the theme, up to three images.|

🎉**Congratulations!** You have successfully created a theme using *import theme* on [Salla Partners Portal](https://salla.partners/).

<!--
:::note[]
**Next**, we'll go through the [**Theme Development**](https://docs.salla.dev/doc-421878?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) workflow in detail.
:::
-->
<br/>