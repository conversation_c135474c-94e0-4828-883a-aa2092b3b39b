The [`loyalty page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/loyalty.twig) shows the content of the  loyalty program, which is meant to enable the store's customers to benefit from collecting the points for vouchers and offers provided by the store. Once they have collected enough points, the customers will be eligible to redeem them for exciting gifts or products.

**Following is the page location and url:**

```shell title = "🌐 Page URL: http://www.store-domain.com/loyalty/oKgyXG"
└── src
    ├── views
      ├── pages
      |   ...
      |   ├── loyalty.twig
      |   ...
      ...
```
<br/>

### Example
<!--
focus: false
-->
![Thank you](https://cdn.salla.network/docs/twilight/4/loyalty-page-01.png?v=1-10-2022)


### Variables


<DataSchema id="1383865" />


### Components
Besides extending the master layout to show the common header and footer, this page starts by displaying the `breadcrumbs` component. The `{% component breadcrumbs %}` line returns the current navigation for the user:

```php
{% component 'header.breadcrumbs'%}
```

### JS Web Components
The Loyalty page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores:

- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Loyalty [`<salla-loyalty>`](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD)
- Slider [`<salla-slider>`](https://docs.salla.dev/doc-422735?nav=01HNFTE06J4QC24T0D5BPRYKMD)


### Usage
This page template receives the [`loyalty`](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD) object, which contains all of the information related to the loyalty program, such as its name and description. The developer may show the collected points of a logged in user by using the variable `user.loyalty_points`.

```php lineNumbers
<div>
  <span>{{ loyalty.name }}</span>
</div>
<div>
  <h1>{{loyalty.name}}</h1>
  <p>
    {{loyalty.description}}
  </p>
  
  {% if user.loyalty_points %}
    <p>{{user.loyalty_points}}</p>
  {% endif %}
</div>

```

A _for-loop_ statement can be used to display a list of the possible methods for collecting points as follows:

```php lineNumbers
{% for point in loyalty.points %}
<div>
    <i class="{{ point.icon ? point.icon : 'sicon-star' }}" style="color: {{point.color}}"></i>
    <div style="background-color: {{point.color}}"></div>
</div>
<div>
    <h4 style="color: {{point.color}}">
        {{point.points}}
        <p>{{point.description}}</p>
</div>
{% endfor %}
```

In case of the method for collecting points is to here a link, the developer may show the sharing link as follows:

```php lineNumbers
{% if point.type == 'share' %}
<h3>{{ point.url }}</h3>
```

After all, the developer can display the list of prizes using the following _for_ loop statement, noting that another nested loop can be used to display any awarded products:

```php lineNumbers
{% for prizeGroup in loyalty.prizes %}
<div>
  <h2>{{ prizeGroup.title }}</h2>
</div>
{% for prize in prizeGroup.items %}
  <div>
    <div>
      {% if prize.url %}
      <a href="{{ prize.url }}"> {{ prize.image }} </a>
      {% endif %}
      <div>
        {% if prize.url %}
        <a href="{{ prize.url }}">{{ prize.name }}</a> 
        {% else %} 
          {{ prize.name}} 
        {% endif %}
      </div>
      <p>{{ prize.description }}</p>
      {% if user.type == 'user' %}
        <div>{{ prize.cost_points }}</div>
      {% endif %}
    </div>
  </div>
{% endfor %} 
{% endfor %}
```

:::tip[Educational Clip]
<Video src="https://youtu.be/uJrmfvLnl38?si=NfOdAxIWPbTwQ1TU"></Video>
:::

