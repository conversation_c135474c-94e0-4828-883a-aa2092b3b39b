By default, themes created with **Twilight** include a footer section. The footer section includes  components such as the `contacts`, `payment-methods`, `social media links`, and many more.

**Following is the location of the footer components:**

``` shell
└── src 
    ├── components
    |   ...
    |   └── Footer                   
    |       ├── footer.twig
    |       ├── contacts.twig
    |       ├── mobile-apps.twig
    |       ├── menu.twig
    |       ├── payment-methods.twig          
    |       └── social.twig            
    |   ...
    ...

```

## Footer Components Example
In the following example we can see that the footer includes:
- [Footer](#footer)
- [Contacts](#contacts)
- [Mobile Apps](#mobile-app)
- [Menu](#menu)
- [Payment Methods](#payment-methods)
- [Social](#social-links)

<!--
focus: false
-->
![Footer Components](https://cdn.salla.network/docs/twilight/4/pages-components-footer-01.png)

<hr/>

## Footer
This part is the footer's main component, which embeds several parts, such as `contacts`, `payment-methods`, and `social`.

```php title="Call Command"
{% component "footer.footer" %} 
```

### Usage
In general, the footer section is a container for all of the footer-related components. These element components can easily be called there as per the developer style and design. Below is an example of including these components.

```php lineNumbers
<div>
    <a href="{{ link('/') }}">
        <img src="{{ store.logo }}" alt="{{ store.name }}">
    </a>
    {% if store.description %}
        <p>
            {{ store.description|raw }}
        </p>
    {% endif %}
    {% component 'footer.social' %}

    {% if store.settings.tax.number %}
        <div>
            {% if store.settings.tax.certificate %}
                <a onclick="document.querySelector('#modal-value-tax').show()" href="#!"
                   alt="{{ store.settings.tax.number }}">
                    <img src="{{ 'images/tax.png' | cdn }}" alt="value added tax"/>
                </a>
            {% endif %}
            <div>
                <p>{{ trans('common.elements.tax_number') }}</p>
                <b>{{ store.settings.tax.number }}</b>
            </div>
        </div>

        {% if store.settings.tax.certificate %}
            <salla-modal sub-title-first sub-title="{{ trans('common.elements.tax_number') }}"
                         modal-title="{{ store.settings.tax.number }}" id="modal-value-tax">
                <div>
                    <img src="{{ store.settings.tax.certificate }}" alt="{{ store.settings.tax.number }}"/>
                </div>
            </salla-modal>
        {% endif %}
    {% endif %}
</div>

{% component 'footer.pages' %}
{% component 'footer.contacts' %}
{% component 'footer.mobile-apps' %}
{% hook copyright %}
{% component 'footer.payment-methods' %}
```
:::tip[Educational Clip]
<Video src="https://youtu.be/bEKtudlK_ts?si=tz5kiik4GdmUkfwD"></Video>

:::

<hr/>

## Contacts
This component shows the "contact us" details provided by the store owner. These details can be a WhatsApp contact number, a mobile or phone number, a Telegram channel, or even an email address.

```php title="Call Command"
{% component "footer.contacts" %} 
```

### Variables


<DataSchema id="1383641" />


### Usage
This component receives an array for `contacts`. Each option in the array represents a specific contact detail. The developer can use a *loop* statement to display all of the contacts details. Below is an example of that.

```php lineNumbers
{% if is_header %}
    {% for contact in contacts|filter(contact => contact.type in ['email', 'phone']) %}
        <a href="{{ contact.url }}">
            {% if contact.type == 'email' %}
                {{ contact.value }}
            {% else %}
                <span>{{ trans('blocks.footer.social') }}:</span>
                <span>{{ contact.value }}</span>
            {% endif %}
        </a>
    {% endfor %}
{% else %}
    <h3>{{ trans('blocks.footer.social') }}</h3>
    {% for contact in contacts %}
        <a href="{{ contact.url }}">
            <i class="{{ contact.icon }}"></i>
            <span>{{ contact.value }}</span>
        </a>
    {% endfor %}
{% endif %}
```

<hr/>

## Mobile-app
This component lists the links of the mobile applications of the store. For example, it may show the store's iOS application link in the App Store.

```php title="Call Command"
{% component "footer.mobile-apps" %} 
```

### Variables


<DataSchema id="1383659" />


### Usage
This component receives an array of `apps`, and then display each element using a *for-loop* statement.

```php lineNumbers
<h3>{{ trans('blocks.footer.download_apps') }}</h3>
<ul>
    {% for app in apps %}
        <li>
            <a href="{{ app.url }}" rel="noreferrer" target="_blank">
                <img src="{{ app.name }}.png" alt="{{ app.name }}"/>
            </a>
        </li>
    {% endfor %}
</ul>
```

<hr/>

## Menu 
In this section of the footer, we list a group of links to some internal pages. For example, a link to the "Privacy Policy" page.

```php title="Call Command"
{% component "footer.menu" %} 
```

### Variables


<DataSchema id="1383658" />

### Usage
This component receives an array of pages as `items`, and then display each element using a *for-loop* statement.

```php lineNumbers
{% if is_header %}
    {% if items|length %}
        {% for key, item in items|slice(0,3) %}
            <a href="{{ item.url }}">{{ item.title }}</a>
        {% endfor %}
    {% endif %}
{% else %}
    <h3>{{ trans('blocks.footer.pages_links') }}</h3>
    <div>
        {% for item in items %}
            <a href="{{ item.url }}">{{ item.title }}</a>
        {% endfor %}
    </div>
{% endif %}
```

<hr/>

## Payment-methods 
This component lists the available payment methods provided by the store. For example, "Cash On Delivery" or/and "Credit Cards".

```php title="Call Command"
{% component "footer.payment-methods" %}
```

### Variables


<DataSchema id="1383662" />


### Usage 
This component receives an array of `payment_methods`, and then display each element using a *for-loop* statement.

```php lineNumbers
<ul>
    {% for method in payment_methods %}
        <li>
            <img src="{{ ('images/payment/'~ method ~'_mini.png') | cdn }}" alt="{{ method }}" />
        </li>
    {% endfor %}
    {% if store.social.maroof %}
        <li>
            <a href="{{ store.social.maroof }}" target="_blank" rel="noreferrer">
                <img src="{{ 'images/maroof-footer.png' | cdn }}" alt="maroof" />
            </a>
        </li>
    {% endif %}
</ul>
```

<hr/>

## Social Links
This component lists all of the social media links provided by the store. For example, Instagram, Twitter, Snapchat, Tiktok, Youtube, Facebook, and Pinterest links.

```php title="Call Command"
{% component "footer.social" %}
```

### Variables


<DataSchema id="1383663" />



### Usage
This component receives an array of `links`, and then display them our using a *for-loop* statement.
```php lineNumbers
<ul>
    {% for link in links %}
        <li>
            <a href="{{ link.url }}" title="link.name" aria-label="{{ link.name }}">
                <i class="sicon-{{ link.type }}"></i>
            </a>
        </li>
    {% endfor %}
</ul>
```


