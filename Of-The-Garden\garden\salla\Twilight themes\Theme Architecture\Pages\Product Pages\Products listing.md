Twilight Theme includes a [`products listing template page`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/product/index.twig). This page template is based on a category or search query. It is created to produce better products listings based on a specific need or purpose set by the developer. 
This page is a key feature for a better customer experience as it funnels site visitors to product detail pages and closer to conversion.

Following is the page location and url:

``` shell title = "🌐 Page URL: http://www.store-domain.com/products-listing-slug/c1825487583"
└── src
    ├── views
      ├── pages
      |  ├── product
      |    ...
      |    ├── index.twig
      |    ...
    ...
```

In the Twilight, this page template shows a list of products based on pre-defined filters, which are:

- **Category:** shows products that are from the same category, for example, women's tops, men's shoes, and sports wear. 

- **Offers:** displays products that have special offers, such as buy two for the price of one or get 60% off the second item.

- **Tags:** used to display the products that have been tagged with the same "tag". Product tags are keywords for product identification, so that they are easier to find. For example, for apparel products, users may create tags for T-shirt products, such as "t-shirt", "cotton", "polo", etc.

-  **Search results:** displays the search results based on the user's manual search using special keywords.

### Example

<!-- focus: false -->
![Products listing](https://cdn.salla.network/docs/twilight/4/pages-product-listing-01.png)

### Variables


<DataSchema id="1383872" />

### Components

The product listing page includes the [breadcrumbs component](https://docs.salla.dev/doc-422601?nav=01HNFTE06J4QC24T0D5BPRYKMD). Breadcrumbs are a set of links that indicate the current page and its "ancestors" leading back to the site's homepage.

```php
{% component 'header.breadcrumbs' %}
```

In addition, this page template includes the [testimonials](https://docs.salla.dev/doc-422584?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) component. This component helps to display the testimonials the developer chooses to display.

```php
{% component 'home.testimonials' %}
```
### JS Web Components
Product Listing page may include the following [JS Web Components](https://docs.salla.dev/doc-422580?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores.

- Products List [`<salla-products-list>`](https://docs.salla.dev/doc-422719?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Hooks
The `products listing template page` calls for the following [hooks](https://docs.salla.dev/doc-422552?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) in order to inject extra information.

```php
{% hook 'product:index.items.start' %}
{% hook 'product:index.items.end' %}
```

### Usage
This page template receives an object `products` that contains all of the products that need to be listed on this page. As we explained before, these products have a common category, offers, tags, or search result. 

Initially, the developer can display the name of the page using the variable `page.title`. This is to show the type of listing being used, filteration, on this page.

```php
{{ page.title|raw }}
```

Next, the products listing sorting method should be set. The array `sort_list` contains the sorting methods for the product listing, which can be displayed with a `for-loop` statement. Each element inside that array consists of an object that contains a pair of values, which indicates the sorting method. 

The available sorting methods are: `ourSuggest`, `bestSell`, `topRated`, `priceFromTopToLow`, `priceFromLowToTop`. The developer may retrieve the value of `sort.is_selected` in order to check if a specific sorting method has already been selected.

```php
{% if sort_list|length %}
  {% for sort in sort_list %}
    <option value="{{ sort.value }}" {{ sort.is_selected?'selected':'' }}>{{ sort.label }}</option>
  {% endfor %}
{% endif %}
```

At this point, the list of the given products can be displayed. The `products` object within a `for-loop` statement can be used to display a list of the received products. The developer may use any style for display purposes.

 Many details for the product can be displayed as per the developer's need, for example: `product.name`, `product.image.url`, `product.url`, `product.price`, and many more.

The developer may use the value `trans('pages.categories.no_products')` in the case of no products in the listing. This is to make sure that the page is showing the correct content for the end user.

```php lineNumbers
{% if products|length %}
    <salla-infinite-scroll next-page="{{ products.next_page }}" next-page.autoload>
        {% for product in products %}
            <a href="{{ product.url }}">
                <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
                {% if product.promotion_title %}
                    {{ product.promotion_title }}
                {% endif %}
            </a>
            <h3>
                <a href="{{ product.url }}">{{ product.name }}</a>
            </h3>
            {% if product.on_sale %}
                <div>
                    <h4>{{ product.sale_price|money }}</h4>
                    {{ product.regular_price|money }}
                </div>
            {% else %}
                <h4>{{ product.price|money }}</h4>
            {% endif %}
        {% endfor %}
    </salla-infinite-scroll>
{% else %}
    {{ trans('pages.categories.no_products') }}
{% endif %}
```

Finally, if the testimonials are enabled by the store settings, the developer may display them as below:

```php lineNumbers
{% if store.settings.category.testimonial_enabled %}
  {% component 'home.testimonials' %} 
{% endif %}
```

:::tip[Educational Clip]
<Video src="https://youtu.be/OE2KaZSly9s?si=VkQ6jZz9Nk5r5ZC2"></Video>
:::

