Featured products list, which is a **_pre-defined component_**, is a collection created in a specific design to draw customers' attention to see a certain collection of products primarily.
Twilight comes with three pre-styled featured products components, and this is the _style-1_ component.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── featured-products-style1.twig
          ...
```

### Example
<!--
focus: false
-->
![Featured Products Style 1](https://cdn.salla.network/docs/twilight/4/pages-components-home-featured-style1-01.png)

### Variables


<DataSchema id="1383692" />


### Usage

In general, this pre-styled featured products component starts with checking if there is a _main product_ to display in bigger size:

```php lineNumbers
{% if main_product %}
    <div class="product-feature">
        <div>
            <h2>{{ main_product.title }}</h2>
        </div>
        <a href="{{ product.url }}">
            <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
            {% if product.promotion_title %} {{ product.promotion_title }} {% endif %}
        </a>
        <h3>
            <a href="{{ product.url }}">{{ product.name }}</a>
        </h3>
        {% if product.on_sale %}
            <div>
                <h4>{{ product.sale_price|money }}</h4>
                {{ product.regular_price|money }}
            </div>
        {% else %}
            <h4>{{ product.price|money }}</h4>
        {% endif %}

        <salla-add-product-button product-id="{{ main_product.id }}"
                                  product-status="{{ main_product.status }}"
                                  product-type="{{ main_product.type }}">
        </salla-add-product-button>
    </div>
{% endif %}
```

After that, the component list the rest products as pre a pre-defined style. Developer has the option to edit that style.

```php lineNumbers
{% for section in items %}
    <h2>{{ section.title }}</h2>

    <div class="products-section">
        {% for product in section.products %}
            <div class="product-item">
                <a href="{{ product.url }}">
                    <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
                    {% if product.promotion_title %} {{ product.promotion_title }}
                    {% endif %}
                </a>
                <h3>
                    <a href="{{ product.url }}">{{ product.name }}</a>
                </h3>
                {% if product.on_sale %}
                    <div>
                        <h4>{{ product.sale_price|money }}</h4>
                        {{ product.regular_price|money }}
                    </div>
                {% else %}
                    <h4>{{ product.price|money }}</h4>
                {% endif %}

                <salla-add-product-button product-id="{{ product.id }}"
                                          product-status="{{ product.status }}"
                                          product-type="{{ product.type }}">
                </salla-add-product-button>
            </div>
        {% endfor %}
    </div>
{% endfor %}
```





