This endpoint is used for the purpose of rating the shipping company responsible for delivering orders. The customer will be able to send a review of that shipping company.

:::tip
The *shipping rating* endpoint has been implemented in the [Rating](https://docs.salla.dev/doc-422728?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, , and It's all setup to save developer's time and effort.
:::

## Payload `authenticated`

<DataSchema id="1427925" />

## Response
<Tabs>
  <Tab title="Success">
 
<DataSchema id="1427926" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
The method `shipping` may be called for rating a shipping company responsible for delivering an order as follows.

```js
salla.rating.shipping({
  comment: "Fast delivery",
  order_id: 587,
  rating: 5,
  shipping_company_id: 45
})
.then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onShippingRated and onShippingFailed events.

### onShippingRated
This event is triggered when rating a shipping company responsible for delivering an order is done without having any errors coming back from the backend.

```js
salla.event.rating.onShippingRated((response) => {
  console.log(response)
});
```
### onShippingFailed
This event is triggered when rating a shipping company responsible for delivering an order is not completed and an error has occurred.

```js
salla.event.rating.onShippingFailed((errorMessage) => {
  console.log(errorMessage)
});
