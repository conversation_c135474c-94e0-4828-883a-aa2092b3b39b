**Salla** theme developers can easily support other languages thanks to the Twilight's localization function, which makes it simple to extract text in a selected language.
In the previous [article](doc-422553?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), we explained how to perform localization within the Twilight theme engine. 



:::info
✅ **Twilight JS SDK** provides API methods meant to ease the access to the store's transaltions.
✅ The theme's languages are all **loaded by default** when the SDK is initialized via [salla.init()](doc-421943?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
::: 

In this article, we are going to talk about using the **Twilight JS SDK** for setting and getting a store's translation in action. 

## 📙 What you'll learn
- Usage
- Getting transaltion
- Setting transaltion


## Usage

The locales directory [`/locales/`](https://github.com/SallaApp/theme-raed/tree/master/src/locales) contains the localization files, which are *JSON-based* files that define translation strings. Each of these is a collection of key-value pairs found in a JSON string file, such as "Key":"Value."

**The following is an example of a locale file:**


```js
{
  "common": {
    "remember_my_choice": "Remember my choice",
    "note": "Note",
    "country_code": "country code"
  },

  "pages": {
    "cart": {
      "free_shipping": "Free Shipping"
    }
  }
}

```

:::tip
 Supporting multiple languages is known as **internationalization**, or **i18n** (18 letters separate the i and n). [Twilight i18n](https://github.com/SallaApp/twilight-i18n) provides the developer with access to many translations that are ready made for your Twilight Theme. The developer can find a complete list of all the language translation files supported by Twilight [here](https://localazy.com/p/twilight)

:::

Mainly, the **Twilight JS SDK** offers two methods to manage the translations, `get()` and `set()`. 

## Getting transaltion
The `get()` method can be used to retrieve text from the locale file. It needs only one parameter, which is `key` of that text. For example, in the previous code, the developer might return the text "Free Shipping" as follows:

```js
salla.lang.get('pages.cart.free_shipping')
```

## Setting transaltion
The `set()` method can be used to change the translation of text in the locale file. It needs two parameters, the `key` of that text and the value that will be stored for that text. For example, in the previous code, the developer may change the text "Remember my choice" as follows:

```js
salla.lang.set('en.pages.order.new_order','New Order')
```

:::warning
This updates an existing key of the current locale
:::

## Events

In some scenarios, the developer may need to retrieve some translations upon the completion of the loading. In such a case, the event "onLoaded()" may be invoked as follows:

```js
Salla.lang.onLoaded(() => {
  Salla.log(salla.lang.get('pages.profile.verify_title'))
});
```

<br>

# Adding New Translations

## Using Single Key Path

The `add()` method can be used to add a new translation key path to multiple locales. It needs two parameters, which is the `key` of the text to be translated, and the `object` that contains the translation of the `key` in multiple locales. For example, the developer can add the text "New Order" to different locales as follows:

```js
salla.lang.add('pages.order.new_order', {'ar': "طلب جديد", 'en': "New Order"})
```

## Using Bulk Key Paths

The `addBulk()` method can be used to add more than one new translation key paths to multiple locales. It needs one parameter, which is the `object` containing multiple keys and their corresponding translations in different locales. For example, the developer can add the texts "New Order" and "New Product" to different locales as follows:

```js
salla.lang.addBulk({
    pages.order.new_order', {'ar': "طلب جديد", 'en': "New Order"},
    pages.product.new_product', {'ar': "منتج جديد", 'en': "New Product"}
});  
```


:::check[Information]
To explore more about this API, check the [Lang.js](https://github.com/rmariuzzo/Lang.js) library on GitHub.
:::
