This endpoint is a feature provided by an online Salla Store that allows customers to book a product as a service. This endpoint handles requests to add a new booking for a specific product/service, and creates a booking record in the store's database that includes relevant details. The endpoint streamlines the booking process for customers and store administrators, and provides a convenient way to book products as services. 


## Payload 


<DataSchema id="1387195" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1387196" />
      
  </Tab>
   
  
</Tabs>


## Usage
To perform the action of booking a product as a service, the method `add()` may be used as follows.


```js
const productId = 123;
salla.booking.add(productId)
  .then((response) => {
    // Do something with the add endpoint response
    console.log(response.status);
  })
  .catch((error) => {
    // Handle any errors that occurred during the request
    console.error(error);
  });
```

## Events
This endpoint may trigger two events, the onAdded and onAdditionFailed events.

### onAdded
This event is triggered when booking a product as a service is done without having any errors coming back from the backend.

```js
salla.event.booking.onAdded((response) => {
  console.log(response)
});
```
### onAdditionFailed
This event is triggered when booking a product as a service is not completed and an error has occurred.

```js
salla.event.booking.onAdditionFailed((errorMessage) => {
  console.log(errorMessage)
});