{#
| Variable      | Type   | Description                                                      |
|---------------|--------|------------------------------------------------------------------|
| items[]       | array  | List of 3 features                                               |
| items[].icon  | string | SallaIcons css class `sicon-*`                                   |
| items[].title | string | Title of the feature                                             |
| items[].text  | string | Subtitle for the feature                                         |
| position      | Int    | Sorting number start from zero                                   |
#}
<section class="s-block s-block--features container">
    <div class="grid grid-cols-[repeat(2,minmax(0,1fr))] md:grid-cols-[repeat(3,minmax(0,1fr))] gap-4 sm:gap-6 xl:gap-8">
      {% for item in items %}
          <div class="s-block--features__item">
            <div class="feature-icon">
              <i class="{{ item.icon }}"></i>
            </div>
            <h2>{{ item.title }}</h2>
            <p>{{ item.text }}</p>
          </div>
      {% endfor %}
    </div>
</section>