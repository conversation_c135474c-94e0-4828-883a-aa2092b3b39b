The `<salla-verify>` web component used to show fields for verifying email/mobile of users by sending OTP verification code during registration, [login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD), or profile update, and that can be done using the properties' parameters available.

:::tip[Note]
Available API Endpoints for the Verify component are:

- [Auth Verification](https://docs.salla.dev/doc-422620?nav=01HNFTDZPB31Y2E120R84YXKCX)
- [Contact Verification](https://docs.salla.dev/doc-422686?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::


## Example

<!--
focus: false
-->

![Verify Example](https://cdn.salla.network/docs/twilight/6/js-web-verify-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- render it as modal and will show via open method or event -->
<salla-verify></salla-verify>

<!-- render it as inline dom to show otp collect form -->
<salla-verify display="inline"></salla-verify>
```      
  </Tab>

   <Tab title="JS">
      
```js
var verifyComponent = document.querySelector("salla-verify");

verifyComponent.addEventListener("verified", function (response) {
    console.log('otp verified');
});
```
  </Tab>  
    
   <Tab title="SASS">
  
This JS web component can be targeted for styling by its `.s-verify` class. Following is a complete source code for customizing this component:

```css

.s-verify {
  &-host{
    
  }
  &-message {

  }
  &-label {

  }
  &-codes {

  }
  &-input {

  }
  &-footer {

  }
  &-submit {

  }
  &-resend-message {

  }
  &-timer {

  }
  &-resend {

  }
  &-back {
    
  }
}
```  
  </Tab>  
</Tabs>



## Properties

| Property    | Attribute     | Description                                               | Type                 | Default   |
| ----------- | ------------- | --------------------------------------------------------- | -------------------- | --------- |
| Auto Reload | `auto-reload` | Should auto reloading the page after success verification. | `boolean`            | `'true'`  |
| Display     | `display`     | Should render component without modal                     | `"inline" \| "modal"` | `'modal'` |
| Support Web Authentication     | `support-web-auth`     | Once the API verifies the success. It will login the customer in web pages                     | `boolean` | `true` |
| Type          | `type`          | Verifying method                                          | `"email" \| "mobile"`   | `'mobile'`   |

## Events

| Event      | Description                     | Type               |
| ---------- | ------------------------------- | ------------------ |
| `verified` | This is triggered when success verification event is fired. | `CustomEvent<any>` |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `getCode` which gets the current code and `open(data:any)` which shows the verifying as modal.

| Method   | Description                                  | Return Type            |
| -------- | -------------------------------------------- | ---------------------- |
| `getCode()`   | Gets the current code. | `Promise<string>` |
| `open(data: any)`   | Shows the verifying as modal. | `Promise<void>` |

## Slots
The `slots` makes it customizable to modify certain labels, such as `after-footer`.

| Slot             | Description                                                                   |
| ---------------- | ----------------------------------------------------------------------------- |
| `after-footer` |Placeholder position that appears after the footer.                                                          |
| `footer`       | Replaces the footer. By default, it contains: `verify button`, `resend`, and `timer`. |