This endpoint removes an item from the customer's shopping cart. The customer may remove an item by clicking on the "remove from to cart" button. 

## Payload

<DataSchema id="1387204" />



## Response

<Tabs>
  <Tab title="Success">

<DataSchema id="1427401" />
  
  </Tab>
   <Tab title="Error">

<DataSchema id="1427314" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of removing an item from the cart, the developer may call the method `deleteItem` along with the id of the item that will be removed from the cart.
```js
salla.cart.deleteItem({ id: 12345 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.cart.deleteItem(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onItemDeleted and onItemDeletedFailed events.

### onItemDeleted
This event is triggered when removing an item from the cart is done without having any errors coming back from the backend.

```js
salla.cart.event.onItemDeleted((response) => {
  console.log(response)
});
```
### onItemDeletedFailed
This event is triggered when removing an item from the cart is not completed and an error has occurred. For example, the id of the product to be added to the cart was not found, or there is no product with such an id currently in the cart.

```js
salla.cart.event.onItemDeletedFailed((errorMessage) => {
  console.log(errorMessage)
});
```