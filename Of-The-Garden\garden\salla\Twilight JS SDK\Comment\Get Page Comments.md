The *get page comments* endpoint enables you to retrieve comments associated with a specific page identified by its pageId. By making a request to this endpoint, you can retrieve comments made by users or customers on the specified page.

## Payload 

<DataSchema id="1387225" />

## Response

<Tabs>
  <Tab title="Success">

<DataSchema id="1427934" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />

      
  </Tab>
  
</Tabs>


## Usage
To add the customer's comment about a specific product or a specific page, the developer may call the `send` method.

```js
salla.comment.getPageComments({
  pageId: 123,
  page: 1,
  per_page: 10,
})
.then((response) => {
  /* add your code here */
});

// TIP: short version
salla.comment.getPageComments(123, 1, 10)
.then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger the onFetched event.

### onFetched
This event is triggered when fetching the comments is done without having any errors coming back from the backend.

```js
salla.event.comment.onFetch((response) => {
  console.log(response)
});
```
