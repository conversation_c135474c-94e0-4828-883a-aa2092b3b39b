This endpoint is used to *change* the currency of a merchant's store and to represent prices for the products. Although the store payouts can be in a different currency, the customer can choose any of the currencies mentioned in the currency selection to display the prices.


:::tip
The *change* endpoint has been implemented in the [Localization](https://docs.salla.dev/doc-422710?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


## Payload


<DataSchema id="1387231" />



## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427927" />
     
  </Tab>
   <Tab title="Error">
       
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>

## Usage
To perform the action of changing the store's currency, the developer may call the `change()` method as below.


```js
salla.currency.change({ currency_code: "SAR") }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.currency.change("SAR").then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onChanged and onFailed events.

### onChanged
This event is triggered when changing the store's currency is done without having any errors coming back from the backend.

```js
salla.event.currency.onChanged((response) => {
  console.log(response)
});
```
### onFailed
This event is triggered when changing the store's currency is not completed and an error has occurred.

```js
salla.event.currency.onFailed((errorMessage) => {
  console.log(errorMessage)
});