This component is used to display a threaded comments section that also allows adding comments and rating them. It can be displayed as a feedback of products or testimonial which are shown in [Single product page](https://docs.salla.dev/doc-422561?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and [Single page](https://docs.salla.dev/doc-422578?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).


**The following is the location of the Comments component**

``` shell
└── src 
    ├── views
      ├── components
      |   ...
      |   └── comments.twig     
      |   ...
      ...

```

## Example

<!-- focus:false -->
![Comments](https://cdn.salla.network/docs/twilight/4/pages-components-comments-01.png)

## Variables



<DataSchema id="1384762" />

## Usage 
This component will check the eligibility of the user to create comments. If the user is allowed, the component will display comment section contaiting the text field to allow user to comment and submit the comment using `salla-button`. 

```php lineNumbers
{% if user.can_comment %}
<form onsubmit="return salla.form.onSubmit('comment.send', event);">
  <input name="type" type="hidden" value="{{ type }}" />
  <input name="id" type="hidden" value="{{ id }}" />

  <h2>{{ trans('blocks.comments.title') }}</h2>
  <textarea placeholder="{{ trans('blocks.comments.placeholder') }}" name="comment" required maxlength="500" cols="30" rows="30"></textarea>
  <div>
    <salla-button type="submit" loader-position="end">
      {{ trans('blocks.comments.submit') }}
    </salla-button>
  </div>
</form>
{% endif %}
```

Next, the component checks comments availability then uses `salla-infinite-scroll` to display the comments in a `for-loop`. It will also display the comments' details such as `name`, `avatar`, `has-order`, `rating star`, `created date`, `comment content` and checks if the `user type` is admin. As shown in the below code.

```php lineNumbers
{% if comments|length %}
<salla-infinite-scroll next-page="{{ comments.next_page }}">
  <h2>{{ pluralize('blocks.comments.comment', comments.total|number)|raw }}</h2>
  {% for comment in comments %} 
  {# you can check the full exmaple in the starter theme #} 
  {% set is_admin = comment.type=='admin' %} 
  {% if not is_admin %}
  <div>
    {% endif %}
    <div
      class="{% if is_admin %} nested-comment reply ps-8 md:ps-16 mt-8 admin 
              {% else %} comment 
              {% endif %} flex text-sm space-s-3 comment_id_{{ comment.id }}">
      {% if is_admin %} <i></i>
      {% endif %}
      <div>
        <img data-src="{{ comment.avatar|is_placeholder ? 'images/avatar.png' | cdn : comment.avatar }}" alt="{{ comment.name }}" src="{{ 'images/s-empty.png' | asset }}" />
      </div>
      <div>
        <h3>{{ comment.name }}</h3>
        {% if comment.has_order or comment.stars %}
        <div>
          {% if comment.has_order %}
          <i></i>
          <span>
            {{ trans('blocks.comments.has_bought') }}{{ comment.stars?', ':'' }}
          </span>
          {% endif %} {% if comment.stars %}
          <span>{{ trans('pages.rating.rated') }}</span>
          {% endif %}
        </div>
        {% endif %}
      </div>
      <p>{{ comment.created_at|time_ago }}</p>
      {% if comment.stars %}
      <div>
        {% for i in 1..5 %}
        <i></i>
        {% endfor %}
      </div>
      {% endif %}
      <p>{{ comment.content|raw }}</p>
      {% if comment.is_pending %}
      <span>{{ trans('blocks.comments.waiting_approval') }}</span>
      {% endif %}
      {% if comment.has_order %}
      <i data-toggle="tooltip" data-placement="right" title="{{ trans('blocks.comments.has_order') }}"></i>
      {% endif %}
    </div>
    {% if not is_admin %} 
    {% for replay in comment.replies %} 
    {% include _self with {comment:replay} %}
    {% endfor %}
  </div>
  {% endif %} {% endfor %}
</salla-infinite-scroll>
{% else %}
<div>
  <i></i>
  <p>{{ trans('blocks.comments.no_comments') }}</p>
</div>
{% endif %}
```

