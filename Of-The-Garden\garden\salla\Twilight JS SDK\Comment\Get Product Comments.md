The *get product comments* endpoint enables you to retrieve comments associated with a specific product identified by its productId. By making a request to this endpoint, you can retrieve comments made by users or customers on the specified product.

## Payload 

<DataSchema id="1387226" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427935" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />

  </Tab>
  
</Tabs>


## Usage
To get the customer's comment about a specific product or a specific page, the developer may call the `getProductComments` method.

```js
salla.comment.getProductComments({
  productId: 23,
  page: 2,
  per_page: 5,
})
.then((response) => {
  /* add your code here */
});

// TIP: short version
salla.comment.getProductComments(23, 2, 5)
.then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger the onFetched event.

### onFetched
This event is triggered when fetching the comments is done without having any errors coming back from the backend.

```js
salla.event.comment.onFetch((response) => {
  console.log(response)
});
```
