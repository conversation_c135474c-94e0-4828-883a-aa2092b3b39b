This endpoint returns the unique identifier (ID) of the current cart associated with a particular user or session. It is used to retrieve the current cart ID, which is typically required for other cart-related operations such as adding or removing items from the cart, checking out, or retrieving cart details.

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427443" />

  
  </Tab>
   <Tab title="Error">

       
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of returning the ID of the current cart, the developer may call the `getCurrentCartId()` as shown below.

```js
salla.cart.getCurrentCartId()
  .then((response) => {
    /* add your code here */
  });

```
