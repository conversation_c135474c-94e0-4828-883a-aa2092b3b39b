The `<salla-filters>` web component is used to filter data in a variety of ways, such as by text, by date, or by number. They can also be used to filter data based on the user's input. They are easy to use and can be customized to meet the specific needs of the Theme.



## Example

<!--focus: false -->
![Filters image](https://cdn.salla.network/docs/twilight/6/js-web-filters-01.jpg)

<br>

:::caution[Alert]
It is important to **ensure** adding the filters component in the [Twilight.json](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) file, for the component to work when calling the `<salla-filters>` web component. **Skipping this step could lead to the component's inability to work**.
:::

## Usage
<Tabs>
  <Tab title="HTML">
     
```html
<!-- Basic Filters component usage -->
<salla-filters
    filters='[{
      "label":"by brand",
      "key":"categories",
      "inputType":"checkbox",
      "type":"variants",
      "value":[{
      "key":"114",
      "count":2,
      "value":"abc",
      "from":"*",
      "to":"*"}]
      }]'>
</salla-filters>
```
      
  </Tab>
 
  <Tab title="SASS">
      
This JS web component can be targeted for styling. Following is a complete source code for customizing this component:

```css
:host {
  display: block;
}

.s-rating-stars-small {
  line-height: 12px;
}
.s-filters-radio{
  background-image: none !important;
}
```

  </Tab>
  

</Tabs>

## Properties

| Property | Attribute  | Description                                                                                                | Type                             | Default     |
| -------- | ---------- | ---------------------------------------------------------------------------------------------------------- | -------------------------------- | ----------- |
| Filters    | --    | Lists down the filter values  | `Filter[]` | `undefined` |

## Events

| Event                    | Description                                                 | Type               |
| ------------------------ | ----------------------------------------------------------- | ------------------ |
| `changed` | This event will be fired when the selected filter changes in its value | `CustomEvent<any>` |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `getFilters` which gets the data for filtering.


| Method           | Description         | Return Type     |
| ---------------- | ------------------- | --------------- |
| `applyFilters()` | Applies a filter to a view.  | `Promise<void>` |
| `getFilters()` | Gets the data that is available for filtering.  | `Promise<object>` |
| `resetFilters()` | Resets the selected filter to its default state. | `Promise<void>` |