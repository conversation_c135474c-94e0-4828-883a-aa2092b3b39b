This endpoint cancels an order, indicating that the customer no longer wants the product that was initially ordered.

## Payload `authenticated`
<DataSchema id="1427906" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427907" />
   
  </Tab>
   <Tab title="Error">

    
<DataSchema id="1427184" />
  </Tab>
  
</Tabs>



## Usage
The `cancel()` method receives the *id* of the order to be cancelled when the developer simply calls it.

```js
salla.order.cancel({ id: 98789 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.order.cancel(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onCanceled and onNotCanceled events.

### onCanceled
This event is triggered when cancelling an order process is done without having any errors coming back from the backend.

```js
salla.event.order.onCanceled((response) => {
  console.log(response)
});
```
### onNotCanceled
This event is triggered when cancelling an order process is not completed and an error has occurred.

```js
salla.event.order.onNotCanceled((errorMessage) => {
  console.log(errorMessage)
});
