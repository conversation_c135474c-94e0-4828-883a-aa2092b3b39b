When the Twilight JS SDK is first loaded, the initialization procedure is used to obtain the necessary configuration settings. The developer has the ability to configure the [Twilight engine](https://docs.salla.dev/doc-422610?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) to meet the needs of his theme


:::tip
✅ **Twilight JS SDK** can be customized by selecting from a number of different configurations. When the SDK is first initialised, the configurations are set to their default values.
✅ In the case of using these configurations in a separate project other than the theme's pages, the SDK needs to be initialized via `salla.init()`.
:::


In this article, we will explore the different parts of the **Twilight JS SDK**.

## 📙 What you'll learn
- [Configurations](#configurations)
- [Usage](#usage)
  - [Set SDK's configurations](#set-sdk's-configurations)
  - [Get SDK's configurations](#get-sdk's-configurations)

## Configurations
  
**Here is a comprehensive list of all of the available configurations:**



<DataSchema id="1430660" />

## Usage
As we stated [before](https://docs.salla.dev/doc-422612?nav=01HNFTDZPB31Y2E120R84YXKCX#basic-configuration), the developer can configure the [Twilight engine](https://docs.salla.dev/doc-422610?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) to meet the requirements of his theme.

### Set SDK's configurations
In general, the configuration can be set during the SDK initialization by calling `salla.init()`. Also, after the initialization, the developer can set any of the confiurations by calling the method `salla.config.set()`. Following, we see how the developer would be able to set the SDK configurations using each of these ways.

#### Set store configuration
Here is an example of how to set any value related to the store via the `salla.init()` method.
```js
salla.init({ 
  debug: true, 
  store: { 
    id: *********, 
    url: "https://my_store.test/",  
    name: "WOW store", 
    settings: { auth: { email_allowed: false } } 
  }
});
```

Similarly, we can see below how to set any value related to the store via the `salla.config.set()` method.
```js
salla.config.set({ 
  debug: true, 
  store: { 
    id: *********,
    url: "https://my_store.test/",  
    name: "WOW store"
  }
});
```

#### Set theme configuration

Here is an example of how to set any value related to the theme via the `salla.init()` method.
```js
salla.init({ 
  ...
  theme: { color: {primary: "#1232aa"} }
  ...
});
```

### Get SDK's configurations

Similarly, the developer can use the `salla.config.get()` method to retrieve any value from the configuration file. For example, the `user.id` can be retrieved using the syntax below.

```js
salla.config.get('user.id');
```

Furthermore, for the currencies and languages, the following syntax can be followed:

```js
salla.config.currencies().then((currencies) => {
  console.log(currencies);
});
```

Similarly, the list of the available laguges can be retrieved as follows:
```js
salla.config.languages().then((languages) => {
  console.log(languages);
});
```

