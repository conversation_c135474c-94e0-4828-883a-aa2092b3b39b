 
The `<salla-apps-icons>` web component allows users to display Google Play Store and Apple store icons to download the store's apps.

## Example 
![](https://cdn.salla.network/docs/twilight/6/js-web-app-icon-01.png)

## Usage

<Tabs>

  <Tab title="HTML">

```html
<salla-apps-icons apps-title=“Icon 101” hide-title=“false” vertical=“true”>
</salla-apps-icons>
```

  </Tab>

  <Tab title="SASS">

This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
:host {
    display: block;
}
```
  </Tab>

</Tabs>

## Properties

| Property	| Attribute	| Description                      	| Type  	| Default 	|
| ----------- | ------------ | ------------------------------------ | --------- | ----------- |
| Apps' Title | `apps-title` | The title to display.            	| `string`  | `undefined` |
| Hide Title | `hide-title` | Flag to show or hide title.      	| `boolean` | `undefined` |
| Vertical  | `vertical`   | Display flag to horizontal/vertical. | `boolean` | `undefined` |


## Slots
The`slots` makes it customizable to modify certain labels, such as `app`.

| Slot	| Description                                              	|
| ------- | ------------------------------------------------------------ |
| `app` | Replaces the slot with properties such as `icon` and `name`. |



