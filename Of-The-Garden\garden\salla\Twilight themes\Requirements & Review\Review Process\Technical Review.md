During a Theme technical review, several key aspects can be assessed to ensure the quality, functionality, and compatibility of the Theme. Here are some tasks typically performed during a Theme technical review:


### 1.0. Theme Design Quality

Evaluating the Theme's codebase to ensure it follows best practices, is well-structured, and adheres to Salla coding standards. Checking for readability, comments, and organization of the code. The following are also included in the theme design quality review:

- **1.1.** Theme size should not exceed `1mb`.
- **1.2.** Theme should use localization and not static string.
- **1.3.** Theme should be up to date with latest changes in [Theme Raed](https://github.com/SallaApp/theme-raed), you need to track the updates in [Theme Raed](https://github.com/SallaApp/theme-raed) frequently. 
- **1.4.** Validate `Length` in Twilight _(Maximum is `1000`)_.
- **1.5.** Count the number of requests in Network, if there is too much requests _(for example every product has request)_ should be prevented.

### 2.0. Performance:
Assessing the Theme's loading speed and performance on various devices and internet connections. Checking for optimization techniques to enhance speed and reduce page load times.

#### 2.1 Performance Using Lighthouse

These tests will applied on default preview store:
- **2.1.1.** Themes must have a minimum average Lighthouse performance score of **60** across the Theme's product, collection, and home page, for both desktop and mobile. 
- **2.1.2.** Themes must have a minimum average Lighthouse accessibility score of **90** across the Theme's product, collection, and home page, for both desktop and mobile. 

### 2.2. Responsiveness:

Verifying that the Theme is responsive and displays correctly on different devices and screen sizes, ensuring a consistent and user-friendly experience.

### 2.3. Cross-Browser Compatibility:
Testing the Theme on multiple web browsers _(Chrome, Firefox, Safari, Edge, etc.)_ to ensure consistent functionality and appearance across different browsers.

### 2.4. Security:
- Checking for potential vulnerabilities, ensuring that the Theme follows security best practices, and does not't pose risks to users or websites.
- Search for any usage for |raw and make sure that there is no usage allows the merchant to add custom html/js

### 2.5. Functionality Testing:
Testing all Theme features, including navigation, forms, buttons, sliders, etc., to ensure they work as intended and are bug-free.

### 2.6. Theme Raed Updates:

You need to track all updates in [Theme Raed](https://github.com/SallaApp/theme-raed) , and update your theme with new enhancements and features newly installed. 

By conducting a thorough technical review encompassing these aspects, it ensures that the Theme meets quality standards, functions optimally, and provides a positive user experience for both merchants and end-users.
