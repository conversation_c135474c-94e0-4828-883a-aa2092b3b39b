This `<salla-user-profile>` web component allows the user to display the user information, this can be customised to display information such as Name, Email, Phone Number. 


## Example

<!--
focus: false
-->

![User Profile Component](https://cdn.salla.network/docs/twilight/6/js-web-user-profile-01.png)

## Usage


<Tabs>
  <Tab title="HTML">
  ```html
<salla-user-profile
    custom-fields='[
        {
            "id": 743827961,
            "label": "Experience",
            "description": "",
            "type": "text",
            "required": 0,
            "value": null
        }, {
            "id": 2117208314,
            "label": "Age",
            "description": "",
            "type": "number",
            "required": 1,
            "value": null
        }, {
            "id": 1209997307,
            "label": "Friends Picture",
            "description": "",
            "type": "photo",
            "required": 0,
            "value": null
        }, {
            "id": 635713220,
            "label": "Multi Uploader",
            "description": "Please select multiple file",
            "type": "photo",
            "required": 1,
            "value": null
        }        
    ]'>
</salla-user-profile>
```
  </Tab>
  <Tab title="SASS">
 This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
:host {
  display: block;
}
```
  </Tab>
  
</Tabs>


<!--
type: tab
title: TWIG
-->




<!--
type: tab
title: SASS
-->



<!-- type: tab-end -->


## Properties

| Property | Attribute | Description | Type    | Default |
| -------- | --------- | ----------- | ------- | ------- |
| Custom Fields    | `custom-fields`    | Custome fields that can be rendered in addition to the default ones.        | `string` | `undefined` |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `setCustomFields(fields:CustomField[])`which sets custom fields for the component.

| Method | Description | Return Type |
| ------ | ----------- | ----------- |
| `setCustomFields(fields: CustomField[])`    | Sets custom fields for the component. Can be useful for non HTML usage.        | `Promise<void>`          |




