The `<salla-quantity-input>` web component is used to allow the customer to use a counter to specify the needed quantity of a specific product, which is framed by a [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component. The component extends the input number element. For more, read from [Mozilla](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/number).


## Example

<!--
focus: false
-->

![Quantity Input Example](https://cdn.salla.network/docs/twilight/6/js-web-quantity-input-01.gif)

## Usage
<Tabs>
  <Tab title="HTML">
      
 ```html
<!-- Basic Quantity Input component usage -->
<salla-quantity-input
  cart-item-id="12345"
  max="10"
  value="1"
  name="quantity">
</salla-quantity-input>
```     
  </Tab>

 <Tab title="SASS">
        
 This JS web component can be targeted for styling by its `.s-quantity-input` class. Following is a complete source code for customizing this component:

```js
.s-quantity-input{
  &-input{
    
  }
  &-button{

  }
}
```     
  </Tab>  
    
</Tabs>



## Properties

| Property | Attribute  | Description                                                                                                | Type                             | Default     |
| -------- | ---------- | ----------------------- | -------------------------------- | ----------- |
| Cart Item ID            | `cart-item-id`              | Cart Item's ID.                                                                                                         | `any`                                                     | `undefined`         |

## Methods
The pre-defined `methods` allow for calling the function built by Salla which are `increase` to add up quantity by one, `decrease` to reduce quantity by one, and `setValue` which allows for a customizible manner of inputting numbers.


| Method   | Description                | Return Type             |
| -------- | -------------------------- | ----------------------- |
| `increase()` | Increases quantity by one. | `Promise<HTMLElement>` |
| `decrease()` | Decreases quantity by one. | `Promise<HTMLElement>`  |
| `setValue(value: any)` | Sets quantity by custom value. | `Promise<HTMLElement>` |