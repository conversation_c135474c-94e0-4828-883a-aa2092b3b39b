This **_pre-defined component_** is set to display the latest products added to the store automatically. It has a fixed, not scrolled, view. It comes with a pre-defined style, which can easily be modified by the developer.

**Following is the location of this component.**

```shell
└── src
  ├── views 
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── latest-products.twig
          ...
```


### Example

<!--
focus: false
-->
![Home Latest Products](https://cdn.salla.network/docs/twilight/4/pages-components-home-latest-products-01.png)

### Variables

<DataSchema id="1383697" />

### Usage
This component receives a list of new products, if any. Then it loops through them using *for-loop* in order to display paginated using `salla-infinite-scroll`:

```php lineNumbers
<h2>{{ trans('blocks.home.latest_products') }}</h2>
{% if products|length %}
    <salla-infinite-scroll next-page="{{ products.next_page }}" next-page.autoload>
        {% for product in products %}
            <a href="{{ product.url }}">
                <img src="{{ product.image.url }}" alt="{{ product.image.alt }}" />
                {% if product.promotion_title %}
                    {{ product.promotion_title }}
                {% endif %}
            </a>
            <h3>
                <a href="{{ product.url }}">{{ product.name }}</a>
            </h3>

            {% if product.on_sale %}
                <h4>{{ product.sale_price|money }}</h4>
                {{ product.regular_price|money }}
            {% else %}
                <h4>{{ product.price|money }}</h4>
            {% endif %}

            <salla-add-product-button product-id="{{ product.id }}"
                                      product-status="{{ product.status }}"
                                      product-type="{{ product.type }}">
            </salla-add-product-button>
        {% endfor %}
    </salla-infinite-scroll>
{% endif %}
```


