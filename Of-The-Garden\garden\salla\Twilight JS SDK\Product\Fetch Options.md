This endpoint is used for fetching related data that is useful to include in the product, such as reviews, ratings, categories, or any other related entities.

The `includes` parameter is an optional part of the `FetchProductsQueryParams` interface. It allows you to list related data that should be included in the response when fetching products. This way, you can get more detailed information in one request, without making multiple API calls.

:::tip
The *fetchOptions* endpoint has been implemented in the [fetch product](https://docs.salla.dev/doc-422650/?nav=01HNFTDZPB31Y2E120R84YXKCX) endpoint, and It's all set up to save developer's time and effort.
:::

## Payload


<DataSchema id="2534851" />

## Response
<Tabs>
  <Tab title="Success">

      
<DataSchema id="2524739" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage

Define an array, `productIds`, and pass the product IDs. Then, call the `salla.product.api.fetchOptions` method from and pass in the `productIds` array. The method returns a promise, and the result, stored in `productOptions`, contains the options for the specified products.

> 📝 You can use the [`"fetch()"`](https://docs.salla.dev/doc-422650/?nav=01HNFTDZPB31Y2E120R84YXKCX) API with the `"includes"` query parameter to fetch a list of product options

```js
const productIds = [34253234, 123353455, 67567567]
const productOptions = await salla.product.api.fetchOptions(productIds)
```


## Events
This endpoint may trigger two events, the onProductOptionsFetched and onProductOptionsNotFetched events.

### onProductOptionsFetched
This event is triggered when the action of fetching product options is done without having any errors coming back from the backend.

```js
salla.product.event.onProductOptionsFetched((response) => {
 console.log(response);
});
```
### onProductOptionsNotFetched
This event is triggered when the action of fetching product options is not completed and an error has occurred.

```js
salla.product.event.onProductOptionsNotFetched((error) => {
 console.log(error);
});
```

