This endpoint is used to retrieve the details of the loyalty program sponsored by the store, as well as offer prizes and discounts to attract and retain customers. 
:::tip
The *get program* endpoint has been implemented in the [Loyalty](https://docs.salla.dev/doc-422712?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component , and It's all setup to save developer's time and effort.

:::
## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1387245" />
     
  </Tab>
   <Tab title="Error">

<DataSchema id="1427314" />
  </Tab>
  
</Tabs>



## Usage
To perform the action of getting a program details, the method `order()` may be called as below, with the id of the order to be rated.

```js
salla.loyalty.getProgram().then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onProgramFetched and onProgramNotFetched events.

### onProgramFetched
This event is triggered when fetching the loyalty program is done without having any errors coming back from the backend.

```js
salla.event.rating.onProgramFetched((response) => {
  console.log(response)
});
```
### onProgramNotFetched
This event is triggered when fetching the loyalty program is not completed and an error has occurred.

```js
salla.event.rating.onProgramNotFetched((errorMessage) => {
  console.log(errorMessage)
});