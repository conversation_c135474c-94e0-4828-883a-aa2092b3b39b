This component is part of the main view for the home page landing part. It helps to portray the store main categories links.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── main-links.twig
          ...
```


### Example

<!--
focus: false
-->
![Main Links](https://cdn.salla.network/docs/twilight/4/pages-components-home-custom-main-links-01.png)

### Settings

This component is a [custom component](doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). Its configuration is described in the [twilight.json](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) as follows:

```json lineNumbers
{
  "version": ...,
  "theme_name": ...,
  "repo_url": ...,
  "support_url": ...,
  ...
  "components": [
    {
      "name": "main-links",
      "title": "Main Links",
      "icon": "sicon-layout-grid-rearrange",
      "path": "home.main-links",
      "fields": [
        {
          "id": "title",
          "type": "string",
          "label": "Title",
          "placeholder": "Enter the title here",
          "required": false
        },
        {
          "id": "links",
          "type": "collection",
          "format": "collection",
          "required": true,
          "minLength": 3,
          "maxLength": 6,
          "label":"Links and Icons",
          "fields": [
            {
              "id": "icon",
              "type": "string",
              "format": "string",
              "label": "Icon",
              "placeholder": "Tplacehoder text",
              "required": true
            },
            {
              "id": "title",
              "type": "string",
              "placeholder": "Enter link title here...",
              "label": "Link title",
              "required": true
            },
            {
              "id": "url",
              "type": "string",
              "format": "url",
              "label": "Link url",
              "placeholder": "Enter link url here...",
              "inputType": "url",
              "required": true
            }
          ]
        },
        {
          "type": "boolean",
          "label": "Merge with top component",
          "id": "merge_with_top_component",
          "format": "switch",
          "selected": true
        }
      ]
    },
  ]
}
```
#### Theme Preview 

The components can be managed using the theme preview in the [Theme menu item](https://salla.partners/themes) of Salla Partners Portal. The developer can edit the component and enable it in the theme preview dashboard.
![Theme Preview Dashboard](https://i.imgur.com/wqeT7pS.png)

### Variables
The variables of this component are fetched from the twilight.json file as per the merchant settings. They are located in the components section's fields.


<DataSchema id="1383698" />



### Usage
The main links are displayed using a pre-defined style, which can be edited by the developer. 
This component uses the `link url` and `link icon` for example, and displays them using a **for-loop**.


```php lineNumbers
{% for link in component.links %}
  <a href="{{ link.url }}"></a>
      <i class="{{ link.icon }}"></i>
      <h4>{{ link.title }}</h4>
  </a>
{% endfor %}
```



