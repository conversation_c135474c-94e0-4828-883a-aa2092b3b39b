The `<salla-rating-modal>` web component is used to display the rating scale for a store, product, or shipping company. It consists of a [Modal](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD) activated by the [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component, and that can be customized using the properties' parameters available.


## Example

<!--
focus: false
-->

![Rating Example](https://cdn.salla.network/docs/twilight/6/js-web-rating-01.gif)


## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Button to activate-->
<salla-button onclick="salla.event.dispatch(`rating::open`)">Open Rating</salla-button>

<!-- Show Rating Modal-->
<salla-rating-modal order-id="14643439"></salla-rating-modal>
```      
  </Tab>

   <Tab title="SASS">
  
This JS web component can be targeted for styling by its `.s-rating-modal` class. Following is a complete source code for customizing this component:

```css

.s-rating-modal {
  &-wrapper {

  }
  &-footer {

  }
  &-btn {

  }
  &-dots {

  }
  &-step-dot {

  }
  &-step {

  }
  &-active {

  }
  &-unactive {

  }
  &-hidden {

  }
  &-unvisiable {

  }
  &-step-wrap {

  }
  &-product-details {

  }
  &-rounded-icon {

  }
  &-title {

  }
  &-store-logo {

  }
  &-shipping-logo {

  }
  &-shipping-icon {

  }
  &-comment {

  }
  &-validation-msg {

  }
  &-product {

  }
  &-product-img {
    
  }
  &-product-title {

  }
  &-thanks {

  }
  &-icon {

  }
  &-thanks-msg {

  }
  &-thanks-btn {

  }
  &-thanks-time {

  }
  &-btn-star {

  }
  &-btn-star-large {

  }
  &-btn-star-small {

  }
  &-hovered {

  }
  &-selected {

  }
  &-stars-company {

  }
  &-stars-product {

  }
  &-bg-gray {

  }
  &-bg-primary {

  }
} 
```

    
  </Tab>  
</Tabs>



## Properties

| Property | Attribute  | Description                         | Type     | Default                       |
| -------- | ---------- | ----------------------------------- | -------- | ----------------------------- |
| Order ID | `order-id` | The Order ID as an input for rating | `number` | `'salla.config.get('page.id')'` |

## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `open` and/or `close` the Rating Component.

| Method   | Description                | Return Type             |
| -------- | -------------------------- | ----------------------- |
| `close()` | Closes the rating component | `Promise<HTMLElement>`  |
| `open()` | Opens the rating component | `Promise<NodeJS.Timeout>` |


:::tip[Tip]
 To use a method, you can for instance `open` the component via the event:

 ```html
 onclick="salla.event.dispatch(`rating::open`)"
 ```
 and `close` the component via the event:
 ```html
 onclick="salla.event.dispatch(`rating::close`)"
 ```
:::

