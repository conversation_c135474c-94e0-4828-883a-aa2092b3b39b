This endpoint enables the user to select particular items, complete the purchase of that item, and then proceed directly to the checkout page to complete the purchase without having to complete any intermediary stages.

## Payload

<DataSchema id="1387201" />

## Response

<Tabs>
  <Tab title="Success">

<DataSchema id="1427430" />

  </Tab>
   <Tab title="Error">

<DataSchema id="1427314" />
  </Tab>
  
</Tabs>


## Usage
To perform the action completing the purchase of items and proceeding directly to the checkout, the developer may call the `createQuickOrder()` as shown below.

```js
salla.cart.createQuickOrder({
    email: "<EMAIL>",
    phone: "056342682",
    country_code: "966",
    name: "<PERSON>",
    product_ids: [22,65,3],
    agreement: true,
  })
  .then((response) => {
    /* add your code here */
  });

```


## Events
This endpoint may trigger two events, the onQuickOrderSucceeded and onQuickOrderFailed events.

### onQuickOrderSucceeded
This event is triggered when the action completing the purchase of items and proceeding directly to the checkout is done without having any errors coming back from the backend.

```js
ssalla.cart.event.onQuickOrderSucceeded((response) => {
  console.log(response)
});
```
### onQuickOrderFailed
This event is triggered when the action completing the purchase of items and proceeding directly to the checkout is not completed and an error has occurred.

```js
salla.cart.event.onQuickOrderFailed((errorMessage) => {
  console.log(errorMessage)
});
```