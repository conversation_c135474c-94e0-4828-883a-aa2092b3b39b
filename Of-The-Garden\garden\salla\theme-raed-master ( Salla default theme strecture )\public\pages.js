(()=>{"use strict";var t={399:(t,e,n)=>{n.d(e,{A:()=>a});var r=n(29),o=n(901),i=function(){return(0,o.A)((function t(){(0,r.A)(this,t)}),[{key:"onReady",value:function(){}},{key:"registerEvents",value:function(){}},{key:"initiate",value:function(t){if(t&&!t.includes(salla.config.get("page.slug")))return app.log("The Class For (".concat(t.join(","),") Skipped."));this.onReady(),this.registerEvents(),app.log("The Class For (".concat((null==t?void 0:t.join(","))||"*",") Loaded🎉"))}}])}();i.initiateWhenReady=function(){var t,e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;"ready"===(null===(t=window.app)||void 0===t?void 0:t.status)?(new this).initiate(n):document.addEventListener("theme::ready",(function(){return(new e).initiate(n)}))};const a=i},417:(t,e,n)=>{function r(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}n.d(e,{A:()=>r})},29:(t,e,n)=>{function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}n.d(e,{A:()=>r})},901:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(922);function o(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(0,r.A)(o.key),o)}}function i(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}},954:(t,e,n)=>{function r(t){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},r(t)}n.d(e,{A:()=>r})},501:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(662);function o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&(0,r.A)(t,e)}},822:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(284),o=n(417);function i(t,e){if(e&&("object"==(0,r.A)(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return(0,o.A)(t)}},662:(t,e,n)=>{function r(t,e){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},r(t,e)}n.d(e,{A:()=>r})},327:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(284);function o(t,e){if("object"!=(0,r.A)(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!=(0,r.A)(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}},922:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(284),o=n(327);function i(t){var e=(0,o.A)(t,"string");return"symbol"==(0,r.A)(e)?e:e+""}},284:(t,e,n)=>{function r(t){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}n.d(e,{A:()=>r})}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{var t=n(29),e=n(901),r=n(822),o=n(954),i=n(501);function a(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(a=function(){return!!t})()}(function(n){function u(){return(0,t.A)(this,u),e=this,n=u,i=arguments,n=(0,o.A)(n),(0,r.A)(e,a()?Reflect.construct(n,i||[],(0,o.A)(e).constructor):n.apply(e,i));var e,n,i}return(0,i.A)(u,n),(0,e.A)(u,[{key:"onReady",value:function(){var t,e=(null===(t=app.element(".count-anime"))||void 0===t||null===(t=t.dataset)||void 0===t?void 0:t.count)||0;(new anime.timeline).add({targets:".loyality-item",opacity:[0,1],translateX:[20,0],delay:function(t,e){return 100*e}}).add({targets:".star-anime",opacity:[0,1],rotate:[50,0],duration:4e3,delay:function(t,e){return 100*e}},"-=1000").add({targets:".count-anime",innerText:[0,e],duration:2e3,easing:"linear",round:!0,delay:function(t,e){return 150*e}},"-=3700").add({targets:".btn-anime",opacity:[0,1],duration:2e3,translateX:[20,0],delay:function(t,e){return 100*e}},"-=3200")}}])})(n(399).A).initiateWhenReady(["loyalty"])})(),(()=>{var t=n(29),e=n(901),r=n(822),o=n(954),i=n(501);function a(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(a=function(){return!!t})()}(function(n){function u(){return(0,t.A)(this,u),e=this,n=u,i=arguments,n=(0,o.A)(n),(0,r.A)(e,a()?Reflect.construct(n,i||[],(0,o.A)(e).constructor):n.apply(e,i));var e,n,i}return(0,i.A)(u,n),(0,e.A)(u,[{key:"onReady",value:function(){var t=document.querySelector("#brands-nav");document.querySelector(".brands-nav-wrap").style.height=t.clientHeight+"px",app.onClick(".brands-nav__item",(function(t){var e=t.target;app.all(".brands-nav__item",(function(t){return app.toggleElementClassIf(t,"is-selected","unselected",(function(){return t==e}))}))})),window.addEventListener("scroll",(function(){var t=window.pageYOffset<=200;app.toggleClassIf("#brands-nav","is-not-sticky","is-sticky",(function(){return t}))}))}}])})(n(399).A).initiateWhenReady(["brands.index"])})()})();