A fixed banner is a **_pre-defined component_** which is in charge of displaying a banner that is fixated on the Home Page.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── fixed-banner.twig
          ...
```


### Example
<!--
focus: false
-->
![Fixed Banner](https://cdn.salla.network/docs/twilight/4/pages-components-home-fixed-banner-01.png)

### Variables


<DataSchema id="1383695" />

### Usage
This fixed banner component receives the values of `image.url` and `image.alt` and displays it a background for this fixed area.

```php lineNumbers
<a href="{{ url }}" aria-label="Banner {{ image.alt }}">
    <img src="{{'images/s-empty.png' | asset}}" data-src="{{ image.url }}" alt="{{ image.alt }}" />
</a>
```
<br>

:::tip[Educational Clip]

<Video src="https://youtu.be/onCsXbZi_34?si=hTnZvYwNg_ZhHZHP"></Video>


:::
