The `<salla-reviews>` web component displays a vertically scrollable reviews, which you can customize its data source using the properties available.

:::tip[API Usage]
Learn more about the Reviews API usage from [here](https://docs.salla.dev/doc-705836?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::

## Example

<!--
focus: false
-->


![Reviews Component](https://cdn.salla.network/docs/twilight/6/js-web-reviews.png)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<salla-reviews><salla-reviews/>
```
  </Tab>      
  
</Tabs>



## Properties

| Property         | Attribute          | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Type                                                       | Default     |
| ---------------- | ------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------- | ----------- |
| Display All Link | `display-all-link` | Controls the visibility of a link to the reviews page. When set to `true`, a link will be displayed allowing users to navigate to the reviews' section.                                                                                                                                                                                                                                                                                                                            | `boolean`                                                  | `undefined` |
| Limit            | `limit`            | Defines the maximum number of reviews to retrieve from the API.                                                                                                                                                                                                                                                                                                                                                                                                                    | `number`                                                   | `5`         |
| Source           | `source`           | Specifies the data source for reviews. Valid options include: • `store`: Retrieves reviews for the entire store. <br>• `all`: Retrieves reviews from all sources. <br>• `categories`: Retrieves reviews for specific product categories. <br>• `products`: Retrieves reviews for individual products. <br>• `json`: Retrieves reviews from a custom JSON payload. <br> <br>📝 **Note** that when using `json` as the source, a valid payload must be provided in the `sourceValue` property. | `"all" \| "categories" \| "json" \| "products" \| "store"` | `"store"`   |
| Source Value     | `source-value`     | Provides the data specific to the chosen source. Required when using `categories`, `products`, or `json` as the source: <br> • `categories`: An array of category IDs. <br> • `products`: An array of product IDs.  <br> • `json`: A custom JSON object containing the reviews' data.                                                                                                                                                                                                  | `(string \| number)[] \| number \| object \| string`       | `undefined` |
