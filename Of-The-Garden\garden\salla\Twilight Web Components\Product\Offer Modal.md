The `<salla-offer-modal>` web component shows a list of products with an offer given by the store admin. These offered products are related to a specific product. It consists of [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component to which Merchants can select offers related to product(s) they added to the cart, and that can be customized using the slots' parameters available.

:::tip[note]
Available API Endpoints for the Offer component is:

- [Product Offer Details](https://docs.salla.dev/doc-422643?nav=01HNFTDZPB31Y2E120R84YXKCX)

:::

## Example

<!--
focus: false
-->

![Offer Example](https://cdn.salla.network/docs/twilight/6/js-web-offer-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">

   ```html
<!-- Button to activate Show Offer -->
<salla-button onclick="salla.event.dispatch(`offer::open`, 12345)">
  Offer Modal
</salla-button>

<!-- Offer Modal Component -->
<salla-offer-modal>
  <div slot="header">
      <p>%{{store.username}}%</p>
      <p>%{{store.logo%}}</p>
  </div>
</salla-offer-modal>
```      
  </Tab>
  <Tab title="SASS">

      
This JS web component can be targeted for styling by its `.s-offer-modal` class. Following is a complete source code for customizing this component:

```js

.s-offer-modal {
  &-body {
    
  }
  &-badge {
    &-icon {

    }
    &-text {

    }
  }
  &-product {
    &-image-wrap {

    }
    &-image {

    }
    &-info {

    }
    &-name {

    }
    &-price {

    }
    &-old-price {

    }
  }
  &-btn-wrap {

  }
  &-footer {

  }
  &-expiry {

  }
  &-remember-label {

  }
  &-remember-input {

  }
  &-scrolled-slider-wrap {

  }
  &-slider-nav {

  }
  &-nav-btn {

  }
  &-nav-btn-icon {

  }
  &-next-btn {
    
  }
  &-prev-btn {
    
  }
  &-btn-is-active {

  }
}
```

  </Tab>  
</Tabs>


## Methods
The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `show` a specific `product` or `offer`.


| Method                     | Description                                       | Return Type     |
| -------------------------- | ------------------------------------------------- | --------------- |
| `open(product_id: number)` | Opens the available offers for a specific product | `Promise<any>`  |
| `showOffer(offer: any)`    | Shows offer details for a specific product        | `Promise<void>` |


:::tip[Tip]
To use a method, you can for instance `open` the component via the event:
```html
onclick="salla.event.dispatch(`offer::open`, 12345)"
```
:::


## Slots
The`slots` makes it customizable to modify certain labels, such as `header`.

| Slot       | Description                                                                                |
| ---------- | ------------------------------------------------------------------------------------------ |
| `header`   | The top of the popup, which has replaceable properties `{name}`, `{message}`.                    |
| `category` | Replaces Category badge, which has replaceable properties `{name}`, `{url}`.                     |
| `product`  | Replaces product card, which has replaceable properties `{name}`, `{url}`, `{image}`, `{price}`. |
