Show the world what you have built and publish it to the [Theme Marketplace](https://s.salla.sa/marketplace/themes/tag-all) for [Salla Merchants](https://salla.partners/) to start using it. As the Theme has been [tested and previewed](https://docs.salla.dev/doc-422776?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), it is time to submit the publication request for Salla Team to approve it.


:::tip[A thing to know!]
- 🖥️ [**Salla CLI**](https://github.com/SallaApp/Salla-CLI), which is a command-line tool developed by Salla team, can be used to [publish](https://docs.salla.dev/doc-422968?nav=01HNA8QHCPJTCY5VSEZ616JCAK) a theme.
- ✅ The developer can decide whether a theme is installed in all stores or just some of them by setting the theme's availability during the [theme setup](https://docs.salla.dev/doc-421879?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
:::


## 📙 What you'll learn
By the end of this article, you will have successfully requested to publish your theme via [Salla Partners Portal](https://salla.partners). You will learn:
- [Theme Publication](#theme-publication)
- [Withdraw Theme Publication](#withdraw-theme-publication)

<hr>

### Theme Publication
Once all information has been provided, the developer can submit the request for publication by scrolling down to the Theme Publication section in the Partners Portal. Click on "Complete Theme publication" button.

<!--
focus: false
-->
![Send Publication Request](https://cdn.salla.network/docs/twilight/1/publish-theme-01.png?=ve)

This will redirect you to the Listing Details page where details about the theme should be provided as shown below.
|Item|Description|
|--|--|
|Theme Screenshots| Images of how the Theme would appear on the store|
|Preview Stores| Details about the demo store that will be used as preview store|
|Theme Price| The them price and discount details if applicable|
|Support Details| The contact details for Theme support|


## Listing Information
This is the second part of Theme Setup, where the developer can prepare the Theme for publishing.

#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">1</span> Theme Screenshots

If not added previously, the developer can add high-quality screenshots, so as to make the theme more attractive and more user-friendly. There should be at least 3 images, with  the resolution of 1366x768.

<!--
focus: false
-->
![Theme Screenshots](https://cdn.salla.network/docs/twilight/1/set-up-theme-02.png?=ve)



#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">2</span> Preview Stores

To showcase Themes for Merchants in a live demonstration by using one of your Demo Stores.
<!--
focus: false
-->
![](https://i.imgur.com/0pLtemN.png)

To add a Demo Store as a preview store follow these steps:
a- Click "Add New Store"
![image](https://i.imgur.com/IE0V9OA.png)
b- Fill in the details
![image](https://i.imgur.com/hbSFL5v.png)

And the details are as follow:

| Item  |Description   |
|---|---|
|1-Preview Store |Select the Demo Store from the List.|
|2- Theme Category| Select the Theme Category from the drop down list|
|3- Color|Choose the store Color|
|4- Thumbnail| Add a Thumbnail image for your Preview Store|
|5- Default Store|Tick here if you wish to have this Demo Store as the default preview store|
|6- Save|Click "Save" to save the changes|

Once you finish these steps, you will find the Demo Store listed in the Preview Stores, and a notification will be displayed on the upper right of the page


#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">3</span> Theme Price

Set the one-time price of the Theme, which will be used to purchase the Theme from the Merchant. Perhaps add a discount to encourage more Merchants to install and use the Theme.

<!--
focus: false
-->
![Theme Price](https://cdn.salla.network/docs/twilight/1/setup-theme-19.png?ss)

:::tip[Note]
Price your theme at a minimum of *SAR250* to align with our platform's pricing policy and ensure fair compensation for your effort.
:::


#### <span style="background-color: #85dbb3; padding:3px; font-weight: bold">4</span> Support Details

For the Merchants to continue using the Theme, having detailed support ways would help greatly.

![image](https://cdn.salla.network/docs/twilight/1/setup-theme-14.png?new)

After completing the Listing Information details, click on the "Send publication request" button.

![](https://cdn.salla.network/docs/twilight/1/publish-theme-06.png)

A pop-up window will appear requesting Theme Category and Changelog details. Fill in the details and click "Send publication request" button to continue.
![](https://cdn.salla.network/docs/twilight/1/publish-theme-05.png)

:::warning[Alert]
Always pay attention to any warnings received in the theme's [Github](https://www.github.com) repository's latest commit update text. Follow the directions given to fix any problems and keep enjoying the Twilight experience.

![Image](https://cdn.salla.network/docs/twilight/1/publish-theme-03.png?v=1-10-202)
:::
 
### Withdraw Theme Publication
The developer may need to withdraw the theme in order to make some modifications. The theme publication can be withdrawn by clicking on the "Withdraw" button at the top of the screen.

<!--
focus: false
-->
![Withdraw Request](https://i.imgur.com/yH67JlW.png)

