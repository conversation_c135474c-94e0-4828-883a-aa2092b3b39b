The `<salla-product-availability>` web component is to show the "Notify availability" option as a button for the registered customer, while prompting unregistered merchants to input their phone number/email to be added to the notification list. It consists of a [Modal](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD) activated by the [But<PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component and that can be customized using the properties' parameters available.

:::tip[Note]
Available API Endpoints for the Product Availability component is:

- [Product Availability](https://docs.salla.dev/doc-422642?nav=01HNFTDZPB31Y2E120R84YXKCX)
:::

## Example

<!--
focus: false
-->

![Product Availability Example](https://cdn.salla.network/docs/twilight/6/js-web-product-availability-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">
   
```html
<!-- Product Availability Modal with SMS & Mobile for unsubscribed users -->
<salla-product-availability
  product-id="12345"
  channels="sms,email"
  is-subscribed="false">
</salla-product-availability>

<!-- Product Availability for subscribed users -->
<salla-product-availability product-id="12345" is-subscribed="true"></salla-product-availability>
```
   
  </Tab>
  <Tab title="SASS">

      
This JS web component can be targeted for styling by its `.s-product-availability` class. Following is a complete source code for customizing this component:

```css

.s-product-availability {
  &-wrap {

  }
  &-subscribed {

  }

  &-subs-icon {

  }

  &-body {

  }

  &-label {

  }

  &-input {

  }

  &-footer {

  }

  &-footer-btn {

  }

  &-error-msg {

  }
}
```
  </Tab>  
</Tabs>

:::tip[Tip]
`is-subscribed` boolean variable manipulates the status of the Product Availability Component.
:::


## Properties

| Property      | Attribute       | Description                                                               | Type                                | Default                       |
| ------------- | --------------- | ------------------------------------------------------------------------- | ----------------------------------- | ----------------------------- |
| Channels      | `channels`      | Available notification channels                                           | `"email" \| "sms" \| "sms,email"` | `undefined`                   |
| Is Subscribed | `is-subscribed` | Whether or not the current user subscribed to the notification channel                       | `boolean`                           | `'false'`                       |
| Product ID    | `product-id`    | Product ID under which the merchant can sign up for an availability notice | `number`                            | `salla.config.get('page.id')` |