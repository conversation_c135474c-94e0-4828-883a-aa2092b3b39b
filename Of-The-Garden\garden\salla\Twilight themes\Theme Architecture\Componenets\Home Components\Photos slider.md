Photos slider (also known as carousels or slideshows) are a simple way to display a variety of images. It's **_pre-defined component_**. The idea of large, magnificent, dazzling picture shows might be very appealing. Attractive photos can entice new visitors to your site by attracting their attention right away.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components
   |  ├── home
   |  |   ...
   |  |  ├── photos-slider.twig
          ...    
```



### Example
<!--
focus: false
-->
![Photo Slider](https://cdn.salla.network/docs/twilight/4/pages-components-home-photo-slider-01.png)

### Variables


<DataSchema id="1383700" />

### Usage
Using a **for-loop**, The slider's images within `items` will be displayed using `item.image.url`.


```php lineNumbers
<section id="photos-{{ position }}">
    <div data-id="photos-{{ position }}">
        <div class="swiper-wrapper">
            {% for item in items %}
                <div class="swiper-slide" data-src="{{ item.image.url }}"></div>
            {% endfor %}
        </div>
        <div class="swiper-pagination"></div>
        <button aria-label="Previous Slide" class="slider-prev">
            Previous
        </button>
        <button aria-label="Next Slide" class="slider-next">
            Next
        </button>
    </div>
</section>
```

