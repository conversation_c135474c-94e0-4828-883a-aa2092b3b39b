The `<salla-placeholder>` web component is used to reserve space for content that soon will appear in a layout soon. It may include a paragraph, a header, and an image, depending on the content type. It emphasizes that the content is to be loaded and can be customized using the properties' parameters available.

<!-- Available Endpoints 

- 1
- 2
- 3 -->

## Example

<!--
focus: false
-->
![Placeholder Image](https://cdn.salla.network/docs/twilight/6/js-web-placeholder-01.png)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<!-- Basic Placeholder component usage -->
<salla-placeholder icon="s-icon inbox" icon-size="md" alignment="center">
  <span slot="title">No orders found</span>
  <span slot="description">Oh! You have not ordered yet!</span>
</salla-placeholder>
```

  </Tab>
  
</Tabs>


## Properties

| Property  | Attribute   | Description                                   | Type                                   | Default |
| --------- | ----------- | --------------------------------------------- | -------------------------------------- | ------- |
| Alignment | `alignment` | Defines the alignment of contents.            | `"center" \| "left" \| "right"`        | `left`  |
| Icon      | `icon`      | Customizes the icon to display in SVG format. | `string`                               | `Inbox` |
| Icon Size | `icon-size` | Adjusts the size of the icon.                 | `"lg" \| "md" \| "sm" \| "xl" \| "xs"` | `"md"`  |

## Slots
The`slots` makes it customizable to modify certain labels, such as `description`.

| Slot            | Description                                   |
| --------------- | --------------------------------------------- |
| `description` | Additional content displayed below the title. |
| `title`       | The primary content of the placeholder.       |
