The [`thank you page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/thank-you.twig) is the page the customer is taken to after completing an order transaction. Naturally, the page includes a brief thank you message that signals the store's appreciation for completing the order - just like any consumer would expect. In addition, this page template lists the [order details](https://docs.salla.dev/doc-422564?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) for the customer's reference.

**Following is the page location and url:**

```shell title = "🌐 Page URL: http://www.store-domain.com/thankyou/oKgyXG"
└── src
    ├── views
      ├── pages
      |   ...
      |   ├── thank-you.twig
      |   ...
      ...
```
<br/>

### Example
<!--
focus: false
-->
![Thank you](https://cdn.salla.network/docs/twilight/4/pages-thank-you-01.png)


### Variables


<DataSchema id="1383884" />


### Hooks
The `thank you page template` may call the following [hooks](doc-422552?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) in order to inject extra information.

```php
{% hook 'thank-you:items.start' %}
{% hook 'thank-you:items.end' %}
```

### JS Web Components
The Thank You page may include the following [JS Web Components](https://docs.salla.dev/doc-422688?nav=01HNFTE06J4QC24T0D5BPRYKMD), which are ready-made designs and style-sets of web components for Salla stores:

- Button [`<salla-button>`](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD)

### Usage
This page template receives the `order` object, which contains all of the information related to the purchased order. 

The developer may start by displaying the order id using the variable `order.order_id`.
After that, a _for-loop_ statement is used in order to list the purchased items with their details. For example, `item.codes` and `item.name`. Other details can also be displayed, such as attached files, with these items, using the array `item.files`. 

Below is a full example of that:

```php lineNumbers
<p>Thank you</p>
<p>Order Id #{{ order.id }}</p>

<p>Order details</p>

{% for item in order.items %}
    {% if item.codes|length %}
        ({{ item.name }})
        {% for code in item.codes %}
            {{ code }}
        {% endfor %}
    {% endif %}

    {% if item.files|length %}
        {{ thank_you.files }} ({{ item.name }})
        {% for file in item.files %}
            <a href="{{ file.url }}">{{ file.name }}</a>
        {% endfor %}
    {% endif %}
{% endfor %}
```

Another thing that can be done on this page is to show that the order details have been sent to the customer's email using the variable `order.customer.email` along with the order invoice.

```php lineNumbers
{% if order.email_sent %}
  {{ thank_you.email_sent }}
  {{ order.customer.email }}
{% else %}
  {{ thank_you.resend_email }}
  {{ thank_you.sent_invoice }}
{% endif %}
```

After that, it would also be good to show store support contact information in case the customer needs to contact the store owner.

```php lineNumbers
<p>Support</p>

{% if store.contacts.mobile %}
  {{ store.contacts.mobile }}
{% endif %} 

{% if store.social.whatsapp %}
  {{ store.social.whatsapp }}
{% endif %}
```
:::tip[Educational Clip]
<Video src="https://youtu.be/w9kPR4I3yUw?si=0xSBzyFpgG3UKz6z"></Video>
:::
