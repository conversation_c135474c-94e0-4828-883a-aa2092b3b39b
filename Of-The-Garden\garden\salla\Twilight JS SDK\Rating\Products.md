The customer is able to rate each product purchased in a specific order. This endpoint is used to save the customer's review comments on the purchased products list.



:::tip
The *products rating* endpoint has been implemented in the [Rating](https://docs.salla.dev/doc-422728?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, , and It's all setup to save developer's time and effort.

:::
## Payload `authenticated`

<DataSchema id="1387271" />


## Response
<Tabs>
  <Tab title="Success">
      
<DataSchema id="1427924" />
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />

      
  </Tab>
  
</Tabs>


## Usage
To perform the process of rating the list of the purchased products, the developer may call the method `products()` along with the products' array and the order id as per the following example. 


```js
salla.rating.products({
  products: [
    {
      product_id: "5871",
      comment: "good quality",
      rating: 5,
    },
    {
      product_id: "2589",
      comment: "i like the colors option",
      rating: 4,
    },
    {
      product_id: "1147",
      comment: "good price",
      rating: 4,
    },
  ],
  order_id: 589,
})
.then((response) => {
  /* add your code here */
});
```

## Events
This endpoint may trigger two events, the onProductsRated and onProductsFailed events.

### onProductsRated
This event is triggered when the process of rating a list of the purchased products is done without having any errors coming back from the backend.

```js
salla.event.rating.onProductsRated((response) => {
  console.log(response)
});
```
### onProductsFailed
This event is triggered when the process of rating a list of the purchased products is not completed and an error has occurred.

```js
salla.event.rating.onProductsFailed((errorMessage) => {
  console.log(errorMessage)
});