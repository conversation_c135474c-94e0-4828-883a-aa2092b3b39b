Twilight theme engine comes with pre-defined list of pages that form together the Salla theme pages.

Following is the pages location:

```sh

└── src
  ├── views
    ├── pages
    ...
```
<br/>

:::caution[Alert]
The pre-defined pages' names and paths are **not changeable**. Meaning, the developer may modify page contents, however, the page's filename and path should not be changed.
:::



## 📙 What you'll learn
This article lists all of the predefined pages that come with the Twilight theme. These pages together make a complete store from displaying items and categorizing them all the way to cart. There are nine pages in total which are:
- [Home](#home-page)
- [Product pages](#products-pages)
  - [Products listings](#products-pages)
  - [Single product ](#products-pages)
- [Customer pages](#customer-pages)
  - [Profile](#customer-pages)
  - [Order list](#customer-pages)
  - [Orders details](#customer-pages)
  - [Wishlist](#customer-pages)
  - [Notification](#customer-pages)
- [Blog pages](#blog-pages)
  - [Single blog page](#blog-pages)
  - [Blog listing page](#blog-pages)
- [Brands pages](#brands-pages)
  - [Signle brand page](#brands-pages)
  - [Brands listing page](#brands-pages)
- [Cart](#cart-page)
- [Loyalty](#loyalty-page)
- [Thank you](#thank-you-page)
- [Single page](#single-page)
<hr>

### [Home page](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

[Home page](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is the most significant page in the store, it collects all the main functions that the customer needs once they land on the store website. [Home page](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is located at [`src/views/pages/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/index.twig). More about Home page [here](https://docs.salla.dev/doc-422558?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). 

### [Products pages](https://docs.salla.dev/doc-422561?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

Products pages are where the products details are listed. There are two kinds of products pages which are [Single product](https://docs.salla.dev/doc-422561?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) and [Products listings](https://docs.salla.dev/doc-422559?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). 

The single product page displayes informations of a particular product such as price, variety, dimensions, size, availability and so on. Single product page is located at [`src/views/pages/product/single.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/product/single.twig). More about Single product page [here](https://docs.salla.dev/doc-422561?nav=01HNFTD5Y5ESFQS3P9MJ0721VM). 

The products listings page gives an organized view of products such as:
|Product listing|Description|
|---|---|
|Category listing| is where the store owner show cases the store items as they wish, and accordingly the developler should customize the page|
|Offers listing| The best about shopping is hunting for offers! Well, this page is in charge of that. Store owners can manage the items they have on offer and make them stand out to attract customers.|
|Search results listing|Having a store with many items can be overwhelming and finding what you want can be tricky, this page helps to display the items searched by the customer in a neat way.|
|Tags listing| Tags page allowes you to mark or categorize a page or groups of pages on your store. It also helps to identify, with greater ease and in more business-relevant terms, what your website visitors are accessing. You can then use the information as a more intuitive way to segment, build lead score models, and report on store performance.|

Products listings are located at [`src/views/pages/product/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/product/index.twig), you can also find more info about Product listings [here](https://docs.salla.dev/doc-422559?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### [Customer pages](https://docs.salla.dev/849314f0?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

Customer's related pages are significant in managing their related data. 
Customer pages are:

- [Profile](https://docs.salla.dev/doc-422562?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) page,
  this page displays the customer's demographic details such as name, email, address and phone number. Customer profile page is located at [`src/views/pages/customer/profile.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/profile.twig). More about Profile page [here](https://docs.salla.dev/doc-422562?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
- [Order list ](https://docs.salla.dev/doc-422563?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) page,
  this page helps listing the orders made by the customer. Customer order list page is located at [`src/views/pages/customer/orders/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/orders/index.twig). More about Order list [here](https://docs.salla.dev/doc-422563?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
- [Orders details](https://docs.salla.dev/doc-422564?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) page, this page displays further details of customer orders. Customer order details page is located at [`src/views/pages/customer/orders/single.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/orders/single.twig). More about Orders details page [here](doc-422564?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
- [Wishlist](https://docs.salla.dev/doc-422565?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) page, this page displays items which are in the customer's wishlist. Customer wishlist is located at [`src/views/pages/customer/wishlist.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/wishlist.twig). More about Wishlist page [here](https://docs.salla.dev/doc-422565?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
- [Notifications](https://docs.salla.dev/doc-422566?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) page, shows the notifications sent to the customer. Customer notifications page is located at [`src/views/pages/customer/notifications.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/customer/notifications.twig). More about Notifications page [here](https://docs.salla.dev/doc-422566?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### Blog pages

- [Single blog page](https://docs.salla.dev/doc-422568?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) template, displays a single article content from the store bolg. The content includes words and images.Single blog page is located at [`src/views/pages/blog/single.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/blog/single.twig). More about single blog page [here](https://docs.salla.dev/doc-422568?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
- [Blog listing page](https://docs.salla.dev/doc-422567?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) template, renders a list of the available blogs articles. It will show snippits for each blog article including the article title, summary, image, and author name. Blog listing page is located at [`src/views/pages/blog/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/blog/index.twig). More about blog listing page [here](https://docs.salla.dev/doc-422567?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

### Brands pages

- [Single brand page](https://docs.salla.dev/doc-422572?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), shows the details of a particular brand that belongs to a list of brands the store offers. The page is located at [`src/views/pages/brands/single.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/brands/single.twig). More about single brand page [here](https://docs.salla.dev/doc-422572?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).
- [Brands listing page](https://docs.salla.dev/doc-422570?nav=01HNFTD5Y5ESFQS3P9MJ0721VM), shows the list of brands associated with the store. The page is located at [`src/views/pages/brands/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/brands/index.twig). More about brands listing page [here](https://docs.salla.dev/doc-422570?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### [Cart page](https://docs.salla.dev/doc-422575?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

The [Cart page](https://docs.salla.dev/doc-422575?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is where the customer can manage the items they are going to buy, so it's critical for the store to provide a seamingly experience for its customer. The page is located at [`src/views/pages/cart.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/cart.twig). More about Cart page [here](https://docs.salla.dev/doc-422575?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### [Loyalty Page](https://docs.salla.dev/doc-422576?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

The [Loyalty page](https://docs.salla.dev/doc-422576?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is where the developer can introduce a loyalty program to enable the merchant offer points to the store cutomers and trade in the points for rewards. The page is located at [`src/views/pages/loyalty.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/loyalty.twig). Explore more about Loyalty page [here](https://docs.salla.dev/doc-422576?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### [Thank you page](https://docs.salla.dev/doc-422577?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

A [Thank you page](https://docs.salla.dev/doc-422577?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is the page visitors, leads, and customers see after joining your email list, submitting a form, or making a purchase. Think of a thank you page as a way to turn new visitors into warm leads and returning customers into repeat buyers. [Thank you page](https://docs.salla.dev/doc-422577?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) is located at [`src/views/pages/thank-you.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/thank-you.twig). 
More about Thank you page [here](https://docs.salla.dev/doc-422577?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### [Single Page](https://docs.salla.dev/doc-422578?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

The aim of using this page is for those pages that are fixed or don't need frequent changes, such as policy page, terms and conditions page and so on. Single Page is located at [`src/views/pages/page-single.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/page-single.twig). More about Single Page [here](https://docs.salla.dev/doc-422578?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

### [Landing Page](https://docs.salla.dev/doc-422579?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)

This page provides a strong starting point that's easy to customize. It includes an attractive offer and a countdown timer to help increase sales. With eye-catching visuals and user-friendly features, this template is a perfect fit for goals and target audience. Create an impressive online presence with this optimized landing page template.

Landing Page is located at [`src/views/pages/landing-page.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/landing-page.twig). More about Landing Page [here](doc-422579?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).

