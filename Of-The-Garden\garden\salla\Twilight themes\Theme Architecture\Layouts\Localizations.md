Twilight's localization feature makes it simple to extract strings in other languages, allowing Salla theme developers to effortlessly support multiple languages. 


:::info[Information]
In order to help merchants reach more people, Salla has chosen to support multilingual stores. This is a very effective way for them to reach a larger audience.
:::
 


In this article, we will explore the localization:
- [Files location](#localization-files-location)
- [Configuration](#configuration)
- [Translation retrieval](#translation-retrieval)
- [Supported Languages](#supported-languages)

## Localization files location

The localization files, which are JSON based files, are located in the locales directory [`src/locales/`](https://github.com/SallaApp/theme-raed/tree/master/src/locales) and are used to define translation strings. This directory will contain a JSON file for each language supported by the theme. For applications with a large number of translatable strings, this strategy is recommended.
:::info[Information]
Supporting multiple languages is known as **internationalization**, or **i18n** (18 letters separate the i and n). [Twilight i18n](https://github.com/SallaApp/twilight-i18n) provides the developer with access to many translations that are ready made for your Twilight Theme.
:::

:::tip[Note]
You can find a complete list of all the language translation files supported by <PERSON> [here](https://localazy.com/p/twilight)
:::

Following is the location of the localization files:

```sh
...
└── src 
    ├── locales
      ├── ar.json
      ├── en.json
      .....
      ├── your-language-code.json  
...
```

## Configuration 

The localization file is a list of key-value pairs, e.g. `"Key":"Value"`, that can be found in a JSON string file. A colon sign `:` separates the key and value strings, which are then enclosed by a main key value that groups the key-value pairs according to their intended purpose. Below is a very basic JSON structure with a main key value and a list of key-value pairs for translation.

```js
{
"common": {
  "modal": {
    "remember_my_choice": "لا تذكرني مرة اخرى",
    "note": "ملاحظة",
    "country_code":"كود الدولة",
    "copy_code": "نسخ الكود"
  }
 }
}
```

For example, the localization file for the Arabic language [`locales/ar.json`](https://github.com/SallaApp/theme-raed/blob/master/src/locales/ar.json) can be as follows:

```js
{
  "blocks": {
    "header": {
      "cart": "السلة",
      "login_by_sms": "تسجل الدخول برسالة نصية",
      "login_by_email": "تسجل الدخول بالبريد الإلكتروني"
    },
    "home": {
      "browse_brands": "تصفح من خلال العلامات التجارية"
    }
  }
}
```
<br>

:::tip[Best practices for Localizations]
- 1. All texts should be in 3 levels, eg. level1.level2.level3.
- 2. All keys used on Salla platforms can be found [here](https://localazy.com/p/twilight/phrases/56). In this essential directory, use the search box to find the specific key you need, then click to copy it. Additionally, ensure you incorporate the Localization file into the designated filepath.
:::

### Translation retrieval
For retrieving the translation strings, the developer can simply use the default helper [trans()](https://docs.salla.dev/doc-421929?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) function. This helper translates the passed key to the current store language. Retrieving the translation can be done in different ways:
- Simple key: 
```js
<!-- simple key -->
<span>{{ trans('common.remember_my_choice') }}</span>
```

- Key with variable:
```js
<!-- key with variable -->
<span>{{ trans('blocks.header.cart', ['word' => 'Products']) }}</span>
```
- Key with enforced locale/language:
```js
<!-- key with enforced locale/language -->
<!-- this will always print the result of key in English even if the store has different default language -->
<span>{{ trans('common.titles.orders', [], en) }}</span>
```


:::tip[A thing to know!]
 - Arabic and English Languages should be supported when adding new keys.
 - By default English Language is set as a fallback language.
 - The developer can override, an existing translation, for example override `الإضافة إلى السلة` to `تبرع الآن`. Learn more about overriding process [here](https://github.com/SallaApp/theme-ihsan/blob/master/src/locales/ar.json).
:::

 
### Supported Languages

Following is the list of the supported langauges by Twilight:

|Code|Language|
|----|------|
|ar| العربية|
|en| English|
|sq| shqip|
|hy| հայերէն|
|bg| български|
|zh| 汉语|
|hr| hrvatski|
|cs| čeština|
|da| dansk|
|nl| dutch|
|et| eesti keel|
|fi| suomen kieli|
|fr| français|
|de| Deutsch|
|el| Ελληνικά|
|he| עִבְרִית|‎
|hi| हिन्दी|
|hu| magyar|
|ind| bahasa Indonesia|
|ga|Gaeilge|
|it| italiano|
|ja| 日本語|
|ko| 한국어|
|lv| latviešu|
|mt|Maltese|
|fa| فارسی|
|pl| Polish|
|pt| português|
|ro| română|
|ru| русский|
|sl| slovenščina|
|es|español|
|sv| svenska|
|tl| Tagalog (Filipino)|
|tr| Türkçe|
|uk|украї́нська|
|ur| اُردُو|

:::tip[Educational Clip]

<Video src="https://youtu.be/pXlxVCLu2QA?si=kLvlxWkvur4AKcD0"></Video>

:::