This endpoint provides the details of the gifted product, along with any related entities, such as images and texts associated with the gift.

 
:::tip
The *get gift details* endpoint has been implemented in the [Gifting](https://docs.salla.dev/doc-422705?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component , and It's all setup to save developer's time and effort.
:::

## Payload


<DataSchema id="1387239" />

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427523" />
  
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>


## Usage
To perform the action of providing the details of the gifted product, the developer may call the method `getGiftDetails()` as follows.


```js
salla.product.getGiftDetails({ product_id: 852369 }).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onGiftFetched and onGiftFetchFailed events.

### onGiftFetched
This event is triggered when the action of providing the details of the gifted product is done without having any errors coming back from the backend.

```js
salla.product.event.onGiftFetched((response) => {
  console.log(response)
});
```
### onGiftFetchFailed
This event is triggered when the action of providing the details of the gifted product is not completed and an error has occurred.

```js
salla.product.event.onGiftFetchFailed((errorMessage) => {
  console.log(errorMessage)
});

