The `<salla-reviews-summary>` web component allows users to display the general rating out of 5 stars on the product details page. This makes it easier for customers to quickly find the information they need by highlighting the main topics that appear across existing reviews.


## Example 
![](https://cdn.salla.network/docs/twilight/6/js-web-reviews-summary.png)

## Usage

<Tabs>

  <Tab title="HTML">

```html
<salla-reviews-summary> </salla-reviews-summary>
```

  </Tab>

  <Tab title="SASS">

This JS web component can be targeted for styling by its `:host` class. Following is a complete source code for customizing this component:

```js
:host {
    display: block;
}
```
  </Tab>

</Tabs>

## Properties


| Property              | Attribute | Description | Type     | Default     |
| --------------------- | --------- | ----------- | -------- | ----------- |
| Item ID _(required)_ | `item-id` | Product ID to fetch its summary  | `number` | `undefined` |


