The `<salla-tabs>` web component makes it possible to have several panes inside a single view. This implies the content is presented in several independent panes, each of which can be seen independently of the others. If the user wants to see a certain section of the page, they click on that tab's header. The component groups several tabs/panes that each consists of `<salla-tabs-header>` and `<salla-tabs-content>` where:

- **<PERSON><PERSON> Tabs Header**: The `<salla-tabs-header>` web sub-component represents the tab for a specific tab/pane within the `<salla-tabs>` web component, allowing users to select and navigate to a particular section of the page.
- **Salla Tabs Content**: The `<salla-tabs-content>` web sub-component represents the content or tab/pane associated with a specific header within the `<salla-tabs>` web component, displaying the relevant information or functionality when the corresponding tab is selected.


## Example

<!--
focus: false
-->

![Tabs](https://cdn.salla.network/docs/twilight/6/js-web-tabs-01.png)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<salla-tabs>
  <!-- Tab One -->
  <salla-tab-header slot="header" name="tab_one"
    ><span>Tab One</span></salla-tab-header
  >
  <salla-tab-content slot="content" name="tab_one">
    <p>This is the content of the first tab.</p>
  </salla-tab-content>
  <!-- Tab Two -->
  <salla-tab-header slot="header" name="tab_two"
    ><span>Tab Two</span></salla-tab-header
  >
  <salla-tab-content slot="content" name="tab_two">
    <p>This is the content of the second tab.</p>
  </salla-tab-content>
  <!-- Tab Three -->
  <salla-tab-header slot="header" name="tab_three"
    ><span>Tab Three</span></salla-tab-header
  >
  <salla-tab-content slot="content" name="tab_three">
    <p>Third tab content goes here</p>
  </salla-tab-content>
</salla-tabs>
```

  </Tab>
  <Tab title="SASS">

This JS web component can be targeted for styling by its `.s-tabs` class. Following is a complete source code for customizing this component:

```css
.s-tabs {
  &-selected {
  }

  &-bg-normal {
  }

  &-header {
    /* Hide scrollbar for Chrome, Safari and Opera */
    &::-webkit-scrollbar {
      display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
  &-default-background {
  }

  &-content {
  }

  &-content-selected {
  }

  &-transit {
  }
}
```

  </Tab>  
</Tabs>

## Properties

#### salla-tabs

| Property         | Attribute          | Description             | Type      | Default     |
| ---------------- | ------------------ | ----------------------- | --------- | ----------- |
| Background Color | `background-color` | Background color value  | `string`  | `undefined` |
| Vertical         | `vertical`         | Aligns tabs vertically. | `boolean` | `false`     |

#### salla-tab-header

| Property     | Attribute      | Description                                             | Type                 | Default     |
| ------------ | -------------- | ------------------------------------------------------- | -------------------- | ----------- |
| Name         | `name`         | Header identifier name to sync with the content.        | `string`             | `undefined` |
| Active Class | `active-class` | The class applied to the currently active(selected) tab | `string`             | `undefined` |
| Height       | `height`       | Set the height of the tab bar                           | `string` or `number` | `undefined` |
| Centered     | `centered`     | Center tab items in the given flex.                     | `boolean`            | `false`     |

#### salla-tab-content

| Property | Attribute | Description                                                                                               | Type     | Default     |
| -------- | --------- | --------------------------------------------------------------------------------------------------------- | -------- | ----------- |
| Name     | `name`    | Set name of the tab content. Mainly used as a key to synchronize the content with it's respective header. | `string` | `undefined` |

## Methods

The pre-defined `methods` allow for calling functions built by Salla to carry out certain actvities, such as `getChild` which exposes `self` for the parent.


#### salla-tab-header

| Method       | Description                   | Return                                                                             |
| ------------ | ----------------------------- | ---------------------------------------------------------------------------------- |
| `"getChild"` | Expose `self` for the parent. | ```Promise<{selected: `boolean`, unselect: `boolean`, name: `string`, id: `string` }>``` |

#### salla-tab-content

| Method       | Description                   | Return                                                                             |
| ------------ | ----------------------------- | ---------------------------------------------------------------------------------- |
| `"getChild"` | Expose `self` for the parent. | ```Promise<{selected: `boolean`, unselect: `boolean`, name: `string`, id: `string` }>``` |

## Events

#### salla-tab-header

| Event           | Description                                  |
| --------------- | -------------------------------------------- |
| `"tabSelected"` | Emits event object when clicked or selected. |

## Slots

#### salla-tabs

| Slot        | Description                                                                                 |
| ----------- | ------------------------------------------------------------------------------------------- |
| `content` | The current and active tab content section. The `salla-tab-content` component is used here. |
| `header`  | The tab header section. The `salla-tab-header` component is used here.                      |
