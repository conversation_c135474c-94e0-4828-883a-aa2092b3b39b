Theme **CSS variables** allow the themes style to be modified by altering the font type and color code. This step provides options for the developer to apply changes on the Themes as well as enabling them using the Theme features in [Salla Partners Portal](https://salla.partners/). The **CSS variables** can be placed in the pages the developer wants to apply the styles on, or it can be placed in the master layout.


:::tip[A thing to know!]
 To make a unified theme style using **CSS variables**, it's advised to place the variables in the master layout which helps to apply it globally.
:::


This article walks through how the developer can change the theme styles using the Theme CSS variables and how to enable the features in the Partners Portal.


## 📙 What you'll learn

- [Assigning the Theme CSS variables](#assigning-the-themes-css-variables).
- [Enabling CSS variables in Salla Partners Portal](#enabling-css-variables-in-salla-partners-portal).

<hr>

### Assigning the themes CSS variables

Colors and fonts are the predominant features of the theme, which can be set by assigning the thems CSS variables. This section explains how he developer can assign CSS variables of the theme where the merchant has the ablility to change the font and color of theme once it's appiled in their store. As illustrated below:

``` php title = "master.twig" lineNumbers=true
... 
<style>
  :root {
  --font-main: {{theme.font.name}};
  --color-primary: {{ theme.color.primary }};
  --color-primary-dark: {{ theme.color.darker(0.15) }};
  --color-primary-light: {{ theme.color.lighter(0.15) }};
  --color-primary-reverse: {{ theme.color.reverse_text }};
  }
</style>
...

```
The developer can use the above CSS variables to assign their values as mentioned in the follwoing table.

|CSS Variable   | Description   |
|---|----|
|`--font-main`|This line will assign the font type to be applied in on the page|
|`--color-primary`| The primary color will be assigned to the font|
| `--color-primary-dark`|This line modifes the primary color to suit the dark mode for the theme, in this case it darkens the color by 15% |
|`--color-primary-light`|This line modifes the primary color to suit the day mode for the theme, in this case it brightens the color by 15%  |
|`--color-primary-reverse`|This line selects the reverse color for the primary color which is the oppoist of the primary color |


Another possibility is that the developer provids fixed values to the CSS variables. This would prevent the merchant from altering the theme's typeface or color scheme, as demonstrated by the following line of code:

``` php title = "master.twig" lineNumbers=true
...
<style>
:root {
  --font-main: 'Times New Roman';
  --color-primary: #da8f81;
  --color-primary-dark: #b3685a;
  --color-primary-light: #ffb5a7;
  --color-primary-reverse: #5a0f01;
  }
</style>
...
```

The CSS variables assignes the `font-main` with 'Times New Roman' and the colors with the specific color code in HEX form, the font and color will then be applied to the theme. 

### Enabling CSS variables in Salla Partners Portal

Twilight themes grants the ability for the merchant to adjust the store theme fonts and colors which is available with the help of CSS variables that were assigned by the developer in the [previous](#enabling-css-variables-in-salla-partners-portal) section. After assigning the CSS variables by the developer, the font and color can be enabled by using the Theme features in the [partners portal](https://salla.partners/) 
<!--
focus: false
-->
![Themes Features](https://cdn.salla.network/docs/twilight/4/css-variables-01.jpg)

By enabling the features, it allows the merchant to make changes to the themes fonts and colors.

:::tip[Educational Clip]

<Video src="https://www.youtube.com/watch?v=Uj397TxiOww
"></Video>

:::

