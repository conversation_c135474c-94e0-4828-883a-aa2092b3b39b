Developers can use local storage to save and retrieve data in the browser. The data in local storage does not have an expiration date. This means that even if the tab or browser window is closed, the data will remain. Furthermore, the data is only saved locally. 

The token, cart, and wishlist data of the user are stored in local storage.This decreases client-server communication strain. Unlike regular cookies, the stored values last beyond the current session.



:::info
Under the hood, the **Twilight JS SDK** makes use of [**Store.js**](https://github.com/marcuswestin/store.js), which is a is a cross-browser local storage solution for all use cases that is utilized all over the web. It includes both basic key/value storage (get/set/remove/each) and a robust set of plug-in storage and extra capabilities.
:::

In this article, we will explore how **Twilight JS SDK** manages the local storage using Store.js.

## 📙 What you'll learn
- [Basic usage](#basic-usage).
- [Handeling user's token](#handeling-user's-token)
- [Handeling user's cart summary](#handeling-user's-cart-summary)


## Basic usage
**Twilight JS SDK** provides a basic APIs for local storage across browsers:
#### Store new value
The method ` salla.storage.set()` can be used to store a name value in the browser's local storage. In the following example, a key named `user` has been stored with the value `Ahmed`.
```js
// Store current user
salla.storage.set('user', { name:'Ahmed' })
```
#### Get stored value
The method `salla.store.get()` can be used to retrieve the value of the stored key `user` from the browser's local storage.
```js
// Get current user
salla.storage.get('user')
```
#### Remove stored value
The method `salla.store.remove()` can be used to remove the value of the stored key `user` from the browser's local storage.
```js
// Remove current user
salla.storage.remove('user')
```
#### Clear all keys
he method `salla.storage.clearALL()` can be used to remove any stored key/value from the browser's local storage.
```js
// Clear all keys
salla.storage.clearAll()
```
#### Loop over all stored values
In the following example, we see an example of a **for-loop** statement used to loop over all stored values.
```js
// Loop over all stored values
salla.storage.each(function(value, key) {
  console.log(key, '==', value)
})
```
## Handling user's token
Tokens for users are stored in the browser's local storage, which means that they stay the same even if a page is refreshed or a new browser tab is opened. The following is an example code to return the user's token as an `object`.
```js
user_token = salla.storage.get("token");
```
## Handling a user's cart summary
Another advantage of using local storage is the ability to manage the user's cart summary. This will create a persistent shopping cart instance. To do so, the developer needs to create the following fields in the browser's local storage:
- `total`: grand total for the cart.
- `sub_total`: cart sub total, which is the items total without any extra cost, such as shipping cost.
- `discount`: any given discount.
- `real_shipping_cost`: The real cost of shipping.
- `count`: the number of the items in the cart.
- `shipping_cost`: the total cost of the shipping. 
The method `salla.storage.set()` is used to create a `cart.summery` in the local storage as follows:
```js
salla.storage.set("cart.summary", {
  total: 200,
  sub_total: 150,
  discount: 10,
  real_shipping_cost: 60,
  count: 3,
  shipping_cost: 0,
});
```
On the other hand, the method `salla.storage.get()` is used to return the user's cat summary from the local storage as follows:
```js
user_cart = salla.storage.get('cart');
```