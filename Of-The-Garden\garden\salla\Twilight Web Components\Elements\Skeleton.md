The `<salla-skeleton>` web component is used to display an indication to the user that something is coming but not yet available, which means that the content is to be loaded and can be customized using the properties' parameters.

## Example

<!--focus: false -->
![Skeleton](https://cdn.salla.network/docs/twilight/6/js-web-skeleton-01.png)

## Usage
<Tabs>
  <Tab title="HTML">
      
```html
<!-- Skeleton component usage -->
<salla-skeleton height='9rem'></salla-skeleton>
<salla-skeleton height='15px' width='100%'></salla-skeleton>
<salla-skeleton height='9px' width='50%'></salla-skeleton>
```
      
  </Tab>
    <Tab title="SASS">

```css
:host {
  display: block;
}
```
      
  </Tab>
</Tabs>


## Properties

| Property | Attribute | Description                                                | Type                   | Default    |
| -------- | --------- | ---------------------------------------------------------- | ---------------------- | ---------- |
| Height | `height`  | Sets the skeleton height                                   | `string`               | `'100%'`   |
| Type   | `type`    | Sets the shape type of the skeleton is it circle or normal | `"circle" \| "normal"` | `'normal'` |
| Width  | `width`   | Sets the skeleton width                                    | `string`               | `'100%'`   |
