Featured products list is a collection created in a specific design to draw customers' attention to see a certain collection of products primarily. This component is a **_pre-defined component_**
Twilight comes with three pre-styled featured products components, and this is the _style-2_ component. This style is mixd of `tabbed` and `slider` views.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── featured-products-style2.twig
          ...
```


### Example
<!--
focus: false
-->
![Featured Products Style 2](https://cdn.salla.network/docs/twilight/4/pages-components-home-featured-style2-01.png)


### Variables


<DataSchema id="1383693" />

### Usage 

The component list the products as per a pre-defined slider. Developer has the option to use any style, or slider base in the `is_slider` value. 

```php lineNumbers
{% for section in items %}
    <h2>{{ section.title }}</h2>

    <div class="products-section">
        {% for product in section.products %}
            <div class="product-item">
                <a href="{{ product.url }}">
                    <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
                    {% if product.promotion_title %} {{ product.promotion_title }}
                    {% endif %}
                </a>
                <h3>
                    <a href="{{ product.url }}">{{ product.name }}</a>
                </h3>
                {% if product.on_sale %}
                    <div>
                        <h4>{{ product.sale_price|money }}</h4>
                        {{ product.regular_price|money }}
                    </div>
                {% else %}
                    <h4>{{ product.price|money }}</h4>
                {% endif %}

                <salla-add-product-button product-id="{{ product.id }}"
                                          product-status="{{ product.status }}"
                                          product-type="{{ product.type }}">
                </salla-add-product-button>
            </div>
        {% endfor %}
    </div>
{% endfor %}
```
