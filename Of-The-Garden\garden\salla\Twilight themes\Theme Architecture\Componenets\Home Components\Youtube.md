This **_pre-defined component_** is responsible for displaying Youtube videos that the developer preselects.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── youtube.twig
          ...
```

### Example
<!--
focus: false
-->
![YouTube](https://cdn.salla.network/docs/twilight/4/pages-components-home-youtube-01.png)

### Variables


<DataSchema id="1383705" />


### Usage
This component needs the` youtube_id` variable, which is the video URL, in order to display the embedded YouTube video. Below we can see its code. The **default** look of this component in the following code:

```php lineNumbers
<section>
  <iframe width="560" height="349" src="https://www.youtube.com/embed/{{ youtube_id }}?rel=0&hd=1" frameborder="0" allowfullscreen></iframe>
</section>
```

