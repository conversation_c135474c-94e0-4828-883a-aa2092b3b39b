This endpoint is used to list the currencies of a merchant's store. Although the store payouts can be in a different currency, the store's owners can list the different available currencies in the store.
:::tip
The *list* endpoint has been implemented in the [Localization](https://docs.salla.dev/doc-422710?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::
## Response
<Tabs>
  <Tab title="Success">
 
<DataSchema id="1387235" />
     
  </Tab>
   <Tab title="Error">
       
<DataSchema id="1427314" />
      
  </Tab>
  
</Tabs>



## Usage
To perform the action of listing the avaliabe currencies for the store, the developer can call `list()` method.


```js
salla.currency.list().then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onFetched and onFailedToFetch events.

### onFetched
This event is triggered when listing the avaliabe currencies for the store is done without having any errors coming back from the backend.

```js
salla.event.currency.onFetched((response) => {
  console.log(response)
});
```
### onFailedToFetch
This event is triggered when listing the avaliabe currencies for the store is not completed and an error has occurred.

```js
salla.event.currency.onFailedToFetch((errorMessage) => {
  console.log(errorMessage)
});