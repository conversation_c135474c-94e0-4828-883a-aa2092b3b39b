The `<salla-login-modal>` web component displays the login form, which prompts a user for their credentials in order to authenticate their access. It usually comprises of the standard username or email as well as a phone number. It consists of a [Modal](https://docs.salla.dev/doc-422714?nav=01HNFTE06J4QC24T0D5BPRYKMD) activated by the [<PERSON><PERSON>](https://docs.salla.dev/doc-422694?nav=01HNFTE06J4QC24T0D5BPRYKMD) component, and that can be customized using the properties' parameters available.

:::tip[Note]
Available API Endpoints for the Login component is:

- [Auth Login](https://docs.salla.dev/doc-422618?nav=01HNFTDZPB31Y2E120R84YXKCX)

:::

## Example

<!--
focus: false
-->

![Login Example](https://cdn.salla.network/docs/twilight/6/js-web-login-01.gif)

## Usage

<Tabs>
  <Tab title="HTML">

```html
<!-- Button to open the Login Component-->
<salla-button width="wide" onClick="salla.event.emit('login::open')">
  Login
</salla-button>

<salla-login-modal is-email-allowed is-email-required is-mobile-allowed>
  <!-- a slot show before login DOM via email .-->
  <!-- <div slot="before-login-email">-->
  <!-- </div>-->

  <!-- a slot show after login DOM via mobile .-->
  <!-- <div slot="after-login-mobile">-->
  <!-- </div>-->
</salla-login-modal>
```      
  </Tab>
<Tab title="SASS">

This JS web component can be targeted for styling by its `.s-login-modal` class. Following is a complete source code for customizing this component:

```js

.s-login-modal {
  &-sub-title {

  }
  &-main-btn {
    
  }
  &-main-btn-icon {

  }
  &-main-btn-text {

  }
  &-main-btn-arrow {

  }
  &-label {

  }
  &-input {

  }
  &-otp-input {

  }
  &-link {

  }
  &-error-message {

  }
  &-wrapper {

  }
  &-tab {

  }
  &-active {

  }
  &-unactive {

  }
}
```
      
  </Tab>    
</Tabs>



## Properties

| Property          | Attribute           | Description                           | Type      | Default     |
| ----------------- | ------------------- | ------------------------------------- | --------- | ----------- |
| Inline          | `inline`            | Display the login modal in an inline manner                                                                                      | `boolean` | `undefined` |
| Is Email Allowed  | `is-email-allowed`  | Granting email input by the merchant  | `boolean` | `undefined` |
| Is Email Required | `is-email-required` | Requiring email input by the merchant | `boolean` | `false` |
| Is Mobile Allowed | `is-mobile-allowed` | Granting mobile input by the merchant. Outside KSA is set to `false` by default | `boolean` | `true` |
| Support Web Authentication | `support-web-auth` | Once the API verifyies the success of the process, it will login the customer in web pages. | `boolean` | `true` |
| Without Reload   | `without-reload`    | Render reloading of the page after a successful login                                                                               | `boolean` | `false`     |

## Methods
The pre-defined `methods` allow for calling the function built by Salla which is `open` to display the Login Component.


| Method   | Description                 | Return Type            |
| -------- | --------------------------- | ---------------------- |
| `open(event?: any)` | Opens the login's component | `Promise<HTMLElement>` |


## Slots

The`slots` makes it customizable to modify certain labels, such as `before-login-email` & `after-registeration`, as well as adding `footer` extended feature.

| Slot                  | Description                                                              |
| --------------------- | ------------------------------------------------------------------------ |
| `before-login-email`  | Shows content before the login with email screen has appeared            |
| `before-login-mobile` | Shows content before the mobile with mobile screen has appeared          |
| `before-login-type`   | Shows content before the login types has been listed in the login screen |
| `before-registration` | Shows content before user registers                                      |
| `after-login-email`   | Shows content after the login with email screen has appeared             |
| `after-login-mobile`  | Shows content after the login with mobile screen has appeared            |
| `after-login-type`    | Shows content after the login types has been listed in the login screen  |
| `after-registration ` | Shows content after user registers                                       |
| `footer`              | Shows content at the modal's footer                                      |