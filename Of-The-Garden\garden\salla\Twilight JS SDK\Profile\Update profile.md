This endpoint is used to update a customer's profile. The profile contains information such as the customer's `first_name`, `last_name`, `birthday`, `gender`, and `avatar`.

## Payload `authenticated`

<DataSchema id="1387263" />

## Response
<Tabs>
  <Tab title="Success">


<DataSchema id="1430679" />
      
  </Tab>
   <Tab title="Error">

<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To update the content of the customer's profile, the developer may call the methos `update()` along with the customer's new information as below.

<!--
type: tab
title: With web componenet
-->

in case the user change the phone/email a OTP required to complete the changes, you can take advancige of `salla-verify-modal` to hanlde the OTP verifation


```html
<script type="javascript">
salla.profile.update({
  first_name: "<PERSON>",
  last_name: "<PERSON><PERSON>",
  birthday: "2022-02-22",
  gender: "male",
  phone: "555555555",
  country_code: "SA",
  email: "<EMAIL>"
}).then((response) => {
  /* add your code here */

  // in case the mobile/email has been change
  // a event will disaptch to `salla-verify-modal` to show 
  // and colloct the OTP and complete the verifation process
});
</script>

<salla-verify-modal></salla-verify-modal>
```


#### Without web componenet


```js
salla.profile.update({
  first_name: "Mohammed",
  last_name: "Salah",
  birthday: "2022-02-22",
  gender: "male",
  phone: "555555555",
  country_code: "SA",
  email: "<EMAIL>"
}).then((response) => {
  /* add your code here */

  // in case the mobile/email has been change
  // a otp required to complete the changes 
  if(response.data.verification.status === 'pending') {
    // you have to colloct the OTP from the user and submit it again like this
    let payload = {
      type: response.data.verification.type, 
      code: '1111' // the OTP from the customer form
    };

    if(payload.type === 'phone') {
      payload.phone = response.data.phone.number;
      payload.country_code = response.data.phone.country;
    } else {
      payload.email = response.data.email;
    }

    salla.profile.verify(payload).then((response) => {
      // phone/email has been changed
    }).catch((error) => {
      // OTP incorrect
    });
  }
});
```

## Events
This endpoint may trigger two events, the onUpdated and onUpdateFailed events.

### onUpdated
This event is triggered when updating the content of the customer's profile is done without having any errors coming back from the backend.

```js
salla.event.profile.onUpdated((response) => {
  console.log(response)
});
```
### onUpdateFailed
This event is triggered when updating the content of the customer's profile is not completed and an error has occurred.

```js
salla.event.profile.onUpdateFailed((errorMessage) => {
  console.log(errorMessage)
});