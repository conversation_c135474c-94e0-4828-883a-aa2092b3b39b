The Get Quick Order Settings endpoint retrieves the configuration settings for a merchant's quick order feature, including the title, sub-title, thanks message, order button text, email requirement, agreement requirement, allowed countries, custom styling options, confirmation button text, and the agreement text. These settings determine the behavior and appearance of the quick order feature on the merchant's store.

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427422" />

      
  </Tab>
   <Tab title="Error">


<DataSchema id="1427184" />
  </Tab>
  
</Tabs>


## Usage
To perform the action of getting the setting of a quick order, the developer may call the `getQuickOrderSettings()` as shown below.

```js
salla.cart.getQuickOrderSettings().then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onQuickOrderSettingFetched and onQuickOrderSettingFailed events.

### onQuickOrderSettingFetched
This event is triggered when getting the setting of a quick order is done without having any errors coming back from the backend.

```js
ssalla.cart.event.onQuickOrderSettingFetched((response) => {
  console.log(response)
});
```
### onQuickOrderSettingFailed
This event is triggered when getting the setting of a quick order is not completed and an error has occurred.

```js
salla.cart.event.onQuickOrderSettingFailed((errorMessage) => {
  console.log(errorMessage)
});
