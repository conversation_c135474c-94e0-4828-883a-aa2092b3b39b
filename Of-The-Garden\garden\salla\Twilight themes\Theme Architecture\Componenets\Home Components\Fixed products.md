Use this **_pre-defined component_** to display a group of products that has no scrolling effect. Their location is fixed.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components    
   |  ├── home
   |  |   ...
   |  |  ├── fixed-products.twig
          ...
```


### Example
<!--
focus: false
-->
![Fixed Products](https://cdn.salla.network/docs/twilight/4/pages-components-home-fixed-products-01.png?updated)

### Variables


<DataSchema id="1383696" />


### Usage 
This component takes a list of the `products` that should be displayed, and then use **for-loop** statement to show them out. <PERSON><PERSON><PERSON> has the option of styling them the way he may need.

```php lineNumbers
<div class="head">
    {% if title %}
        <h2>{{ title }}</h2>
    {% endif %}

    {% if display_all_url %}
        <a href="{{ display_all_url }}">{{ trans('blocks.home.display_all') }}</a>
    {% endif %}
</div>


{% for product in products %}
    <div class="item">
        <a href="{{ product.url }}">
            <img src="{{ product.image.url }}" alt="{{ product.image.alt }}"/>
            {% if product.promotion_title %}
                {{ product.promotion_title }}
            {% endif %}
        </a>
        <h3>
            <a href="{{ product.url }}">{{ product.name }}</a>
        </h3>
        {% if product.on_sale %}
            <div>
                <h4>{{ product.sale_price|money }}</h4>
                {{ product.regular_price|money }}
            </div>
        {% else %}
            <h4>{{ product.price|money }}</h4>
        {% endif %}
    </div>
{% endfor %}
```




