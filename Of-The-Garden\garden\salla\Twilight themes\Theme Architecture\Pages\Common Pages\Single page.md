Twlight provides the ability to create [`single pages`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/page-single.twig) that show static content. Examples of that are the copy right page, terms and conditions, delivery information, shipping methods, and payment methods information pages. All of these topics are essential for the store owner, as he needs them to deliver specific information to the customers. This kind of information is rarely changed. For this reason, these pages are considered static. 

**Following is the page location and url:**

```shell title = "🌐 Page URL: http://www.store-domain.com/page-slug/page-1651849934"
└── src
    ├── views
      ├── pages
      |   ...
      |   ├── page-single.twig
          ...
    ...
```


### Example
<!--
focus: false
-->
![Single page](https://cdn.salla.network/docs/twilight/4/pages-single-page-01.png)

### Variables


<DataSchema id="1383882" />


### Components
This page starts by displaying the [`breadcrumbs`](https://docs.salla.dev/doc-422601?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) component. The `{% component breadcrumbs %}` line returns the current naviation for the user.

```php
{% component 'header.breadcrumbs'%}
```

The users' `comments` on a specific page may be displayed using the component `{% component 'comments' %}`.

```php 
{% component 'comments' %}
```

### Usage
This page template receives the object `page` which contains the details of the static content to be displayed. For example, the developer can use the variables `page.title` and `page.cotent` which are the core of this page template.

```php
{{ page.title }}
{{ page.content|raw }}
```

The users' `comments` on a specific page may be displayed using the component `{% component 'comments' %}`.

```php
{% component 'comments' %}
```





