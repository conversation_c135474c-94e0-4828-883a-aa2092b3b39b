This endpoint is used to fetch any offered items related to the product. Other products may be offered along with the product as an offer from the merchant. This endpoint displays all of the items as an offer.

:::tip
The *product offers* endpoint has been implemented in the [Offer](https://docs.salla.dev/doc-422715?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::

## Payload


<DataSchema id="1427499" />

## Response
<Tabs>
  <Tab title="Success">
      
      
<DataSchema id="1387248" />
      
       
  </Tab>
   <Tab title="Error">
       

<DataSchema id="1427184" />
      
  </Tab>
  
</Tabs>


## Usage
To perform the action of fetching any offered items related to the product, the developer may run the `details()` method as below.


```js
salla.product.offers({ id: 12345 }).then((response) => {
  /* add your code here */
});

// TIP: short version
salla.product.offers(12345).then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onOfferExisted, onOffersFetched and onFetchOffersFailed events.

### onOfferExisted
This event is triggered when fetching any existed offer related to the product is done without having any errors coming back from the backend.

```js
salla.event.product.onDetailsFetched((response) => {
  console.log(response)
});
```

### onOffersFetched
This event is triggered when fetching any offered items related to the product is done without having any errors coming back from the backend.

```js
salla.event.product.onOffersFetched((response) => {
  console.log(response)
});
```
### onFetchOffersFailed
This event is triggered when fetching any offered items related to the product is not completed and an error has occurred.

```js
salla.event.product.onFetchOffersFailed((errorMessage) => {
  console.log(errorMessage)
});
