Hooks are a powerful tool used in theme development for the [Salla Store Theme](https://s.salla.sa/marketplace/themes/tag-all). They allow developers to inject code and content into specific areas of a webpage's template without having to modify the original code. For example, adding meta-data and SEO-related tags into a webpage's `<head>` section. These pieces of code can be "hooked" to the theme by calling them using the template's _hooks_.

:::tip[]
The Twig code syntax `{% hook 'hook.name' %}` is used to add a hook to a specific point. It allows developers to trigger custom code or content at that point without modifying the core code.
:::


In nutsell, template hooks can be used to add content to different parts of the theme to make it more useful. Twilight theme has pre-defined a list of template hooks and made them available in the default templates. 

## 📙 What you'll learn

This article explores the following template hooks:

  - [Head hooks](#head-hooks) 
  - [Body hooks](#body-hooks)
  - [Customer Pages Hooks](#customer-pages-hooks)
  - [Product Pages Hooks](#product-page-hooks)
  - [Cart Page Hooks](#cart-page-hooks)
  - [Thank You Pages Hooks](#thank-you-pages-hooks)
  - [Brand Pages Hooks](#brand-pages-hooks)
  - [Twilight Components Hooks](#twilight-components-hooks)

<hr>

### Head hooks
In HTML, the `<head>` element is used to define the webpage's head section, which contains information about that webpage. Other head elements in the head element include `<title>`, `<meta>`, `<link>`, `<style>`, and so on. 

The Head hooks are commonly used to add meta-data and SEO-related tags into the <head> section of a webpage. They provide an easy and flexible way to add or remove content from the <head> section of the theme template without altering the core code.

The three head hooks, `{% hook 'head:start' %}`, `{% hook head %}`, and `{% hook 'head:end' %}` are used to manage the different sections of the `<head>` area as follows:

| No. | Hook Code                 | Description                                               |
| --- | ------------------------- | --------------------------------------------------------- |
| 1.  | `{% hook 'head:start' %}` | Indicates the start of the `<head>` section in a webpage. |
| 2.  | `{% hook head %}`         | Adds content to the `<head>` section of a webpage.        |
| 3.  | `{% hook 'head:end' %}`   | Indicates the end of the `<head>` section in a webpage.   |



### Body hooks

In HTML, the `<body>` tag is used to define the webpage's content area. In addition to the actual content, the style of content appearance is defined here as well. There are hooks in Twilight that allow the developer to add content to the main area of a webpage.

| No. | Hook Code                   | Description                                               |
| --- | --------------------------- | --------------------------------------------------------- |
| 1.  | `{% hook 'body:classes' %}` | Adds classes to the `<body>` tag of a webpage.            |
| 2.  | `{% hook 'body:start' %}`   | Indicates the start of the `<body>` section in a webpage. |
| 3.  | `{% hook 'body:end' %}`     | Indicates the end of the `<body>` section in a webpage.   |


For example, the hook `body:classes` allows the developer to add specific CSS classes that customise the appearance of that specific page, for instance , the home page or product page. This is done so there is no need for the store's owner to create a child theme or worry about theme updates overwriting the customizations. 

### Customer Pages Hooks
These hooks are used in Salla Store Theme development to modify and extend the functionality of the customer pages.

| No. | Hook                                              | Description                                                                              |
| --- | ------------------------------------------------- | ---------------------------------------------------------------------------------------- |
| 1.  | `{% hook 'customer:profile.form.start' %}`        | Used to add custom content or fields to the customer profile form.                       |
| 2.  | `{% hook 'customer:profile.form.fields.start' %}` | Used to add custom content or fields to the customer profile form.                       |
| 3.  | `{% hook 'customer:notifications.items.start' %}` | Used to trigger custom code or content at the start of the customer notifications items. |
| 4.  | `{% hook 'customer:notifications.items.end' %}`   | Used to trigger custom code or content at the end of the customer notifications items.   |
| 5.  | `{% hook 'customer:wishlist.items.start' %}`      | Used to add custom content or code to the customer wishlist items.                       |
| 6.  | `{% hook 'customer:wishlist.items.end' %}`        | Used to add custom content or code to the end of the customer wishlist items.            |
| 7.  | `{% hook 'customer:orders.index.items.start' %}`  | Used to modify or add content to the start of the customer orders index items.           |
| 8.  | `{% hook 'customer:orders.index.items.end' %}`    | Used to modify or add content to the end of the customer orders index items.             |

### Product Pages Hooks
**Twilight** offers several hooks for customizing the product pages on an online store. 
| No. | Hook                                            | Description                                                                        |
| --- | ----------------------------------------------- | ---------------------------------------------------------------------------------- |
| 1.  | `{% hook 'product:index.items.start' %}`        | Used to trigger custom code or content at the start of the product index items.    |
| 2.  | `{% hook 'product:index.items.end' %}`          | Used to trigger custom code or content at the end of the product index items.      |
| 3.  | `{% hook 'product:single.description.start' %}` | Used to add custom content or code to the start of the product single description. |
| 4.  | `{% hook 'product:single.description' %}`       | Used to add custom content or code to the product single description.              |

### Cart Page Hooks
Twilight  provides two hooks for customizing the cart page. The `{% hook 'cart:items.start' %}` hook is used to add custom content or code at the start of the cart items. The `{% hook 'cart:items.end' %}` hook is used to add custom content or code at the end of the cart items.

| No. | Hook                            | Description                                                        |
| --- | ------------------------------- | ------------------------------------------------------------------ |
| 1.  | `{% hook 'cart:items.start' %}` | Used to add custom content or code at the start of the cart items. |
| 2.  | `{% hook 'cart:items.end' %}`   | Used to add custom content or code at the end of the cart items.   |

### Thank You Pages Hooks
Moreover, Twilight provides four hooks to customize the thank you pages.

| No. | Hook                                 | Description                                                                                                                     |
| --- | ------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------- |
| 1.  | `{% hook 'thank-you:start' %}   `    | Used to declare the beginning of the thank-you template to add custom content.                                                   |
| 2.  | `{% hook 'thank-you:items.start' %}` | Used to list down the items within the thank-you page template, such as order items.                 |
| 3.  | `{% hook 'thank-you:items.end' %}`   | Used to state the end of listing the items after the list of items has been rendered. |
| 4.  | `{% hook 'thank-you:end' %}   `      | Used to declare the end of the thank-you template to add content to the bottom of the template.                                 |
<br>
    
:::info[]
Moreover, the `thank-you:end` hook injects two buttons (Go Back to the store and Create Salla store) in the thank-you page template and that is only in the case of having a Salla store in the "basic" plan 
:::
    
   

### Brand Pages Hooks
**Twilight** also provides four hooks to customize the brand pages of an online store.
| No. | Hook                                       | Description                                                                  |
| --- | ------------------------------------------ | ---------------------------------------------------------------------------- |
| 1.  | `{% hook 'brands:index.items.start' %}`    | Used to add custom content or code at the start of the brand index items.    |
| 2.  | `{% hook 'brands:index.items.end' %}`      | Used to add custom content or code at the end of the brand index items.      |
| 3.  | `{% hook 'brands:single.details.start' %}` | Used to add custom content or code at the start of the single brand details. |
| 4.  | `{% hook 'brands:single.details.end' %}`   | Used to add custom content or code at the end of the single brand details.   |

### Twilight Components Hooks
By default, **Twilight** automatically adds two hooks to any component that is rendered in a template. These hooks, `{% hook 'component.path.start' %}` and `{% hook 'component.path.end' %}`, indicate the start and end of the component's path. You can use these hooks to trigger custom code or content at specific points in the component, without modifying the core code. For example the following hooks can be used to add custom content before and after the header menu component.:

```js
{% hook 'component:header.menu.start' %}
...
{% hook 'component:header.menu.end' %} 
```



                                                                                                                                
                                                                                                           

