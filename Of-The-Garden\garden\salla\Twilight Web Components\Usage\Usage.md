Twilight Web Components makes it easy to implement and use them across any framework or no framework at all. This is accomplished by utilizing web platform APIs and Web Components that are standardized for smooth use.

:::tip[Note]
It's worth noting that the [Tailwind CSS Framework](https://tailwindcss.com/) is the default foundation for the **Twilight web components**.There is a possibility that future plans may include other CSS frameworks.
:::

## Installation

To use the components, developers need to load a CSS file and some JavaScript. The CSS file includes the fonts and design tokens.


#### Twilight Themes


When using **Twilight web components** within Twilight themes, there's no need to include them in your bundle or HTML. The **Twilight theme engine** will automatically inject the latest version of the **Twilight web components** into the page.

#### Bundler/ES modules


You need Node.js and Tailwind CSS installed, then install the components by performing one of the following commands:


<Tabs>
  <Tab title="NPM Installation Command">
```
npm install @salla.sa/twilight-components --save
```
  </Tab>
  <Tab title="Yarn Installation Command">
```
yarn add @salla.sa/twilight-components
```
  </Tab>
</Tabs>



<br>



In your javascript codebase, you can import the components as follows:



<Tabs>
  <Tab title="Import Command">
      
The following code imports and applies polyfills for older browsers, then registers the **Twilight Web Components** with the `window` object, enabling their use in the webpage.
      
```js
import {
  applyPolyfills, 
  defineCustomElements as TwilightWebComponents
} from '@salla.sa/twilight-components/loader';

applyPolyfills().then(() => {
    TwilightWebComponents(window);
});
```
  </Tab>
  <Tab title="HTML/CDN">
The another approach to loading Twilight Components is to use the version hosted on the CDN. The components can be loaded via `<script>` tags in the head of the HTML document

```html
<script
  type="module"
  src="https://unpkg.com/@salla.sa/twilight-components@latest/dist/twilight-components/twilight-components.esm.js"
></script>
```
      
  </Tab>
</Tabs>



### Tailwind Config

[Tailwind CSS Framework](https://tailwindcss.com/) is the default foundation for the **Twilight web components**. There is a possibility that future plans may include other CSS frameworks.

Now you can adding twilight tainwind theme to your `tailwind.config.js` file:

```js
 ...
 plugins: [
      ...
      require('@salla.sa/twilight-tailwind-theme'),
      ...
]
 ...
```

**Tailwind** added a [just-in-time compiler](https://v2.tailwindcss.com/docs/just-in-time-mode), which generates styles as the developer writes the theme rather than generating everything in advance at initial build time.

Since JIT mode generates your CSS on-demand by scanning your template files, it’s crucial that you configure the `content` option in your `tailwind.config.js` file with all of your template paths, otherwise your CSS will be empty


```js title="tailwind.config.js"
...
  content: [
    // theme views
    "views/**/*.twig",
    // list of classes which required by twilight web components
    "node_modules/@salla.sa/twilight-tailwind-theme/safe-list-css.txt"
  ],
...
```

:::tip[Note]
As mentioned [previously](doc-422689#tailwind-config?nav=01HNFTE06J4QC24T0D5BPRYKMD) . The file `tailwind.config.js` is already bundled with the **Twilight starter theme** files. However, you may use any other CSS framework.
:::


## Usage

After installation, Components can be easily added in the basic HTML markup, as shown in the following example:

```html
<!-- Salla Button component-->
<salla-button fill="outline" color="dark">Hello World 👋</salla-button>

<!-- Salla Telephone Input component-->
<salla-tel-input country-code="sa" mobile="555555555"></salla-tel-input>

<!-- Salla Product Availability component-->
<salla-product-availability channels="sms,email"></salla-product-availability>
```


:::tip[Note]
For further usage examples, browse the subsequent [component's page](https://docs.salla.dev/doc-422690?nav=01HNFTE06J4QC24T0D5BPRYKMD).
:::


## CSS Variables

Colors and fonts are the predominant features of the theme, which can be set by assigning the CSS styles. **Twilight Web Components** use the [CSS Variables](doc-421945?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) that have already been introduced. Accordingly, the developer can use the following CSS styles to assign them to the theme's colors and fonts.

:::info[Information]
More details on this can be found [here](doc-421945#assigning-the-themes-css-variables).
:::

```html
<style>
    :root {
        --font-main: 'font-name';
        --color-primary: #4b5563;
        --color-primary-dark: #4b5563;
        --color-primary-light: #d1d5db;
        --color-primary-reverse: #f9fafb;
    }
</style>
```

## Events

We recommend using DOM events, but we also provide custom events to help with specific event kinds. All custom events are always documented on their own documentation page for each component.

```js title="Modal Event Initialization"
// Save reference to the modal component below
var modal = document.querySelector("salla-modal");

// Listen for open events
modal.addEventListener("modalVisibilityChanged", function (status) {
    console.log("Modal is opened?", status);
});
```

Moreover, developers can detect when a component has been loaded, as Twilight registers its components using the standard Web Components API's CustomElementsRegistry object. This object also provides a `whenDefined` function, which takes a component name and returns a promise. When the component is defined, the promise will be fulfilled:

```js
window.customElements.whenDefined("salla-button").then(() => {
    console.log("Salla Button is defined!");
});
```