The [`home page template`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/index.twig) renders the first page which the customer encounters. This page is essential to give the first impact of the store's look-and-feel. The main store attractions are located on this page to showcase the ease of accessibility to go around the store. 

Following is the page location and url :

``` shell title = "🌐 Page URL: http://www.store-domain.com/"
└── src
  ├── views
    ├── pages
    |   ...
    |   ├── index.twig
    |   ...
    ...
```

### Example
<!--
focus: false
-->
![Single page](https://cdn.salla.network/docs/twilight/4/pages-home-01.png)

### Variables


<DataSchema id="1383863" />

### Components
By default, the home page displays a collection of Theme Features listed in the [twilight.json](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) under the `features` section. Which are located in the [`src/views/components/home/<USER>//github.com/SallaApp/theme-raed/tree/master/src/views/components/home) folder and were developed specially for the Home Page. 

More about [`twilight.json`](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) file in this [article](https://docs.salla.dev/doc-421921?nav=01HNFTD5Y5ESFQS3P9MJ0721VM).


:::info[A thing to know!]
- The Theme Features names have the prefix `component-` inside [`twilight.json`](https://github.com/SallaApp/theme-raed/blob/master/twilight.json).
- Developer must remove the Theme Features that will not be used in the theme.
- It's advised to use the Theme Features as a best practise to ensure a smooth experience for all Salla partners.
:::

The content of `{% component home %}` renders the following Theme Features as per the [store's](https://salla.sa) settings. 


| <div style="width:220px">Theme Feature</div> | Discription |
|---|---|
| [Youtube](https://docs.salla.dev/doc-422582?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | This feature is responsible for displaying Youtube videos that the developer preselects. |
| [Fixed Banner](https://docs.salla.dev/doc-422583?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Fixed banner is the area in charge of displaying a banner that is fixated on the home page. |
| [Featured prodcuts style 1](https://docs.salla.dev/doc-422591?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Using this feature, showcases the featured products in a pre-defined style.|
| [Featured prodcuts style 2](https://docs.salla.dev/doc-422592?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Using this feature, showcases the featured products in a pre-defined style. |
| [Featured prodcuts style 3](https://docs.salla.dev/doc-422593?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Using this feature, showcases the featured products in a pre-defined style. |
| [Testimonials](https://docs.salla.dev/doc-422584?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | This feature displays testimonials that the developer preselects. 
|[Parallax backgorund](https://docs.salla.dev/doc-422585?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)|This feature displays products with a backgournd that zooms out slowly giving a 2D effect. |
| [Photos slider](https://docs.salla.dev/doc-422586?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Photos are displayed in a slider by using this feature. |
| [Store Features](https://docs.salla.dev/doc-422587?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | This feature oversees the display of store's overall features, such as detailed product description or customer review of the product. |
| [Square photos](https://docs.salla.dev/doc-422588?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Use this feature to display photos in a square shape. |
| [Fixed products](https://docs.salla.dev/doc-422589?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | Use this feature to pin the products that you wish to have displayed always. |
| [Products slider](https://docs.salla.dev/doc-422589?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | This slider feature helps navigate between products vertically/horizontally. |
| [Latest Products](https://docs.salla.dev/doc-422599?nav=01HNFTD5Y5ESFQS3P9MJ0721VM) | This feature displays the latest products added to the store automatically. |
|[Vertical Menu with Slider](https://docs.salla.dev/doc-422600?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)|This feature display a vertical menue with slider to display links and images|

<!-- |[Vertical menu with slider](../4.2-Components/4.2.1-Home-components/4.2.20-Home-vertical-menu-with-slider.md)| Used to display a menu for a group of the sub-pages' links in a vertical menu. | -->

### Theme Preview 
The components can be added using the theme preview in the [Theme menu item](https://salla.partners/themes) of Salla Partners Portal. The developer can add the component and enable it in the theme preview dashboard.
![Theme Preview Dashboard](https://cdn.salla.network/docs/twilight/4/theme-preview-dashboard-01.png)


### Usage
Twilight provides two different methods to handle the Home Page via the file [`src/views/pages/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/index.twig). The goal here is to enable the developer to perform any design or appearance he may need for this page.

The methods are:
-	[Theme Features and Theme Components](#theme-features-and-theme-components)
-	[Developer Static Content](#static-content)

### Theme Features and Theme Components
This is the default method, in which the Home Page simply displays the [Theme Features](#components) explained in the previous section of this article.

In addition to the Theme Features, the developer has the option to build their own/new Theme Component(s) as per the store’s requirements. 


:::info[The new Theme Component should be:]
- Created inside the path [`src/views/components/*.twig`](https://github.com/SallaApp/theme-raed/tree/master/src/views/components)
- The schema is declared inside the file [`twilight.json`](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) under the `components` section.
:::

Following is an example of the [`twilight.json`](https://github.com/SallaApp/theme-raed/blob/master/twilight.json) where we delcare a new Theme Component named "custom-slider". 



```js title="./twilight.json" lineNumbers
...
  "components": [
    {
      "name": "custom-slider",
      "title": "صور متحركة (مخصص)",
      "icon": "sicon-image-carousel",
      "path": "home.custom-slider",
      "fields": [
        {
          "id": "images",
          "type": "collection",
          "format": "collection",
          "required": true,
          "minLength": 1,
          "maxLength": 10,
          "fields": [
            {
              "id": "image",
              "type": "string",
              "format": "image"
            },
            {
              "id": "title",
              "type": "string",
              "label": "عنوان رئيسي (إختياري)"
            },
            {
              "id": "sub_title",
              "type": "string",
              "format": "textarea",
              "label": "نص توضيحي (إختياري)"
            }
          ]
        }
      ]
    },

...
```

In the previous example, the `path` of the new component is mentioned in `"path": "home.custom-slider"`. This means that the new component is located inside `src/views/components/home/<USER>

:::tip[Note]
The developer has the option to create their component anywhere within the [`src/views/components/`](https://github.com/SallaApp/theme-raed/tree/master/src/views/components) folder.
:::

Twilight is shipped with some ready Theme components, which can be easily modified by the developer. Below is the list of these Theme Components.

| Components                                        | Description                                                      |
|----------------------------------------------------------------|-----------------------------------------------------|
| [Brands](https://docs.salla.dev/doc-422594?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)| Brands' logos of the store are displayed in this component section. |
| [Main links](https://docs.salla.dev/doc-422596?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)                          | This component helps to portray the store main links.                             |
| [Enhanced Slider](https://docs.salla.dev/doc-422597?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)                 | The slider component helps navigate vertically/horizontally.                      |
| [Slider products with header](https://docs.salla.dev/doc-422598?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)           | Slider products with header displays the products in a slide and give the sldier a header title.                                                                              |
|[Enhanced Square Banners](https://docs.salla.dev/doc-422595?nav=01HNFTD5Y5ESFQS3P9MJ0721VM)|Enhanced banners in a square shape are displayed with this component's help|


Below is the [`src/views/pages/index.twig`](https://github.com/SallaApp/theme-raed/blob/master/src/views/pages/index.twig) file for the Home Page, where, in line #3, the `{% component home %}` renders both of the Theme Features and Theme Components. 

```js title="src/views/pages/index.twig [Line #3]" lineNumbers
{% extends "layouts.master" %}
{% block content %}
    {% component home %}
{% endblock %}
{% block scripts %}
    <script type="text/javascript" defer src="{{ asset('dist/home.js') }}"></script>
{% endblock %}
```

The visibility of each of the Theme Features and Theme Components in the Home Page is managed from the [Partners Portal](https://salla.partners/). This means that the partner has the ability to show/hide any of the Theme Features and Theme Components.

<!--
focus: false
-->

![Theme Features](https://cdn.salla.network/docs/twilight/1/setup-theme-05.png?hi)

#### Static Content
In this method, the developer has the option of building their own static content without using the Theme Features nor Theme Component(s). However it's highly advised to utalize the previous methods to ensure a smooth expereince for the end users.

The added static content does not depend on any of the Store settings. It will be displayed as per the given design by the developer. Below is an example for this method.
 

```php title="src/views/pages/index.twig" lineNumbers
{% extends "layouts.master" %}
{% block content %}

    <-- just add your static content here -->  
    <div id="container">
        <h2> My Home Page </h2>
        <img src="{{ asset('images/my_img.png') }}" alt="" />
    </div>
    
{% endblock %}
{% block scripts %}
    <script type="text/javascript" defer src="{{ asset('dist/home.js') }}"></script>
{% endblock %}
```
