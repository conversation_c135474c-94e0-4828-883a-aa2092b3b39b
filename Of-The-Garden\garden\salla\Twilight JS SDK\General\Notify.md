Alert messages can be used to **notify** the user about a special event, such as a danger, success, information, or warning. For many user-related events, strong alert notification messages are required. 

The **Twilight JS SDK** offers a flexible method for managing notification messages. Its flexibility comes from the fact that the developer can use any external Javascript library as the theme's *notifier* tool in order to manage the display of the notification messages. 

Besides that, the developer has the option of building their own notification message library, *notifier*. It requires a basic knowledge of HTML, CSS, and Javascript. In this way, the theme will have a customized notification message that fits its look-and-feel.

:::info
✅  By default, the **Twilight JS SDK** displays a message to the user that requires their attention using the Javascript built-in `alert()` method.
✅  The developer is required to *include any external Javascript library* along with the theme files. This can be accomplished by inserting an HTML script tag, adding a CDN external link in the page's header, or installing it using *npm*, just like any other Javascript library.
:::
 
In this article, we look at how to include any external JavaScript library for notification messages to make it the theme's notifier tool. We also explain how to use it within the theme.

## 📙 What you'll learn
- [Selecting a notification library](#selecting-a-notification-library).
- [Setup the notifier](#setup-the-notifier).
- [Usage](#usage).

## Selecting a notification library
There are a huge number of Javascript libraries that can be utilized as a nyifier tool for the theme. The developer may check this [link](https://github.com/topics/notifications) to choose any of the themes.
:::info
 [Noty](https://ned.im/noty/) and [AlertifyJS](https://alertifyjs.com/) are two Javascript libraries that stand out for managing notification messages. We use the [Noty](https://ned.im/noty/) for this demonstration.
:::
## Setup the notifier
In order to use the [Noty](https://ned.im/noty/) library, it should first be included within the theme files as we stated previously. After that, the developer needs to call the `setNotifier()` method to set it up. This function wraps the external library by creating an instance of it as an object and then passing the library's basic parameters. 
Creating an object for this library, for example, can be done as follows, according to the [Noty](https://ned.im/noty/) installation [guide](https://ned.im/noty/#/installation?id=es6-import-amp-require-usages):
```js
new Noty({
  type: "info",
  text: "Welcome",
}).show();
```
On that basis, we can use the setNotifier() method to wrap the [Noty](https://ned.im/noty/) and prepare it for us as follows:
```js
salla.notify.setNotifier(function (message, type, data) {
  new Noty({
    type: type,
    text: message,
  }).show();
});
```
This way has the advantage of customizing the behavior of the external library without needing to modify its source code.

## Usage
Now that we have the theme *notifer* ready, it can be called easily as per the case the developer is handling. **Twilight JS SDK** includes three pre-defined cases for displaying the message, which are the `success`, `info`, and the `error` cases.

<Tabs>
  <Tab title="Success">
      
Success notification message indicates a successful user action. They can be displayed as follows:
```js
salla.notify.success("The item has been added to the cart");
```
In this example, the *notifier's* type will be `success`, and the list of the arguments will be passed to the `message` and `data` parameters.

  </Tab>
  <Tab title="Information">


Information notification message shows an informative message for the user. They can be displayed as follows:
```js
salla.notify.info("The item is already sold out");
```
In this example, the *notifier's* type will be `info`, and the list of the arguments will be passed to the `message` and `data` parameters.
      
  </Tab>
  <Tab title="Error">


Error notification messages indicate a failing in the user's action. They can be displayed as follows:
```js
salla.notify.error("An error occured while removing the item from the wishlist");  
```
In this example, the *notifier's* type will be `error`, and the list of the arguments will be passed to the `message` and `data` parameters.
      
  </Tab>
</Tabs>


### Advance Usage

The developer may need to pass additional options when displaying notifications, such as the 'layout' parameter. This can be accomplished as follows:


```js
// lets defind our notifier callback at once in the app
salla.notify.setNotifier(function (message, type, data) {
  new Noty({
    type: type,
    text: message,
    timeout: data.timeout || 20, // use a default value or custom
    layout: data.layout || 'topRight', // use a default value or custom
  }).show();
});

// when you want to pass additional options
salla.notify.success("The item has been added to the cart", {layout: 'centerRight', timeout:10});
```