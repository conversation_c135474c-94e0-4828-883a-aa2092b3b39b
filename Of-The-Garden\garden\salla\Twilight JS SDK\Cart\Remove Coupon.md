A coupon is a code can be removed by customers, this  will revert any added discounted price or limited offer on the cart's items. This *remove* endpoint is used for this purpose.

## Response
<Tabs>
  <Tab title="Success">

<DataSchema id="1427419" />
    
  </Tab>
   <Tab title="Error">
       
<DataSchema id="1427184" />   
       
  </Tab>
  

</Tabs>

## Usage
To perform the action of removing a coupon to the cart's items, the developer may call the `remove()` as shown below.

```js
salla.cart.deleteCoupon().then((response) => {
  /* add your code here */
});
```


## Events
This endpoint may trigger two events, the onCouponDeleted and onCouponDeletionFailed events.

### onCouponDeleted
This event is triggered when removing coupon from the cart's items is done without having any errors coming back from the backend.

```js
ssalla.cart.event.onCouponDeleted((response) => {
  console.log(response)
});
```
### onCouponDeletionFailed
This event is triggered when removing coupon from the cart's items is not completed and an error has occurred.

```js
salla.cart.event.onCouponDeletionFailed((errorMessage) => {
  console.log(errorMessage)
});