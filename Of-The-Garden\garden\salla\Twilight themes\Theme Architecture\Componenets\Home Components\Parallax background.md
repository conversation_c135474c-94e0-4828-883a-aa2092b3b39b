Parallax scrolling background is **_pre-defined component_** that functions as a graphics design method in which the background images move past the camera more slowly than foreground images, giving the impression of depth in a 2D scene from a distance.
This component can be used for a better effect to display the store items or related images.

**Following is the location of this component.**

```shell
└── src 
  ├── views
   ├── components
   |  ├── home
   |  |   ...
   |  |  ├── parallax-background.twig
          ...    
```



### Example
<!--
focus: false
-->
![Parallax Background](https://cdn.salla.network/docs/twilight/4/pages-components-home-parallax-background-01.png)

### Variables


<DataSchema id="1383699" />


### Usage
This component receives `url` variable with `is_opacity`, and set them both as a background for a container `<div>`. That container is set to reflect the `parallax`.
In case of receiving the values of `title` and `link_text`, they will be displayed inside the parallax container.

```php lineNumbers
<div style="background-image: url('{{ image.url }}');">
    {% if title %}
        <h3>{{ title }}</h3>
    {% endif %}

    {% if url and link_text %}
        <a href="{{ url }}">{{ link_text }}</a>
    {% endif %}
</div>
```


