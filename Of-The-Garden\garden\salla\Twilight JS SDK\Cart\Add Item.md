This `addItem` endpoint adds an item from the merchant's store to the customer's shopping cart. The customer may select the item, type in the quantity he wants to order, and click on the "add to cart" button. This sends the product to the shopping cart, and then the customer may continue to shop for other items.

:::tip
The *add item* endpoint has been implemented in the [Add Product](https://docs.salla.dev/doc-422692?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and it's ready for use.

:::


## Payload


<DataSchema id="1387197" />


## Response

<Tabs>
  <Tab title="Success">

<DataSchema id="1427397" />
      
     
  </Tab>
   <Tab title="Error">


<DataSchema id="1427184" />
  </Tab>
  
</Tabs>

## Usage
The method `addItem` adds an item into the cart, the developer may call the method `addItem` as follows:


#### Simple Product

```js
salla.cart.addItem({
    id: 1234,
    quantity: 1,
    notes: "please i need to get the red color"
}).then((response) => {
    /* add your code here */
});
```
#### Variable product

```js
salla.cart.addItem({
    id: 1234,
    quantity: 1,
    options: {
      // option id: option value or option value id (select choice)
      117414452: 11232214, // option value id (select choice)
      117416632: "http://option-value-as-url-of-image.com", // for upload field
      117411132: "option value as string" // for string field
    },
    notes: "please i need to get the red color"
}).then((response) => {
    /* add your code here */
});
```

## Events
This endpoint may trigger two events, the onItemAdded and onItemAddedFailed events.

### onItemAdded
This event is triggered when adding an item to the cart is done without having any errors coming back from the backend.
```js
salla.cart.event.onItemAdded((response, product_id) => {
  console.log(response)
});
```
### onItemAddedFailed
This event is triggered when adding an item to the cart is not completed and an error has occurred. For example, the id of the product to be added to the cart was not found.
```js
salla.cart.event.onItemAddedFailed((errorMessage, product_id) => {
  console.log(errorMessage)
});
```
