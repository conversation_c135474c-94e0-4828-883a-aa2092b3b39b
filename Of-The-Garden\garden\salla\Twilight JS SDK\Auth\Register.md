The customer registration endpoint creates a customer account in the merchant's store. A registration request must provide the customer main information such as *first name, email, phone*, and more. Additionally, the login verification type should be stored in *verfied_by* field, so that later the verification *code* will be sent to that verification type.

<!-- theme: success -->

:::tip
The *register* endpoint has been implemented in the [Login](https://docs.salla.dev/doc-422711?nav=01HNFTE06J4QC24T0D5BPRYKMD) Web Component, and It's all setup to save developer's time and effort.
:::


```mermaid
sequenceDiagram
    autonumber
    Customer->>Regiater Form: Fills registration form
    Note right of Customer:  {`first_name`, `last_name`,  `email`, `phone`,<br/> `country_code`, `country_key`, verfied_by,`code`}
    Regiater Form->>Backend: Sends payload with customer full information, and requests new access code
    Backend-->>Customer: Sends access code to the customer
    loop Access code check
        Customer->>Regiater Form: Enters access code
    end
    Regiater Form->Backend: Sends the recived access code
    Backend->Backend: Verifies the recived access code
    Backend->>Regiater Form: Sends verification response
    alt wrong access code
        Regiater Form->>Customer: Asks to request new access code
    else correct access code
        Regiater Form->>Customer: Finishs registratio process
    end
```

## Payload


<DataSchema id="1387187" />


## Response

<Tabs>
  <Tab title="Success">


<DataSchema id="1427200" />
      
  </Tab>
   <Tab title="Error">



<DataSchema id="1427184" />
  </Tab>
  
</Tabs>

## Usage
The `register()` creates a new customer account. A registration request must provide a user with the following information: `first_name`, `last_name`, `email`, `phone`, `country_code`, and `country_key`. Additionally, the login verification type should be stored in `verfied_by`, so that later the verification `code` will be sent to that verification type.
```js
salla.auth.register({
  first_name: 'Mohammed', 
  last_name: 'Ahmed', 
  phone: '5555555',
  country_code: 'SA'
  country_key: '966',
  verified_by: 'phone',
  code: 123
}).then((response) => {
  /* add your code here */
});

```

## Events
This endpoint may trigger two events, the onRegistered and onCodeNotReSent events.

### onRegistered
This event is triggered when the customer registration process is done without having any errors coming back from the backend.

```js
salla.event.auth.onRegistered((response) => {
  console.log(response)
});
```
### onRegistrationFailed
This event will be triggered when the *register* process is not completed, and an error has occurred.
```js
salla.event.auth.onRegistrationFailed((errorMessage) => {
	console.log(errorMessage)
});
```