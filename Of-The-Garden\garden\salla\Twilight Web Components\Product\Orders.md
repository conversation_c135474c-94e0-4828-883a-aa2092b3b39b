The `<salla-orders>` web component shows a table with order details, such as order ID, product total, order status, and more.


## Example

<!--
focus: false
-->

![Orders Component](https://cdn.salla.network/docs/twilight/6/js-web-orders.png)

## Usage

<Tabs>
  <Tab title="HTML">
      
```html
<salla-orders></salla-orders>
```
  </Tab>      

  
</Tabs>

## Properties

| Property       | Attribute        | Description                                          | Type                   | Default     |
| -------------- | ---------------- | ---------------------------------------------------- | ---------------------- | ----------- |
| Load More Text | `load-more-text` | Load more text                                       | `string`               | `undefined` |
| Params       | `params`               | A query Parameter to send along with the fetch request | `OrderQueryParameters` | `undefined` |
