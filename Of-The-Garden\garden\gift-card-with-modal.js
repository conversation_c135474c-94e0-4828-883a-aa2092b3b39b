// First, let's create the modal with tabs
const giftModalHtml = `
<salla-modal
  id="giftModal"
  title="بطاقة الإهداء والرسالة"
  class="s-modal hydrated"
  width="md">

  <!-- Simple tab system with direct control -->
  <div id="giftTabs" class="gift-tabs">
    <!-- Tab headers -->
    <div class="tab-headers">
      <button id="card_tab_btn" class="tab-header" data-tab="card_tab">اختر بطاقة الإهداء</button>
      <button id="message_tab_btn" class="tab-header" data-tab="message_tab">أضف رسالة</button>
    </div>

    <!-- Tab contents -->
    <div class="tab-contents">
      <!-- Gift Card Tab -->
      <div id="card_tab" class="tab-content">
        <div class="p-3 gift-cards-container">
          <div class="gift-cards-grid">
            <!-- Card options with radio buttons using Salla structure -->
            <label class="gift-card-option s-product-options-option">
              <input type="radio" name="card" value="floral" checked>
              <div class="gift-card-image-container">
                <img src="https://cdn.salla.sa/lGOQjn/tbGq9qkJMOQXVQzEqy21wob204ST7tsla8eiKi4V.png" alt="بطاقة الزهور الملكية">
                <div class="gift-card-selection-indicator">
                  <i class="sicon-check"></i>
                </div>
                <div class="gift-card-checkbox">
                  <i class="sicon-check"></i>
                </div>
                <div class="gift-card-ribbon"><span>مختارة</span></div>
              </div>
              <div class="card-info">
                <span class="card-name">بطاقة فلورا</span>
                <span class="card-price">مجاناً</span>
              </div>
            </label>
            <label class="gift-card-option s-product-options-option">
              <input type="radio" name="card" value="elegant">
              <div class="gift-card-image-container">
                <img src="https://cdn.salla.sa/lGOQjn/SHmZM72C9e2qBCEQmqh5IQrps6RgexssnRwRsUKo.png" alt="بطاقة الأناقة الذهبية">
                <div class="gift-card-selection-indicator">
                  <i class="sicon-check"></i>
                </div>
                <div class="gift-card-checkbox">
                  <i class="sicon-check"></i>
                </div>
                <div class="gift-card-ribbon"><span>مختارة</span></div>
              </div>
              <div class="card-info">
                <span class="card-name">بطاقة أفضل أم على الإطلاق</span>
                <span class="card-price">15 ريال</span>
              </div>
            </label>
            <label class="gift-card-option s-product-options-option">
              <input type="radio" name="card" value="romantic">
              <div class="gift-card-image-container">
                <img src="https://cdn.salla.sa/lGOQjn/8w9mgCLH7V1Ld37Je9u2MGGBSwGWnQth6qqsaTia.png" alt="بطاقة الحب الرومانسية">
                <div class="gift-card-selection-indicator">
                  <i class="sicon-check"></i>
                </div>
                <div class="gift-card-checkbox">
                  <i class="sicon-check"></i>
                </div>
                <div class="gift-card-ribbon"><span>مختارة</span></div>
              </div>
              <div class="card-info">
                <span class="card-name">بطاقة الحب الرومانسية</span>
                <span class="card-price">20 ريال</span>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Message Tab -->
      <div id="message_tab" class="tab-content" style="display: none;">
        <div class="message-form-container">
          <div class="message-form-header">
            <img src="https://cdn.salla.sa/lGOQjn/gtI00xTshTnDsLdx3LSgOpjfeXkmxqEkkiu75qW9.png" alt="اوف ذا جاردن" class="message-form-logo">
          </div>

          <div class="message-form-fields">
            <div class="message-input-wrapper">
              <input type="text" id="giftcard_toInput" class="s-form-control message-input" placeholder="إلى: (اختياري)">
            </div>

            <div class="message-textarea-wrapper">
              <textarea id="giftcard_messageInput" class="s-form-control message-textarea" placeholder="اكتب رسالتك، وعبّر عن مشاعرك"></textarea>
              <p id="giftcard_char_count" class="char-counter">متبقي 192 من الأحرف</p>
            </div>

            <div class="message-actions">
              <span class="message-question">ليس لديك فكرة ماذا تكتب؟</span>
              <button type="button" id="giftcard_suggestionBtn" class="message-suggestion-btn">جرب اقتراحات الرسائل</button>
            </div>

            <div class="message-input-wrapper signature-wrapper">
              <input type="text" id="giftcard_fromInput" class="s-form-control message-input" placeholder="من: (اختياري)">

              <!-- Simple signature button -->
              <button type="button" id="giftcard_signatureBtn" class="signature-btn">
                <i class="sicon-edit-alt"></i> توقيع
              </button>

              <!-- Text signature display (hidden by default) -->
              <div id="text-signature-display" class="signature-display" style="display: none;">
                <span id="text-signature-value" class="signature-value"></span>
                <button type="button" id="text-signature-delete-btn" class="signature-delete-btn">
                  <i class="sicon-trash"></i>
                </button>
              </div>

              <!-- Draw signature display (hidden by default) -->
              <div id="draw-signature-display" class="signature-display" style="display: none;">
                <img id="draw-signature-image" class="signature-image" src="" alt="توقيع">
                <button type="button" id="draw-signature-delete-btn" class="signature-delete-btn">
                  <i class="sicon-trash"></i>
                </button>
              </div>
            </div>

            <div class="message-attachment">
              <button type="button" id="giftcard_attachmentBtn" class="message-attachment-btn">
                <i class="sicon-link"></i>
                نسخ وألصق رابط أغنية أو فيديو
              </button>
            </div>
          </div>

          <!-- Hidden textarea for form submission -->
          <textarea rows="4" class="s-form-control" id="options[391828402]" name="options[391828402]" placeholder="" style="display: none;"></textarea>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Buttons -->
  <div slot="footer" class="modal-buttons">
    <salla-button id="previewBtn" fill="outline" color="primary">معاينة</salla-button>
    <salla-button id="saveBtn" fill="solid" color="primary">حفظ ومتابعة</salla-button>
  </div>
</salla-modal>

<!-- Signature Modal -->
<salla-modal
  id="signatureModal"
  title="أضف توقيعك"
  class="s-modal hydrated"
  width="md">
  <div class="signature-modal-container">
    <!-- Tab headers -->
    <div class="signature-tab-toggle">
      <button id="text_signature_tab_btn" class="signature-tab-btn active">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" fill="none" class="signature-tab-icon">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M13.6675 0.666504H0.916667C0.410667 0.666504 0 1.07625 0 1.58317V3.84734C0 4.35334 0.410667 4.764 0.916667 4.764C1.42267 4.764 1.83333 4.35334 1.83333 3.84734V2.49984H6.31488V13.4998H4.44767C3.94167 13.4998 3.531 13.9096 3.531 14.4165C3.531 14.9234 3.94167 15.3332 4.44767 15.3332H10.1365C10.6425 15.3332 11.0531 14.9234 11.0531 14.4165C11.0531 13.9096 10.6425 13.4998 10.1365 13.4998H8.14821V2.49984H12.7508V3.84734C12.7508 4.35334 13.1615 4.764 13.6675 4.764C14.1735 4.764 14.5841 4.35334 14.5841 3.84734V1.58317C14.5841 1.07625 14.1735 0.666504 13.6675 0.666504Z" fill="currentColor"></path>
        </svg>
        <span>نصي</span>
      </button>
      <button id="draw_signature_tab_btn" class="signature-tab-btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="signature-tab-icon" viewBox="0 0 24 24" fill="none">
          <path d="M6.62656 16.9621C6.39462 16.9615 6.16593 16.9076 5.95813 16.8046C5.75032 16.7015 5.569 16.5522 5.42815 16.3678C5.2873 16.1836 5.19066 15.9693 5.14578 15.7418C5.1009 15.5143 5.10894 15.2795 5.16927 15.0555C5.68754 12.7454 5.90183 10.3775 5.80672 8.01196C5.79958 7.64912 5.92306 7.29577 6.15459 7.01631C6.38612 6.73685 6.71033 6.54986 7.06818 6.48941L12.8944 5.5246C13.0823 5.49465 13.2744 5.54032 13.4288 5.6516C13.5832 5.76289 13.6872 5.93075 13.7182 6.11851C13.7492 6.30626 13.7044 6.49864 13.594 6.65359C13.4835 6.80853 13.3162 6.91345 13.1286 6.94541L7.30244 7.90541C7.35243 10.4364 7.10236 12.9644 6.55748 15.4366L6.63717 15.5154C9.08403 14.9542 11.5948 14.7224 14.1031 14.8261L15.1341 8.94605C15.1708 8.76275 15.2775 8.60089 15.4313 8.49465C15.5851 8.3884 15.7742 8.34607 15.9586 8.37658C16.1431 8.4071 16.3085 8.50807 16.4199 8.6582C16.5313 8.80832 16.5801 8.99587 16.5558 9.18128L15.5891 15.0066C15.5342 15.3671 15.3484 15.6949 15.0672 15.9272C14.7861 16.1595 14.4291 16.2801 14.0647 16.2661C11.6998 16.1716 9.33259 16.3859 7.02306 16.9035C6.89409 16.9405 6.76073 16.9602 6.62656 16.9621Z" fill="currentColor"></path>
          <path d="M14.8756 20.5975C14.0341 20.6146 13.1975 20.4649 12.4141 20.1569C11.1412 19.6814 9.73949 19.6814 8.4666 20.1569C7.68384 20.4658 6.84747 20.6159 6.00613 20.5985C5.81518 20.5985 5.63204 20.5226 5.49702 20.3876C5.36199 20.2526 5.28613 20.0694 5.28613 19.8785C5.28613 19.6875 5.36199 19.5044 5.49702 19.3694C5.63204 19.2343 5.81518 19.1585 6.00613 19.1585C6.68119 19.1762 7.35266 19.0549 7.97893 18.8023C9.56708 18.2136 11.3136 18.2136 12.9018 18.8023C14.1748 19.2772 15.5763 19.2772 16.8493 18.8023C17.6328 18.4935 18.4698 18.3434 19.3117 18.3607C19.5026 18.3607 19.6858 18.4366 19.8209 18.5716C19.9559 18.7066 20.0317 18.8898 20.0317 19.0807C20.0317 19.2717 19.9559 19.4548 19.8209 19.5898C19.6858 19.7249 19.5026 19.8007 19.3117 19.8007C18.636 19.7831 17.9639 19.9043 17.337 20.1569C16.5538 20.4654 15.7172 20.6152 14.8756 20.5975Z" fill="currentColor"></path>
          <path d="M17.3223 10.4496C16.6113 10.4506 15.9289 10.1692 15.4253 9.66723L12.4119 6.65287C12.0216 6.26125 11.7616 5.75882 11.6674 5.21407C11.573 4.66931 11.6489 4.10872 11.8848 3.6087L12.5117 2.28967C12.5505 2.20153 12.6064 2.12206 12.6764 2.05595C12.7464 1.98984 12.829 1.93845 12.9191 1.9048C13.0094 1.87114 13.1054 1.85592 13.2015 1.86003C13.2977 1.86414 13.3921 1.88748 13.4791 1.9287C13.5662 1.96992 13.644 2.02819 13.7081 2.10001C13.7722 2.17185 13.8212 2.2558 13.8523 2.34691C13.8833 2.43803 13.8959 2.53445 13.889 2.63048C13.8822 2.72651 13.8562 2.82019 13.8125 2.90599L13.1866 4.22599C13.0769 4.45712 13.0413 4.71652 13.0848 4.96865C13.1283 5.22077 13.2487 5.45327 13.4295 5.6343L16.4419 8.6487C16.6237 8.82873 16.8563 8.94858 17.1084 8.99202C17.3605 9.03546 17.6198 9.00041 17.8512 8.89159L19.1712 8.26471C19.3438 8.18285 19.542 8.17292 19.7219 8.23711C19.9018 8.3013 20.0488 8.43433 20.1307 8.60695C20.2126 8.77958 20.2225 8.97764 20.1583 9.15757C20.0942 9.33757 19.9611 9.48464 19.7885 9.56643L18.4695 10.1924C18.1108 10.3619 17.719 10.4498 17.3223 10.4496Z" fill="currentColor"></path>
          <path d="M6.06702 16.732C5.92463 16.732 5.78538 16.6898 5.66694 16.6108C5.54851 16.5317 5.45619 16.4193 5.40163 16.2878C5.34707 16.1563 5.33274 16.0116 5.36043 15.8719C5.38814 15.7322 5.45663 15.6039 5.55725 15.5032L9.47307 11.5873C9.53899 11.5166 9.61848 11.4598 9.7068 11.4205C9.79513 11.3811 9.8905 11.3599 9.98717 11.3583C10.0838 11.3565 10.1798 11.3743 10.2695 11.4105C10.3591 11.4468 10.4406 11.5006 10.509 11.569C10.5773 11.6373 10.6312 11.7188 10.6674 11.8085C10.7036 11.8981 10.7214 11.9942 10.7198 12.0908C10.718 12.1875 10.6969 12.2828 10.6575 12.3712C10.6182 12.4595 10.5614 12.539 10.4907 12.6049L6.57578 16.5208C6.50919 16.5879 6.42993 16.6411 6.34258 16.6773C6.25524 16.7136 6.16158 16.7322 6.06702 16.732Z" fill="currentColor"></path>
          <path d="M10.8635 13.179C10.5402 13.1791 10.2219 13.0995 9.9368 12.9473C9.65168 12.795 9.40857 12.5748 9.22893 12.3061C9.04928 12.0374 8.93869 11.7285 8.90695 11.4068C8.87522 11.0852 8.92332 10.7606 9.047 10.4619C9.17066 10.1634 9.36607 9.89985 9.61594 9.6948C9.8658 9.48974 10.1624 9.34948 10.4794 9.28651C10.7965 9.22344 11.1241 9.23966 11.4334 9.33355C11.7426 9.42744 12.024 9.59611 12.2525 9.82478C12.5268 10.0998 12.7135 10.4499 12.789 10.831C12.8644 11.212 12.8252 11.6068 12.6765 11.9657C12.5278 12.3244 12.2761 12.6311 11.9533 12.8471C11.6304 13.0631 11.2508 13.1785 10.8624 13.179H10.8635ZM10.8635 10.6897C10.7595 10.6894 10.6578 10.7199 10.5713 10.7775C10.4848 10.8351 10.4173 10.9171 10.3775 11.0131C10.3377 11.1091 10.3273 11.2148 10.3477 11.3166C10.368 11.4186 10.4183 11.5121 10.4919 11.5854C10.592 11.6806 10.7249 11.7336 10.8629 11.7336C11.001 11.7336 11.1339 11.6806 11.234 11.5854C11.3076 11.5122 11.3578 11.4187 11.3782 11.3169C11.3986 11.2151 11.3882 11.1095 11.3486 11.0135C11.3088 10.9175 11.2415 10.8356 11.1552 10.7779C11.0688 10.7203 10.9673 10.6895 10.8635 10.6897Z" fill="currentColor"></path>
        </svg>
        <span>بخط اليد</span>
      </button>
    </div>

    <!-- Tab contents -->
    <div class="signature-tab-contents">
      <!-- Text Signature Tab -->
      <div id="text_signature_tab" class="signature-tab-content active">
        <div class="text-signature-container">
          <!-- Font selector dropdown -->
          <div class="font-selector-container">
            <label for="font-selector" class="font-selector-label">اختر الخط</label>
            <select id="font-selector" class="font-selector">
              <option value="'Noto Kufi Arabic', sans-serif">Noto Kufi Arabic</option>
              <option value="'Noto Naskh Arabic', serif">Noto Naskh Arabic</option>
              <option value="'Amiri', serif">Amiri</option>
              <option value="'Tajawal', sans-serif" selected>Tajawal</option>
              <option value="'Reem Kufi', sans-serif">Reem Kufi</option>
              <option value="'Caveat', cursive">Caveat</option>
            </select>
          </div>

          <!-- Signature input -->
          <div class="text-signature-input-container">
            <input type="text" id="text-signature-input" class="text-signature-input" placeholder="من: سيف">
          </div>

          <!-- Signature preview -->
          <div class="text-signature-preview-container">
            <label class="preview-label">معاينة التوقيع</label>
            <div id="text-signature-preview" class="text-signature-preview">اكتب توقيعك</div>
          </div>
        </div>
      </div>

      <!-- Draw Signature Tab -->
      <div id="draw_signature_tab" class="signature-tab-content">
        <p class="draw-instructions">ارسم توقيعك داخل الإطار</p>

        <div class="signature-canvas-wrapper">
          <div class="signature-canvas-container">
            <canvas id="signature-canvas" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Buttons -->
  <div slot="footer" class="signature-modal-buttons">
    <salla-button id="clearSignatureBtn" fill="outline" color="danger">مسح</salla-button>
    <salla-button id="confirmSignatureBtn" fill="solid" color="primary">تأكيد</salla-button>
  </div>
</salla-modal>

<!-- Preview Modal -->
<salla-modal
  id="previewModal"
  title="معاينة"
  class="s-modal hydrated"
  width="lg">
  <div class="preview-container">
    <div class="preview-sides">
      <div class="front-side">
        <h4>الجانب الأمامي (الرسالة)</h4>
        <div class="card-preview">
          <p id="preview-recipient" class="preview-text text-center">إلى: حبيبي</p>
          <p id="preview-message" class="preview-text text-center">هديتي لك تعبير بسيط عن تقديري لك، لن يشاء الله تضحك</p>
          <p id="preview-signature" class="preview-text text-center signature-only">توقيعك</p>
          <div class="qr-code-container">
            <div class="qr-card">
              <p class="qr-title">هل استمتعت بالهدية؟</p>
              <div class="qr-content">
                <p class="qr-description">امسح الرمز لإرسال رسالة قصيرة من القلب للمرسل لتعبر عن ودك وامتنانك.</p>
                <div class="qr-image">
                  <img id="preview-qr-code" src="https://api.qrserver.com/v1/create-qr-code/?data=https://example.com&size=80x80" alt="QR Code" class="qr-img">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="back-side">
        <h4>الجانب الخلفي (الغلاف)</h4>
        <img id="preview-card-image" src="https://imagecdn.prod.floward.com/web/images/default_card.png" alt="البطاقة المختارة">
      </div>
    </div>
  </div>
</salla-modal>
`;

// Main gift card selection component
const giftCardHtml = `
<div class="gift-card-container s-block-600">
  <!-- Header with Icon -->
  <div class="gift-header">
    <i class="sicon-gift"></i>
    <span class="gift-title">بطاقة الإهداء والرسالة</span>
  </div>

  <!-- Customization Options -->
  <div class="customization-options">
    <!-- Card Selection -->
    <div class="card-selection card-btn">
      <div class="card-placeholder">
        <i class="sicon-plus"></i>
      </div>
      <div class="card-details">
        <p>اختر بطاقة الإهداء</p>
        <p class="price"></p>
      </div>
    </div>

    <!-- Message Addition -->
    <div class="message-addition message-btn">
      <div class="message-placeholder">
        <i class="sicon-plus"></i>
        <img class="logo-normal" src="https://cdn.salla.sa/cdn-cgi/image/fit=scale-down,width=400,height=400,onerror=redirect,format=auto/lGOQjn/gtI00xTshTnDsLdx3LSgOpjfeXkmxqEkkiu75qW9.png" alt="اوف ذا جاردن، باقات من الورد الطبيعي" height="&quot;48&quot;" style=" height:48px;">
        <p>اضغط هنا لإضافة رسالتك</p>
      </div>
      <p class="message-label">أضف رسالة</p>
    </div>
  </div>

  <!-- Customize Button -->
  <salla-button
    fill="outline"
    color="primary"
    width="wide"
    class="mt-5 customize-btn">
    تخصيص
  </salla-button>
</div>
`;

// Add the CSS
const styles = `
<style>
  /* Import Google Fonts for Arabic signatures */
  @import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;700&family=Noto+Naskh+Arabic:wght@400;700&family=Amiri:wght@400;700&family=Tajawal:wght@400;700&family=Reem+Kufi:wght@400;700&family=Caveat:wght@400;700&display=swap');

  /* Fix for preview modal to appear above all other modals */
  #previewModal {
    z-index: 999999 !important;
  }

  .gift-card-container {
    padding: 20px;
    border: 2px solid var(--color-primary);
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  }

  .gift-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
  }

  .gift-header .sicon-gift {
    color: var(--color-primary);
    font-size: 24px;
  }

  .gift-title {
    font-weight: 600;
    font-size: 18px;
  }

  .customization-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
  }

  .card-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    background: none;
    border: 2px dashed var(--color-primary);
    border-radius: 12px;
    padding: 10px;
    cursor: pointer;
    width: 100%;
  }

  .card-selection .card-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    object-fit: cover;
  }

  .card-placeholder {
    width: 100%;
    aspect-ratio: 1;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--color-grey-50);
    transition: all 0.3s ease;
  }

  .card-placeholder .sicon-plus {
    font-size: 24px;
    color: var(--color-grey-400);
  }

  .card-details {
    width: 100%;
    text-align: center;
  }

  .card-details p {
    margin: 5px 0;
  }

  .card-details .price {
    color: var(--color-primary);
    font-weight: 600;
  }

  .message-addition {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    background: none;
    border: 2px dashed var(--color-primary);
    border-radius: 12px;
    padding: 10px;
    cursor: pointer;
    width: 100%;
  }

  .message-placeholder {
    width: 100%;
    aspect-ratio: 1;
    border: 1px solid var(--color-border);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--color-grey-50);
    transition: all 0.3s ease;
  }

  .message-placeholder .sicon-plus {
    font-size: 24px;
    color: var(--color-grey-400);
    margin-bottom: 8px;
  }

  .message-placeholder p {
    text-align: center;
    color: var(--color-grey-500);
    padding: 0 10px;
  }

  .message-label {
    font-size: 14px;
    color: var(--color-text);
  }

  .card-selection:hover,
  .message-addition:hover {
    border-color: var(--color-primary);
    background-color: var(--color-grey-50);
  }

  .message-addition:hover .message-placeholder {
    border-color: var(--color-primary);
    background-color: var(--color-grey-100);
  }

  .gift-cards-container {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 5px;
    margin-top: 0;
  }

  .gift-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
  }

  @media (max-width: 767px) {
    .gift-cards-grid {
      grid-template-columns: 1fr;
    }

    .preview-sides {
      grid-template-columns: 1fr;
    }

    .card-info {
      padding: 5px 8px;
      font-size: 12px;
    }

    .card-name, .card-price {
      font-size: 12px;
    }

    .gift-card-option {
      max-width: 100%;
      margin: 0 auto;
    }

    .gift-card-image-container {
      max-width: 100%;
      height: auto;
    }
  }

  .gift-card-option {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    overflow: hidden;
  }

  .gift-card-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }

  .gift-card-image-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: 8px;
    border: 1px solid var(--color-grey-200);
    transition: all 0.3s ease;
  }

  .gift-card-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 8px;
  }

  .gift-card-selection-indicator {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .gift-card-selection-indicator i {
    font-size: 14px;
  }

  .gift-card-checkbox {
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .gift-card-checkbox i {
    font-size: 14px;
  }

  .gift-card-option input[type="radio"]:checked ~ .gift-card-image-container {
    border: 3px solid var(--color-primary);
    box-shadow: 0 0 15px rgba(var(--color-primary-rgb), 0.4);
    transform: scale(1.03);
  }

  .gift-card-option input[type="radio"]:checked ~ .gift-card-image-container .gift-card-selection-indicator,
  .gift-card-option input[type="radio"]:checked ~ .gift-card-image-container .gift-card-checkbox {
    opacity: 1;
    transform: scale(1);
  }

  .gift-card-ribbon {
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 80px;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .gift-card-ribbon span {
    position: absolute;
    display: block;
    width: 120px;
    padding: 5px 0;
    background-color: var(--color-primary);
    color: white;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    transform: rotate(45deg);
    right: -30px;
    top: 15px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
  }

  .gift-card-option input[type="radio"]:checked ~ .gift-card-image-container .gift-card-ribbon {
    opacity: 1;
  }

  .gift-card-option:hover .gift-card-image-container {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .card-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 0 0 8px 8px;
  }

  .card-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text);
  }

  .card-price {
    color: var(--color-primary);
    font-weight: 600;
    font-size: 14px;
  }

  .message-form-container {
    padding: 20px;
  }

  .message-form-header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .message-form-logo {
    height: 48px;
    width: auto;
  }

  .message-form-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .message-input-wrapper {
    position: relative;
    width: 100%;
  }

  .signature-wrapper {
    display: flex;
    align-items: center;
  }

  .signature-btn {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-primary);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    transition: all 0.2s ease;
  }

  .signature-btn:hover {
    color: var(--color-primary-dark);
  }

  .signature-display {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding-right: 40px;
  }

  .signature-value {
    font-size: 18px;
    color: var(--color-primary);
    text-align: center;
  }

  .signature-image {
    height: 40px;
    max-width: 150px;
    object-fit: contain;
  }

  .signature-delete-btn {
    background: none;
    border: none;
    color: var(--color-danger);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .signature-delete-btn:hover {
    background-color: rgba(var(--color-danger-rgb), 0.1);
  }

  /* Signature Modal Styles */
  .signature-modal-container {
    padding: 20px;
  }

  .signature-tab-toggle {
    display: flex;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 30px;
    padding: 5px;
    margin-bottom: 30px;
    width: 250px;
    margin-left: auto;
    margin-right: auto;
  }

  .signature-tab-btn {
    padding: 8px 20px;
    background: none;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: center;
  }

  .signature-tab-btn.active {
    color: white;
    background-color: var(--color-primary);
  }

  .signature-tab-icon {
    width: 18px;
    height: 18px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 4px;
  }

  .signature-tab-btn.active .signature-tab-icon {
    color: white;
  }

  .signature-tab-content {
    display: none;
    padding: 10px 0;
  }

  .signature-tab-content.active {
    display: block;
  }

  /* Text Signature Tab */
  .text-signature-container {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .font-selector-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .font-selector-label {
    font-size: 14px;
    color: var(--color-text);
    font-weight: 500;
    text-align: right;
  }

  .font-selector {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    text-align: right;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: border-color 0.2s ease;
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 15px center;
    background-size: 16px;
    padding-left: 40px;
  }

  .font-selector:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  .text-signature-input-container {
    position: relative;
  }

  .text-signature-input {
    width: 100%;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    text-align: right;
    transition: border-color 0.2s ease;
  }

  .text-signature-input:focus {
    outline: none;
    border-color: var(--color-primary);
  }

  .text-signature-preview-container {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .preview-label {
    font-size: 14px;
    color: var(--color-text);
    font-weight: 500;
    text-align: right;
  }

  .text-signature-preview {
    min-height: 60px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    font-size: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Draw Signature Tab */
  .draw-instructions {
    text-align: center;
    color: var(--color-text);
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 500;
  }

  .signature-canvas-wrapper {
    padding: 0 15px;
  }

  .signature-canvas-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
    position: relative;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  /* Signature Pad specific styles */
  .signature-pad-container {
    position: relative;
  }

  .signature-pad-actions {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    gap: 10px;
  }



  #signature-canvas {
    width: 100%;
    height: 200px;
    cursor: crosshair;
    display: block;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  }



  .message-input {
    width: 100%;
    border-radius: 8px;
    padding: 10px 15px;
    border: 1px solid var(--product-border-color);
    text-align: right;
    background-color: var(--color-grey-50);
  }

  .message-textarea-wrapper {
    position: relative;
    width: 100%;
  }

  .message-textarea {
    width: 100%;
    border-radius: 8px;
    padding: 15px;
    min-height: 120px;
    border: 1px solid var(--product-border-color);
    text-align: right;
    resize: vertical;
    background-color: var(--color-grey-50);
  }

  .char-counter {
    position: absolute;
    bottom: 5px;
    left: 15px;
    font-size: 12px;
    color: var(--color-grey-500);
    background-color: transparent;
    padding: 2px 5px;
    border-radius: 4px;
  }

  .message-actions {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .message-question {
    font-size: 14px;
    color: var(--color-text);
  }

  .message-suggestion-btn {
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
  }

  .suggestion-popup {
    position: absolute;
    background-color: white;
    border: 1px solid var(--color-grey-200);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    width: 300px;
    max-width: 90vw;
    overflow: hidden;
    right: 0;
    margin-top: 5px;
  }

  .suggestion-popup-header {
    padding: 10px 15px;
    background-color: var(--color-grey-100);
    border-bottom: 1px solid var(--color-grey-200);
    font-weight: 600;
    text-align: center;
    color: var(--color-primary);
  }

  .suggestion-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .suggestion-item {
    padding: 10px 15px;
    border-bottom: 1px solid var(--color-grey-100);
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: right;
  }

  .suggestion-item:hover {
    background-color: var(--color-grey-50);
    color: var(--color-primary);
  }

  .suggestion-item:last-child {
    border-bottom: none;
  }

  .message-attachment {
    margin-top: 10px;
    text-align: center;
  }

  .message-attachment-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--color-primary);
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
  }

  .message-attachment-btn i {
    font-size: 16px;
  }

  .modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }

  .preview-container {
    padding: 20px;
    margin-top: 30px;
    border: 1px solid var(--color-grey-200);
    border-radius: 8px;
    background-color: white;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .preview-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
    margin: 0;
  }

  .preview-sides {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    position: relative;
  }

  @media (max-width: 768px) {
    .preview-sides {
      grid-template-columns: 1fr;
    }

    .mobile-nav {
      display: flex;
      justify-content: center;
      margin-top: 15px;
      gap: 10px;
    }

    .mobile-nav-btn {
      padding: 8px 15px;
      background-color: var(--color-primary);
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
    }

    .mobile-nav-btn.active {
      background-color: var(--color-primary-dark);
    }
  }

  .front-side, .back-side {
    border: 1px solid var(--color-grey-200);
    border-radius: 8px;
    padding: 20px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  #preview-card-image {
    border: 1px solid var(--color-grey-200);
    border-radius: 1rem;
    max-width: 100%;
    height: auto;
    margin-bottom: 15px;
  }

  .card-preview {
    min-height: 450px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .preview-text {
    font-size: 16px;
    margin: 10px 0;
    padding: 5px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.03);
  }

  .text-center {
    text-align: center;
  }

  .signature-only {
    font-size: 20px;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .qr-code-container {
    margin-top: auto;
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 20px;
  }

  .qr-card {
    display: flex;
    flex-direction: column;
    background-color: #F9F9F9;
    padding: 10px 14px;
    border-radius: 8px;
    margin: 15px 0;
    width: 100%;
    border: 1px solid #e0e0e0;
  }

  .qr-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
  }

  .qr-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    padding: 4px;
    border-radius: 4px;
    min-width: 80px;
  }

  .qr-img {
    width: 80px;
    height: 80px;
  }

  .qr-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--color-text);
    text-align: right;
  }

  .qr-description {
    font-size: 13px;
    color: var(--color-text);
    margin: 0;
    line-height: 1.4;
    text-align: right;
    max-width: 70%;
  }

  /* Custom Tab Styles */
  .gift-tabs {
    width: 100%;
  }

  .tab-headers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    border-bottom: 1px solid var(--color-grey-200);
  }

  .tab-header {
    flex: 1;
    justify-content: center;
    padding: 12px 0;
    color: var(--color-grey-500);
    font-weight: 500;
    transition: all 0.3s ease;
    background: none;
    border: none;
    cursor: pointer;
  }

  .tab-header.active {
    color: var(--color-primary);
    border-bottom: 2px solid var(--color-primary);
  }

  .tab-content {
    display: none;
    margin-top: 0;
    padding-top: 0;
  }

  .tab-content.active {
    display: block;
  }
</style>
`;

// Define all functions first

// Function to open the modal and select a tab
function openGiftModal(tabName) {
  const modal = document.querySelector('#giftModal');

  // Open modal
  modal.open();

  // Select the appropriate tab after a short delay to ensure modal is rendered
  setTimeout(() => {
    console.log(`Attempting to select tab: ${tabName}`);

    // Get all tab headers and contents
    const tabHeaders = document.querySelectorAll('.tab-header');
    const tabContents = document.querySelectorAll('.tab-content');

    console.log(`Found ${tabHeaders.length} tab headers and ${tabContents.length} tab contents`);

    // Remove active class from all tabs
    tabHeaders.forEach(header => {
      header.classList.remove('active');
    });

    tabContents.forEach(content => {
      content.classList.remove('active');
      content.style.display = 'none';
    });

    // Find the tab button and content
    const tabButton = document.querySelector(`#${tabName}_btn`);
    const tabContent = document.querySelector(`#${tabName}`);

    if (tabButton && tabContent) {
      // Add active class to selected tab
      tabButton.classList.add('active');
      tabContent.classList.add('active');
      tabContent.style.display = 'block';
      console.log(`Tab ${tabName} selected successfully`);

      // Initialize character counter if on message tab
      if (tabName === 'message_tab') {
        const charCountEl = document.getElementById('giftcard_char_count');
        const messageTextarea = document.getElementById('giftcard_messageInput');
        if (charCountEl && messageTextarea) {
          const maxLength = 192;
          const currentLength = messageTextarea.value.length;
          const remaining = maxLength - currentLength;
          charCountEl.textContent = `متبقي ${remaining} من الأحرف`;
        }
      }
    } else {
      console.error(`Tab with name ${tabName} not found`);
    }
  }, 100);
}

// Function to update preview content
function updatePreviewContent() {
  console.log('Updating preview content with direct DOM manipulation...');

  try {
    // Get input elements directly
    const toInput = document.getElementById('giftcard_toInput');
    const messageInput = document.getElementById('giftcard_messageInput');
    const fromInput = document.getElementById('giftcard_fromInput');

    // Get values with fallbacks
    const toInputValue = toInput?.value || '';
    const messageInputValue = messageInput?.value || '';
    const fromInputValue = fromInput?.value || '';

    console.log('Input values:', {
      to: toInputValue,
      message: messageInputValue,
      from: fromInputValue
    });

    // Use non-empty values or fallbacks
    const recipient = toInputValue || 'شخص ما';
    const message = messageInputValue || 'هذه هي الرسالة';
    const signature = fromInputValue || 'توقيعك';

    // Get selected card
    const selectedCardElement = document.querySelector('input[name="card"]:checked');
    const selectedCard = selectedCardElement ? selectedCardElement.value : 'default';

    // Force update preview elements with direct DOM manipulation
    const recipientEl = document.getElementById('preview-recipient');
    const messageEl = document.getElementById('preview-message');
    const signatureEl = document.getElementById('preview-signature');
    const cardImageEl = document.getElementById('preview-card-image');

    if (recipientEl) recipientEl.innerHTML = `إلى: <strong>${recipient}</strong>`;
    if (messageEl) {
      // Format message with line breaks
      const formattedMessage = messageInputValue
        .replace(/\n/g, '<br>')
        .replace(/\s{2,}/g, ' &nbsp;');
      messageEl.innerHTML = formattedMessage || 'هذه هي الرسالة';
    }
    if (signatureEl) {
      // Check if we have a drawn signature
      if (fromInput && fromInput.getAttribute('data-signature-type') === 'drawn') {
        const signatureDataURL = fromInput.getAttribute('data-signature-data');
        if (signatureDataURL) {
          signatureEl.innerHTML = `<img src="${signatureDataURL}" alt="توقيع" style="height: 40px; max-width: 150px; display: inline-block;">`;

          // Update the draw signature display in the main form
          const drawSignatureDisplay = document.getElementById('draw-signature-display');
          const drawSignatureImage = document.getElementById('draw-signature-image');
          const signatureBtn = document.getElementById('giftcard_signatureBtn');

          if (drawSignatureDisplay && drawSignatureImage && signatureBtn) {
            drawSignatureImage.src = signatureDataURL;
            drawSignatureDisplay.style.display = 'flex';
            signatureBtn.style.display = 'none';

            // Hide text signature display if visible
            const textSignatureDisplay = document.getElementById('text-signature-display');
            if (textSignatureDisplay) {
              textSignatureDisplay.style.display = 'none';
            }
          }
        } else {
          signatureEl.innerHTML = `<strong>${signature}</strong>`;
        }
      }
      // Check if we have a text signature with custom font
      else if (fromInput && fromInput.getAttribute('data-signature-type') === 'text') {
        const fontFamily = fromInput.getAttribute('data-signature-font');
        if (fontFamily) {
          signatureEl.innerHTML = `<span style="font-family: ${fontFamily}; font-size: 1.5em;">${signature}</span>`;

          // Update the text signature display in the main form
          const textSignatureDisplay = document.getElementById('text-signature-display');
          const textSignatureValue = document.getElementById('text-signature-value');
          const signatureBtn = document.getElementById('giftcard_signatureBtn');

          if (textSignatureDisplay && textSignatureValue && signatureBtn) {
            textSignatureValue.textContent = signature;
            textSignatureValue.style.fontFamily = fontFamily;
            textSignatureDisplay.style.display = 'flex';
            signatureBtn.style.display = 'none';

            // Hide draw signature display if visible
            const drawSignatureDisplay = document.getElementById('draw-signature-display');
            if (drawSignatureDisplay) {
              drawSignatureDisplay.style.display = 'none';
            }
          }
        } else {
          signatureEl.innerHTML = `<strong>${signature}</strong>`;
        }
      }
      // Default signature display
      else {
        signatureEl.innerHTML = `<strong>${signature}</strong>`;

        // Hide both signature displays and show the signature button
        const textSignatureDisplay = document.getElementById('text-signature-display');
        const drawSignatureDisplay = document.getElementById('draw-signature-display');
        const signatureBtn = document.getElementById('giftcard_signatureBtn');

        if (textSignatureDisplay) {
          textSignatureDisplay.style.display = 'none';
        }

        if (drawSignatureDisplay) {
          drawSignatureDisplay.style.display = 'none';
        }

        if (signatureBtn) {
          signatureBtn.style.display = 'flex';
        }
      }
    }

    // Update card image
    const cardImages = {
      'floral': 'https://cdn.salla.sa/lGOQjn/tbGq9qkJMOQXVQzEqy21wob204ST7tsla8eiKi4V.png',
      'elegant': 'https://cdn.salla.sa/lGOQjn/SHmZM72C9e2qBCEQmqh5IQrps6RgexssnRwRsUKo.png',
      'romantic': 'https://cdn.salla.sa/lGOQjn/8w9mgCLH7V1Ld37Je9u2MGGBSwGWnQth6qqsaTia.png',
      'default': 'https://imagecdn.prod.floward.com/web/images/default_card.png'
    };

    if (cardImageEl) cardImageEl.src = cardImages[selectedCard] || cardImages['default'];

    // Update QR code with attached URL if available
    const qrCodeImg = document.getElementById('preview-qr-code');
    if (qrCodeImg) {
      const formContainer = document.querySelector('.message-form-container');
      const attachedUrl = formContainer ? formContainer.getAttribute('data-qr-url') : null;
      const qrUrl = attachedUrl || 'https://example.com';
      qrCodeImg.src = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(qrUrl)}&size=65x65`;
    }

    // Also update the main card display
    updateMainCardDisplay();

    // Update the hidden textarea for form submission
    const hiddenTextarea = document.getElementById('options[391828402]');
    if (hiddenTextarea) {
      const combinedMessage = `To: ${recipient}\nMessage: ${messageInputValue}\nFrom: ${signature}`;
      hiddenTextarea.value = combinedMessage;
    }

    console.log('Preview updated with:', {
      recipient: recipient,
      message: message,
      signature: signature,
      card: selectedCard
    });
  } catch (error) {
    console.error('Error updating preview:', error);
  }
}

// Function to preview the gift card
function previewGiftCard() {
  console.log('Preview button clicked, opening preview modal...');

  const selectedCardElement = document.querySelector('input[name="card"]:checked');

  // Check if a card is selected
  if (!selectedCardElement) {
    alert('الرجاء اختيار بطاقة الإهداء'); // Please select a gift card
    return;
  }

  // Force update the preview content immediately before opening the modal
  updatePreviewContent();

  // Get current input values for logging
  const toValue = document.getElementById('toInput')?.value || '';
  const messageValue = document.getElementById('messageInput')?.value || '';
  const fromValue = document.getElementById('fromInput')?.value || '';

  console.log('Current input values before opening modal:', {
    to: toValue,
    message: messageValue,
    from: fromValue,
    card: selectedCardElement.value
  });

  // Wait for the modal to open before updating content
  const previewModal = document.querySelector('#previewModal');
  previewModal.open();

  // Update preview content multiple times with increasing delays to ensure it works
  setTimeout(updatePreviewContent, 50);
  setTimeout(updatePreviewContent, 150);
  setTimeout(updatePreviewContent, 300);
  setTimeout(updatePreviewContent, 500);
}

// Function to update the main card display
function updateMainCardDisplay() {
  const selectedCardElement = document.querySelector('input[name="card"]:checked');

  // If no card is selected, keep the placeholder
  if (!selectedCardElement) return;

  const selectedCard = selectedCardElement.value;
  const cardImages = {
    'floral': 'https://cdn.salla.sa/lGOQjn/tbGq9qkJMOQXVQzEqy21wob204ST7tsla8eiKi4V.png',
    'elegant': 'https://cdn.salla.sa/lGOQjn/SHmZM72C9e2qBCEQmqh5IQrps6RgexssnRwRsUKo.png',
    'romantic': 'https://cdn.salla.sa/lGOQjn/8w9mgCLH7V1Ld37Je9u2MGGBSwGWnQth6qqsaTia.png'
  };
  const cardNames = {
    'floral': 'بطاقة فلورا',
    'elegant': 'بطاقة أفضل أم على الإطلاق',
    'romantic': 'بطاقة الحب الرومانسية'
  };
  const cardPrices = {
    'floral': 'مجاناً',
    'elegant': '15 ريال',
    'romantic': '20 ريال'
  };

  // Get the card selection container
  const cardSelectionContainer = document.querySelector('.card-selection');
  const cardDetails = document.querySelector('.card-details');
  const mainCardName = document.querySelector('.card-details p:first-child');
  const mainCardPrice = document.querySelector('.card-details .price');

  // Remove placeholder if it exists
  const placeholder = cardSelectionContainer.querySelector('.card-placeholder');
  if (placeholder) {
    placeholder.remove();
  }

  // Check if image already exists, if not create it
  let mainCardImage = cardSelectionContainer.querySelector('.card-image');
  if (!mainCardImage) {
    mainCardImage = document.createElement('img');
    mainCardImage.className = 'card-image';
    mainCardImage.alt = cardNames[selectedCard];
    cardSelectionContainer.insertBefore(mainCardImage, cardDetails);
  }

  // Update the card details
  mainCardImage.src = cardImages[selectedCard];
  if (mainCardName) mainCardName.textContent = cardNames[selectedCard];
  if (mainCardPrice) mainCardPrice.textContent = cardPrices[selectedCard];
}

// Function to save the gift card customization
function saveGiftCardCustomization() {
  const recipient = document.getElementById('toInput').value;
  const message = document.getElementById('messageInput').value;
  const signature = document.getElementById('fromInput').value;
  const selectedCardElement = document.querySelector('input[name="card"]:checked');

  // Check if a card is selected
  if (!selectedCardElement) {
    alert('الرجاء اختيار بطاقة الإهداء'); // Please select a gift card
    return;
  }

  const selectedCard = selectedCardElement.value;

  // Combine the message data into the hidden textarea
  const combinedMessage = `To: ${recipient}\nMessage: ${message}\nFrom: ${signature}`;
  document.getElementById('options[391828402]').value = combinedMessage;

  console.log({
    recipient,
    message,
    signature,
    selectedCard,
    combinedMessage
  });

  // Update the main card display
  updateMainCardDisplay();

  alert('تم حفظ التخصيص! جاهز للمتابعة.');
  document.querySelector('#giftModal').close();
}

// Function to initialize the selection indicators
function initializeSelectionIndicators() {
  // Find the checked radio button
  const checkedRadio = document.querySelector('input[name="card"]:checked');
  if (checkedRadio) {
    // Show the indicators for the selected card
    const selectedCard = checkedRadio.closest('.gift-card-option');
    if (selectedCard) {
      const indicator = selectedCard.querySelector('.gift-card-selection-indicator');
      const checkbox = selectedCard.querySelector('.gift-card-checkbox');
      if (indicator) {
        indicator.style.opacity = '1';
      }
      if (checkbox) {
        checkbox.style.opacity = '1';
      }
    }
  }
}

// Function to set up event listeners
function setupEventListeners() {
  // Message button
  const messageBtn = document.querySelector('.message-btn');
  if (messageBtn) {
    messageBtn.addEventListener('click', () => {
      console.log('Message button clicked, opening message_tab');
      openGiftModal('message_tab');
    });
  } else {
    console.error('Message button not found');
  }

  // Card button
  const cardBtn = document.querySelector('.card-btn');
  if (cardBtn) {
    cardBtn.addEventListener('click', () => {
      console.log('Card button clicked, opening card_tab');
      openGiftModal('card_tab');
    });
  } else {
    console.error('Card button not found');
  }

  // Customize button
  const customizeBtn = document.querySelector('.customize-btn');
  if (customizeBtn) {
    customizeBtn.addEventListener('click', () => {
      console.log('Customize button clicked, opening message_tab');
      openGiftModal('message_tab');
    });
  } else {
    console.error('Customize button not found');
  }

  // Tab header buttons inside the modal
  const tabButtons = document.querySelectorAll('.tab-header');
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.getAttribute('data-tab');
      console.log(`Tab button clicked for ${tabName}`);

      // Get all tab headers and contents
      const tabHeaders = document.querySelectorAll('.tab-header');
      const tabContents = document.querySelectorAll('.tab-content');

      // Remove active class from all tabs
      tabHeaders.forEach(header => {
        header.classList.remove('active');
      });

      tabContents.forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none';
      });

      // Add active class to clicked tab
      button.classList.add('active');
      const tabContent = document.querySelector(`#${tabName}`);
      if (tabContent) {
        tabContent.classList.add('active');
        tabContent.style.display = 'block';
      }
    });
  });

  // Preview button
  const previewBtn = document.querySelector('#previewBtn');
  if (previewBtn) {
    previewBtn.addEventListener('click', previewGiftCard);
  } else {
    console.error('Preview button not found');
  }

  // Save button
  const saveBtn = document.querySelector('#saveBtn');
  if (saveBtn) {
    saveBtn.addEventListener('click', saveGiftCardCustomization);
  } else {
    console.error('Save button not found');
  }

  // Initialize character counter on page load
  const initCharCounter = () => {
    const messageTextarea = document.getElementById('giftcard_messageInput');
    const charCountEl = document.getElementById('giftcard_char_count');
    if (messageTextarea && charCountEl) {
      const maxLength = 192;
      const currentLength = messageTextarea.value.length;
      const remaining = maxLength - currentLength;
      charCountEl.textContent = `متبقي ${remaining} من الأحرف`;
    }
  };

  // Call this on page load
  setTimeout(initCharCounter, 100);

  // Character counter for message and live preview update
  const messageTextarea = document.getElementById('giftcard_messageInput');
  if (messageTextarea) {
    messageTextarea.addEventListener('input', function() {
      // Update character counter
      const maxLength = 192;
      const currentLength = this.value.length;
      const remaining = maxLength - currentLength;
      const charCountEl = document.getElementById('giftcard_char_count');
      if (charCountEl) {
        charCountEl.textContent = `متبقي ${remaining} من الأحرف`;
      }

      // Always update preview when input changes
      updatePreviewContent();
      // Double-check after another short delay to ensure content is updated
      setTimeout(updatePreviewContent, 50);
    });
  } else {
    console.error('Message textarea not found');
  }

  // Message suggestion button
  console.log('Looking for suggestion button with ID: giftcard_suggestionBtn');
  const suggestionBtn = document.getElementById('giftcard_suggestionBtn');
  console.log('Suggestion button found:', suggestionBtn);
  if (suggestionBtn) {
    // Make sure the message textarea is properly initialized
    const initMessageTextarea = () => {
      const messageTextarea = document.getElementById('giftcard_messageInput');
      if (messageTextarea) {
        // Ensure it's properly initialized
        messageTextarea.focus();
        messageTextarea.blur();
      }
    };

    // Call this when the modal is opened
    setTimeout(initMessageTextarea, 200);

    suggestionBtn.addEventListener('click', function() {
      console.log('Suggestion button clicked');

      // Create modal if it doesn't exist
      let suggestionModal = document.getElementById('suggestionModal');
      if (!suggestionModal) {
        console.log('Creating suggestion modal');

        // Define suggestion categories and messages
        const suggestionCategories = [
          { id: 'general', name: 'عامة', active: true },
          { id: 'graduation', name: 'مبروك التخرج', active: false },
          { id: 'health', name: 'دعاء بالشفاء', active: false },
          { id: 'baby', name: 'تهنئة بالمولود', active: false },
          { id: 'birthday', name: 'عيد ميلاد سعيد', active: false }
        ];

        const suggestionMessages = {
          general: [
            'أرسل لك هذه الهدية من القلب، نستاهل كل خير',
            'هديتي لك تعبير بسيط عن تقديري لك، إن شاء الله تعجبك',
            'كل يوم فرصة نقدر فيها وجودك، وهالهدية تعبير بسيط عن تقديري لك',
            'بدون سبب معين، هدية بسيطة عشان أقول لك إنك دائماً على بالي',
            'ما تحتاج مناسبة عشان أدرك كم أنت غالي، هديتي لك بس عشان تنور يومك'
          ],
          graduation: [
            'مبروك التخرج! أنت مثال للإصرار والنجاح',
            'تهانينا بالتخرج، بداية مشوار جديد من النجاح',
            'فخورين بإنجازك وتخرجك، تستاهل كل خير',
            'مبروك التخرج، هذه الهدية البسيطة تعبير عن فخرنا بك',
            'نبارك لك التخرج ونتمنى لك مستقبلاً مشرقاً'
          ],
          health: [
            'سلامات، ما تشوف شر، الله يشفيك ويعافيك',
            'الله يشفيك شفاءً لا يغادر سقماً، وهذه الهدية لتخفيف ألمك',
            'سلامتك من كل شر، الله يلبسك ثوب الصحة والعافية',
            'ما تشوف شر، الله يقومك بالسلامة ويرجعك أحسن من أول',
            'الله يشفيك ويعافيك، وهذه الهدية تعبير بسيط عن محبتنا'
          ],
          baby: [
            'مبروك المولود الجديد، الله يجعله من الذرية الصالحة',
            'تهانينا بقدوم المولود، الله يجعله قرة عين لكم',
            'مبروك الطفل الجديد، الله يحفظه ويبارك فيه',
            'الله يبارك لكم في الموهوب، ويشكر الواهب، ويرزقكم بره',
            'مبروك المولود، الله يجعله من حفظة كتابه وصالح عباده'
          ],
          birthday: [
            'كل عام وأنت بخير، أتمنى لك عاماً مليئاً بالسعادة والنجاح',
            'عيد ميلاد سعيد، أتمنى لك عاماً استثنائياً مليئاً بالإنجازات',
            'في يوم ميلادك، أتمنى لك كل السعادة والتوفيق',
            'كل سنة وأنت طيب، الله يحفظك ويديم عليك الصحة والعافية',
            'عيد ميلاد سعيد، أتمنى لك عاماً مليئاً بالفرح والسرور'
          ]
        };

        // Create modal HTML
        let categoriesHTML = '';
        suggestionCategories.forEach(category => {
          const activeClass = category.active ? 'active' : '';
          categoriesHTML += `<div class="suggestion-category ${activeClass}" data-category="${category.id}">${category.name}</div>`;
        });

        let messagesHTML = '';
        const activeCategory = suggestionCategories.find(cat => cat.active).id;
        suggestionMessages[activeCategory].forEach(message => {
          messagesHTML += `<div class="suggestion-message">${message}</div>`;
        });

        const modalHTML = `
        <div id="suggestionModal" class="suggestion-modal">
          <div class="suggestion-modal-overlay"></div>
          <div class="suggestion-modal-content">
            <div class="suggestion-modal-header">
              <h3 class="suggestion-modal-title">الرسائل المقترحة</h3>
              <div class="suggestion-language-toggle">
                <span class="language-option active">AR</span>
                <span class="language-option">EN</span>
              </div>
              <button type="button" class="suggestion-modal-close">
                <i class="sicon-cancel"></i>
              </button>
            </div>

            <div class="suggestion-categories">
              ${categoriesHTML}
            </div>

            <div class="suggestion-messages">
              ${messagesHTML}
            </div>
          </div>
        </div>
        `;

        // Add modal styles
        const modalStyles = `
        <style>
          .suggestion-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 99999;
            display: none;
            justify-content: center;
            align-items: center;
          }

          .suggestion-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
          }

          .suggestion-modal-content {
            position: relative;
            width: 600px;
            max-width: 90%;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          }

          .suggestion-modal-header {
            position: relative;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--color-grey-200);
          }

          .suggestion-modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            text-align: center;
            flex-grow: 1;
          }

          .suggestion-language-toggle {
            display: flex;
            background-color: #f5f5f5;
            border-radius: 20px;
            overflow: hidden;
          }

          .language-option {
            padding: 5px 10px;
            cursor: pointer;
            font-size: 14px;
          }

          .language-option.active {
            background-color: var(--color-primary);
            color: white;
          }

          .suggestion-modal-close {
            position: absolute;
            top: 15px;
            left: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--color-grey-500);
          }

          .suggestion-categories {
            display: flex;
            overflow-x: auto;
            padding: 15px;
            border-bottom: 1px solid var(--color-grey-200);
            gap: 10px;
          }

          .suggestion-category {
            padding: 8px 15px;
            border-radius: 20px;
            background-color: #f5f5f5;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.2s ease;
          }

          .suggestion-category.active {
            background-color: var(--color-primary);
            color: white;
          }

          .suggestion-messages {
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 10px;
          }

          .suggestion-message {
            padding: 15px;
            border: 1px solid var(--color-grey-200);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: right;
          }

          .suggestion-message:hover {
            border-color: var(--color-primary);
            background-color: rgba(var(--color-primary-rgb), 0.05);
          }
        </style>
        `;

        // Add modal and styles to the document
        console.log('Adding suggestion modal styles to document head');
        document.head.insertAdjacentHTML('beforeend', modalStyles);

        console.log('Adding suggestion modal HTML to document body');
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get the modal element after adding it to the DOM
        console.log('Getting suggestion modal element from DOM');
        suggestionModal = document.getElementById('suggestionModal');
        console.log('Suggestion modal element after adding to DOM:', suggestionModal);

        // Add event listeners for the modal

        // Category switching
        const categoryButtons = suggestionModal.querySelectorAll('.suggestion-category');
        categoryButtons.forEach(button => {
          button.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-category');
            console.log('Category clicked:', categoryId);

            // Update active class
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Update messages
            const messagesContainer = suggestionModal.querySelector('.suggestion-messages');
            let newMessagesHTML = '';
            suggestionMessages[categoryId].forEach(message => {
              newMessagesHTML += `<div class="suggestion-message">${message}</div>`;
            });
            messagesContainer.innerHTML = newMessagesHTML;

            // Add click events to new messages
            addMessageClickEvents();
          });
        });

        // Function to add click events to messages
        function addMessageClickEvents() {
          const messageElements = suggestionModal.querySelectorAll('.suggestion-message');
          messageElements.forEach(message => {
            message.addEventListener('click', function() {
              const messageText = this.textContent;
              console.log('Message selected:', messageText);

              // Set the message in the textarea
              const messageInput = document.getElementById('giftcard_messageInput');
              if (messageInput) {
                // Store the suggestion text
                const suggestionText = messageText;

                // Multiple approaches to ensure the value is set
                // 1. Direct property assignment
                messageInput.value = suggestionText;

                // 2. Force focus and set value
                messageInput.focus();
                messageInput.value = suggestionText;

                // 3. Force update with setTimeout
                setTimeout(() => {
                  // Double-check and retry if needed
                  if (messageInput.value !== suggestionText) {
                    console.log('Value not set correctly, retrying...');
                    messageInput.value = suggestionText;
                    messageInput.setAttribute('value', suggestionText);
                  }

                  // Trigger multiple events to ensure all listeners are notified
                  const events = ['input', 'change', 'keyup', 'keydown'];
                  events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    messageInput.dispatchEvent(event);
                  });

                  // Update character counter directly
                  const charCountEl = document.getElementById('giftcard_char_count');
                  if (charCountEl) {
                    const maxLength = 192;
                    const currentLength = suggestionText.length;
                    const remaining = maxLength - currentLength;
                    charCountEl.textContent = `متبقي ${remaining} من الأحرف`;
                  }

                  // Force update preview content multiple times
                  updatePreviewContent();
                  setTimeout(updatePreviewContent, 100);
                  setTimeout(updatePreviewContent, 300);
                }, 50);

                // Close modal
                suggestionModal.style.display = 'none';
              }
            });
          });
        }

        // Initial call to add click events to messages
        addMessageClickEvents();

        // Close button
        const closeModalBtn = suggestionModal.querySelector('.suggestion-modal-close');
        if (closeModalBtn) {
          console.log('Adding click event to suggestion modal close button');
          closeModalBtn.addEventListener('click', function() {
            console.log('Suggestion modal close button clicked');
            suggestionModal.style.display = 'none';
          });
        }

        // Close when clicking overlay
        const overlay = suggestionModal.querySelector('.suggestion-modal-overlay');
        if (overlay) {
          overlay.addEventListener('click', function() {
            suggestionModal.style.display = 'none';
          });
        }

        // Language toggle (just visual for now)
        const languageOptions = suggestionModal.querySelectorAll('.language-option');
        languageOptions.forEach(option => {
          option.addEventListener('click', function() {
            languageOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            // In a real implementation, you would switch the language of suggestions here
          });
        });
      }

      // Open the modal
      console.log('Opening suggestion modal');
      suggestionModal.style.display = 'flex';
    });
  } else {
    console.error('Suggestion button not found');
  }

  // Attachment button - Open modal instead of prompt
  console.log('Looking for attachment button with ID: giftcard_attachmentBtn');
  const attachmentBtn = document.getElementById('giftcard_attachmentBtn');
  console.log('Attachment button found:', attachmentBtn);
  if (attachmentBtn) {
    console.log('Adding click event listener to attachment button');
    attachmentBtn.addEventListener('click', function() {
      console.log('Attachment button clicked');
      // Create modal if it doesn't exist
      let attachmentModal = document.getElementById('attachmentModal');
      if (!attachmentModal) {
        // Create the modal HTML
        const modalHTML = `
        <div id="attachmentModal" class="attachment-modal">
          <div class="attachment-modal-overlay"></div>
          <div class="attachment-modal-content">
            <div class="attachment-modal-header">
              <h3 class="attachment-modal-title">أضف فيديو أو صورة <i class="sicon-play-circle"></i></h3>
              <button type="button" class="attachment-modal-close">
                <i class="sicon-cancel"></i>
              </button>
            </div>

            <div class="attachment-modal-body">
              <p class="attachment-modal-description">لا تجد الكلمات المناسبة؟ عبر عن نفسك بفيديو أو صورة.</p>

              <ul class="attachment-features">
                <li class="attachment-feature">
                  <i class="sicon-link"></i>
                  انسخ رابط لفيديو أو صورة
                </li>
                <li class="attachment-feature">
                  <i class="sicon-qrcode"></i>
                  سيتم إضافة رمز QR إلى البطاقة
                </li>
                <li class="attachment-feature">
                  <i class="sicon-gift"></i>
                  مجاناً!
                </li>
              </ul>

              <div class="attachment-input-container">
                <input type="text" id="attachmentUrlInput" class="attachment-url-input" placeholder="انسخ الرابط هنا">
                <button type="button" class="attachment-paste-btn">
                  <i class="sicon-link"></i>
                </button>
              </div>

              <!-- URL Preview Container -->
              <div id="url-preview-container" class="url-preview-container" style="display: none;">
                <div class="url-preview-content">
                  <div class="url-preview-image">
                    <img id="url-preview-img" src="" alt="معاينة الرابط">
                  </div>
                  <div class="url-preview-info">
                    <h4 id="url-preview-title" class="url-preview-title"></h4>
                    <p id="url-preview-description" class="url-preview-description"></p>
                  </div>
                </div>
              </div>

              <button type="button" id="addToMessageBtn" class="attachment-submit-btn">أضف للرسالة</button>
            </div>
          </div>
        </div>
        `;

        // Add modal styles
        const modalStyles = `
        <style>
          .attachment-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 99999; /* Increased z-index to appear above other modals */
            display: none;
            justify-content: center;
            align-items: center;
          }

          .attachment-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
          }

          .attachment-modal-content {
            position: relative;
            width: 550px;
            max-width: 90%;
            background-color: white;
            padding: 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          }

          .attachment-modal-header {
            position: relative;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid var(--color-grey-200);
          }

          .attachment-modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
          }

          .attachment-modal-title i {
            color: var(--color-primary);
            font-size: 24px;
          }

          .attachment-modal-close {
            position: absolute;
            top: 15px;
            left: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--color-grey-500);
          }

          .attachment-modal-body {
            padding: 20px;
          }

          .attachment-modal-description {
            text-align: center;
            margin-bottom: 20px;
            color: var(--color-grey-700);
          }

          .attachment-features {
            list-style: none;
            padding: 0;
            margin: 0 0 25px 0;
            display: flex;
            flex-direction: column;
            gap: 10px;
          }

          .attachment-feature {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--color-grey-700);
            font-size: 14px;
          }

          .attachment-feature i {
            color: var(--color-primary);
            font-size: 16px;
          }

          .attachment-input-container {
            position: relative;
            margin-bottom: 20px;
          }

          .attachment-url-input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgb(235, 235, 235);
            border-radius: 8px;
            font-size: 14px;
            direction: rtl;
            text-align: right;
            padding-left: 50px;
          }

          .attachment-url-input:focus {
            outline: none;
            border-color: var(--color-primary);
          }

          .attachment-paste-btn {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--color-primary);
            font-size: 18px;
            cursor: pointer;
          }

          .attachment-submit-btn {
            width: 100%;
            padding: 12px;
            background-color: var(--color-primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
          }

          .attachment-submit-btn:hover {
            background-color: var(--color-primary-dark, #0056b3);
          }

          /* URL Preview Styles */
          .url-preview-container {
            margin: 15px 0;
            border: 1px solid #e0e0e0;
            border-radius: 1rem;
            overflow: hidden;
          }

          .url-preview-content {
            display: flex;
            align-items: center;
            gap: 15px;
          }

          .url-preview-image {
            width: 120px;
            height: 120px;
            overflow: hidden;
            flex-shrink: 0;
          }

          .url-preview-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border: 1px solid #e0e0e0;
            border-radius: 1rem;
          }

          .url-preview-info {
            flex: 1;
            padding: 10px 10px 10px 0;
          }

          .url-preview-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: var(--color-text);
          }

          .url-preview-description {
            font-size: 14px;
            color: var(--color-grey-600);
            margin: 0;
            line-height: 1.4;
          }
        </style>
        `;

        // Add modal and styles to the document
        console.log('Adding modal styles to document head');
        document.head.insertAdjacentHTML('beforeend', modalStyles);

        console.log('Adding modal HTML to document body');
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get the modal element after adding it to the DOM
        console.log('Getting modal element from DOM');
        attachmentModal = document.getElementById('attachmentModal');
        console.log('Modal element after adding to DOM:', attachmentModal);

        // Add event listeners for the modal
        const addToMessageBtn = document.getElementById('addToMessageBtn');
        if (addToMessageBtn) {
          addToMessageBtn.addEventListener('click', function() {
            const urlInput = document.getElementById('attachmentUrlInput');
            if (urlInput && urlInput.value.trim() !== '') {
              const url = urlInput.value.trim();

              // Store the URL for QR code generation
              const messageInput = document.getElementById('giftcard_messageInput');
              if (messageInput) {
                // Store the URL as a data attribute on the form for QR code generation
                const formContainer = document.querySelector('.message-form-container');
                if (formContainer) {
                  formContainer.setAttribute('data-qr-url', url);
                }

                // Update the QR code in the preview
                const qrCodeImg = document.getElementById('preview-qr-code');
                if (qrCodeImg) {
                  qrCodeImg.src = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(url)}&size=65x65`;
                }

                // Update the URL preview image in the attachment modal
                const previewImg = document.getElementById('url-preview-img');
                if (previewImg && previewImg.src) {
                  // If we have a preview image from YouTube or another source, use it for the QR code
                  const qrImage = document.querySelector('.qr-image');
                  if (qrImage) {
                    qrImage.innerHTML = `<img src="${previewImg.src}" alt="QR Code" class="qr-img" style="width: 65px; height: 65px; object-fit: cover; border-radius: 1rem;">`;
                  }
                }

                // Trigger the input event to update character count and preview
                const inputEvent = new Event('input', { bubbles: true });
                messageInput.dispatchEvent(inputEvent);

                // Force update preview content
                updatePreviewContent();

                // Clear the input field
                urlInput.value = '';

                // Close the modal
                console.log('Closing modal after adding message');
                attachmentModal.style.display = 'none';
              }
            }
          });
        }

        // Function to show URL preview
        function showUrlPreview(url) {
          if (!url || !url.trim()) return;

          const previewContainer = document.getElementById('url-preview-container');
          const previewImg = document.getElementById('url-preview-img');
          const previewTitle = document.getElementById('url-preview-title');
          const previewDesc = document.getElementById('url-preview-description');

          // Simple URL validation
          if (!url.match(/^https?:\/\//i)) {
            url = 'https://' + url;
          }

          // For YouTube links, extract video ID and show thumbnail
          if (url.includes('youtube.com') || url.includes('youtu.be')) {
            let videoId = '';

            if (url.includes('youtube.com/watch')) {
              const urlObj = new URL(url);
              videoId = urlObj.searchParams.get('v');
            } else if (url.includes('youtu.be/')) {
              videoId = url.split('youtu.be/')[1].split('?')[0];
            }

            if (videoId) {
              previewImg.src = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(url)}`;
              previewTitle.textContent = 'فيديو يوتيوب';
              previewDesc.textContent = url;
              previewContainer.style.display = 'block';
              return;
            }
          }

          // For other URLs, use the QR code API
          previewImg.src = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(url)}`;
          previewTitle.textContent = 'رابط مرفق';
          previewDesc.textContent = url;
          previewContainer.style.display = 'block';
        }

        // URL input event listener
        const urlInput = document.getElementById('attachmentUrlInput');
        if (urlInput) {
          urlInput.addEventListener('input', function() {
            const url = this.value.trim();
            showUrlPreview(url);
          });
        }

        // Paste button functionality
        const pasteBtn = document.querySelector('.attachment-paste-btn');
        if (pasteBtn) {
          pasteBtn.addEventListener('click', async function() {
            try {
              const text = await navigator.clipboard.readText();
              const urlInput = document.getElementById('attachmentUrlInput');
              if (urlInput) {
                urlInput.value = text;
                // Show preview after pasting
                showUrlPreview(text);
              }
            } catch (err) {
              console.error('Failed to read clipboard contents: ', err);
              // Fallback for browsers that don't support clipboard API
              const urlInput = document.getElementById('attachmentUrlInput');
              if (urlInput) {
                urlInput.focus();
                alert('الرجاء لصق الرابط يدوياً باستخدام Ctrl+V أو ⌘+V');
              }
            }
          });
        }

        // Close modal button
        const closeModalBtn = document.querySelector('.attachment-modal-close');
        if (closeModalBtn) {
          console.log('Adding click event to close button');
          closeModalBtn.addEventListener('click', function() {
            console.log('Close button clicked');
            // Always use our custom modal display
            attachmentModal.style.display = 'none';
          });
        }
      }

      // Open the modal
      console.log('Attempting to open modal, attachmentModal:', attachmentModal);

      // Always use our custom modal display
      console.log('Using custom modal display');
      attachmentModal.style.display = 'flex';

      // Add click event to overlay to close modal
      const overlay = attachmentModal.querySelector('.attachment-modal-overlay');
      if (overlay) {
        overlay.addEventListener('click', function() {
          attachmentModal.style.display = 'none';
        });
      }

      console.log('Modal should be open now');
    });
  } else {
    console.error('Attachment button not found');
  }

  // Get signature button
  const signatureBtn = document.getElementById('giftcard_signatureBtn');

  // Signature button - Open modal
  console.log('Looking for signature button with ID: giftcard_signatureBtn');
  console.log('Signature button found:', signatureBtn);
  if (signatureBtn) {
    console.log('Adding click event listener to signature button');
    signatureBtn.addEventListener('click', function() {
      console.log('Signature button clicked');

      // Get the signature modal
      const signatureModal = document.getElementById('signatureModal');
      if (signatureModal) {
        // Get the current signature type and data
        const fromInput = document.getElementById('giftcard_fromInput');
        const signatureType = fromInput ? fromInput.getAttribute('data-signature-type') : null;
        const signatureFont = fromInput ? fromInput.getAttribute('data-signature-font') : null;
        const signatureValue = fromInput ? fromInput.value : '';

        // Activate the appropriate tab based on previous selection
        const textSignatureTab = document.getElementById('text_signature_tab');
        const textSignatureTabBtn = document.getElementById('text_signature_tab_btn');
        const drawSignatureTab = document.getElementById('draw_signature_tab');
        const drawSignatureTabBtn = document.getElementById('draw_signature_tab_btn');

        if (textSignatureTab && textSignatureTabBtn && drawSignatureTab && drawSignatureTabBtn) {
          if (signatureType === 'drawn') {
            // Activate draw tab
            textSignatureTab.classList.remove('active');
            textSignatureTabBtn.classList.remove('active');
            drawSignatureTab.classList.add('active');
            drawSignatureTabBtn.classList.add('active');
          } else {
            // Default to text tab
            textSignatureTab.classList.add('active');
            textSignatureTabBtn.classList.add('active');
            drawSignatureTab.classList.remove('active');
            drawSignatureTabBtn.classList.remove('active');

            // If there's a text signature, restore its value and font
            if (signatureType === 'text') {
              const textInput = document.getElementById('text-signature-input');
              const fontSelector = document.getElementById('font-selector');
              const textPreview = document.getElementById('text-signature-preview');

              if (textInput && signatureValue) {
                textInput.value = signatureValue;
              }

              if (fontSelector && signatureFont) {
                // Find and select the option with the matching font
                for (let i = 0; i < fontSelector.options.length; i++) {
                  if (fontSelector.options[i].value === signatureFont) {
                    fontSelector.selectedIndex = i;
                    break;
                  }
                }
              }

              // Update the preview
              if (textPreview && signatureValue) {
                textPreview.textContent = signatureValue;
                if (signatureFont) {
                  textPreview.style.fontFamily = signatureFont;
                }
              }
            }
          }
        }
        // Add Signature Pad library if not already added
        if (!document.getElementById('signature-pad-script')) {
          const script = document.createElement('script');
          script.id = 'signature-pad-script';
          script.src = 'https://cdn.jsdelivr.net/npm/signature_pad@4.1.7/dist/signature_pad.umd.min.js';
          script.onload = function() {
            console.log('Signature Pad library loaded');
            initializeSignaturePad();
          };
          document.head.appendChild(script);
        } else {
          // If script is already loaded, initialize signature pad directly
          initializeSignaturePad();
        }

        // Initialize Signature Pad
        function initializeSignaturePad() {
          const canvas = document.getElementById('signature-canvas');
          if (canvas && typeof SignaturePad !== 'undefined') {
            console.log('Initializing Signature Pad');

            // Create Signature Pad instance if not already created
            if (!window.signaturePad) {
              window.signaturePad = new SignaturePad(canvas, {
                minWidth: 1,
                maxWidth: 3,
                penColor: 'black',
                backgroundColor: 'rgba(0,0,0,0)'
              });

              // Adjust canvas for high DPI screens
              function resizeCanvas() {
                const ratio = Math.max(window.devicePixelRatio || 1, 1);
                canvas.width = canvas.offsetWidth * ratio;
                canvas.height = canvas.offsetHeight * ratio;
                canvas.getContext("2d").scale(ratio, ratio);
                window.signaturePad.clear(); // Clear the canvas
              }

              // Initial resize
              resizeCanvas();

              // Clear canvas function
              window.clearSignatureCanvas = function() {
                window.signaturePad.clear();
              };

              // Get signature data URL
              window.getSignatureDataURL = function() {
                return window.signaturePad.isEmpty() ? null : window.signaturePad.toDataURL('image/png');
              };

              // Store canvas reference
              window.signatureCanvas = canvas;
            }
          } else {
            console.error('Canvas element not found or SignaturePad library not loaded');
          }
        }

        // Initialize text signature preview
        const textSignatureInput = document.getElementById('text-signature-input');
        const textSignaturePreview = document.getElementById('text-signature-preview');
        const fontSelector = document.getElementById('font-selector');

        if (textSignatureInput && textSignaturePreview && fontSelector) {
          // Update preview when input changes
          textSignatureInput.addEventListener('input', function() {
            const inputValue = this.value.trim();
            textSignaturePreview.textContent = inputValue || 'اكتب توقيعك';

            // Apply the selected font to the preview
            textSignaturePreview.style.fontFamily = fontSelector.value;
          });

          // Update font when selector changes
          fontSelector.addEventListener('change', function() {
            // Apply the selected font to the preview
            textSignaturePreview.style.fontFamily = this.value;

            // Update the preview text
            const inputValue = textSignatureInput.value.trim();
            textSignaturePreview.textContent = inputValue || 'اكتب توقيعك';

            // If there's a fromInput with a text signature, update its font
            const fromInput = document.getElementById('giftcard_fromInput');
            if (fromInput && fromInput.getAttribute('data-signature-type') === 'text') {
              fromInput.style.fontFamily = this.value;
              fromInput.setAttribute('data-signature-font', this.value);

              // Update the text signature display in the main form
              const textSignatureValue = document.getElementById('text-signature-value');
              if (textSignatureValue) {
                textSignatureValue.style.fontFamily = this.value;
              }

              // Update preview content
              updatePreviewContent();
            }
          });

          // Set initial font
          textSignaturePreview.style.fontFamily = fontSelector.value;

          // Set initial preview text
          const initialValue = textSignatureInput.value.trim();
          if (initialValue) {
            textSignaturePreview.textContent = initialValue;
          }
        }

        // Set up tab switching
        const textTabBtn = document.getElementById('text_signature_tab_btn');
        const drawTabBtn = document.getElementById('draw_signature_tab_btn');

        if (textTabBtn && drawTabBtn) {
          // Text tab button
          textTabBtn.addEventListener('click', function() {
            // Get tab contents
            const textTab = document.getElementById('text_signature_tab');
            const drawTab = document.getElementById('draw_signature_tab');

            // Update active states
            textTabBtn.classList.add('active');
            drawTabBtn.classList.remove('active');

            if (textTab && drawTab) {
              textTab.classList.add('active');
              drawTab.classList.remove('active');
            }
          });

          // Draw tab button
          drawTabBtn.addEventListener('click', function() {
            // Get tab contents
            const textTab = document.getElementById('text_signature_tab');
            const drawTab = document.getElementById('draw_signature_tab');

            // Update active states
            textTabBtn.classList.remove('active');
            drawTabBtn.classList.add('active');

            if (textTab && drawTab) {
              textTab.classList.remove('active');
              drawTab.classList.add('active');

              // Make sure Signature Pad is initialized when switching to draw tab
              if (typeof SignaturePad !== 'undefined' && window.signatureCanvas) {
                // If SignaturePad instance doesn't exist yet, create it
                if (!window.signaturePad) {
                  initializeSignaturePad();
                } else {
                  // If it exists, resize it to fit the canvas properly
                  const ratio = Math.max(window.devicePixelRatio || 1, 1);
                  const canvas = window.signatureCanvas;
                  canvas.width = canvas.offsetWidth * ratio;
                  canvas.height = canvas.offsetHeight * ratio;
                  canvas.getContext("2d").scale(ratio, ratio);
                  window.signaturePad.clear();
                }
              }
            }
          });
        }

        // Clear button functionality
        const clearBtn = document.getElementById('clearSignatureBtn');
        if (clearBtn) {
          clearBtn.addEventListener('click', function() {
            const activeTab = document.querySelector('.signature-tab-content.active');
            if (activeTab.id === 'draw_signature_tab') {
              // Clear canvas using Signature Pad
              if (window.clearSignatureCanvas) {
                window.clearSignatureCanvas();
              } else if (window.signaturePad) {
                window.signaturePad.clear();
              }
            } else {
              // Clear text input
              const textInput = document.getElementById('text-signature-input');
              if (textInput) {
                textInput.value = '';
                const textPreview = document.getElementById('text-signature-preview');
                if (textPreview) {
                  textPreview.textContent = 'اكتب توقيعك';
                }
              }
            }
          });
        }

        // Confirm button functionality
        const confirmBtn = document.getElementById('confirmSignatureBtn');
        if (confirmBtn) {
          confirmBtn.addEventListener('click', function() {
            const activeTab = document.querySelector('.signature-tab-content.active');
            const fromInput = document.getElementById('giftcard_fromInput');

            if (activeTab.id === 'draw_signature_tab') {
              // Get signature from canvas using Signature Pad
              if (window.getSignatureDataURL) {
                const signatureDataURL = window.getSignatureDataURL();

                // Check if canvas is empty using Signature Pad's isEmpty method
                if (!signatureDataURL || (window.signaturePad && window.signaturePad.isEmpty())) {
                  alert('الرجاء رسم توقيعك أولاً');
                  return;
                }

                // Show the drawn signature in the main form
                const drawSignatureDisplay = document.getElementById('draw-signature-display');
                const drawSignatureImage = document.getElementById('draw-signature-image');

                if (drawSignatureDisplay && drawSignatureImage) {
                  // Set the image source
                  drawSignatureImage.src = signatureDataURL;

                  // Show the drawn signature display
                  drawSignatureDisplay.style.display = 'flex';

                  // Hide the signature button
                  signatureBtn.style.display = 'none';

                  // Hide text signature display if visible
                  const textSignatureDisplay = document.getElementById('text-signature-display');
                  if (textSignatureDisplay) {
                    textSignatureDisplay.style.display = 'none';
                  }
                }

                // Clear the text input but keep the signature data
                if (fromInput) {
                  fromInput.value = '';
                  fromInput.setAttribute('data-signature-type', 'drawn');
                  fromInput.setAttribute('data-signature-data', signatureDataURL);

                  // Trigger update events
                  const events = ['input', 'change'];
                  events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    fromInput.dispatchEvent(event);
                  });
                }

                // Update preview content
                updatePreviewContent();

                // Close modal
                signatureModal.close();
              }
            } else {
              // Get signature from text input
              const textInput = document.getElementById('text-signature-input');
              const fontSelector = document.getElementById('font-selector');

              if (textInput && fontSelector) {
                const signatureText = textInput.value.trim();

                if (!signatureText) {
                  alert('الرجاء كتابة توقيعك أولاً');
                  return;
                }

                // Set the signature in the from input
                if (fromInput) {
                  fromInput.value = signatureText;
                  fromInput.setAttribute('data-signature-type', 'text');
                  fromInput.setAttribute('data-signature-font', fontSelector.value);

                  // Apply the selected font to the input field directly
                  fromInput.style.fontFamily = fontSelector.value;

                  // Show the signature text in the main form
                  const textSignatureDisplay = document.getElementById('text-signature-display');
                  const textSignatureValue = document.getElementById('text-signature-value');

                  if (textSignatureDisplay && textSignatureValue) {
                    // Set the text and font
                    textSignatureValue.textContent = signatureText;
                    textSignatureValue.style.fontFamily = fontSelector.value;

                    // Show the text signature display
                    textSignatureDisplay.style.display = 'flex';

                    // Hide the signature button
                    signatureBtn.style.display = 'none';

                    // Hide draw signature display if visible
                    const drawSignatureDisplay = document.getElementById('draw-signature-display');
                    if (drawSignatureDisplay) {
                      drawSignatureDisplay.style.display = 'none';
                    }
                  }

                  // Trigger update events
                  const events = ['input', 'change'];
                  events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    fromInput.dispatchEvent(event);
                  });
                }

                // Update preview content
                updatePreviewContent();

                // Close modal
                signatureModal.close();
              }
            }
          });
        }

        // Open the modal
        signatureModal.open();
      }
    });
  }

  // Set up text and draw signature delete buttons
  const textSignatureDeleteBtn = document.getElementById('text-signature-delete-btn');
  const drawSignatureDeleteBtn = document.getElementById('draw-signature-delete-btn');

  if (textSignatureDeleteBtn) {
    textSignatureDeleteBtn.addEventListener('click', function() {
      const textSignatureDisplay = document.getElementById('text-signature-display');
      const signatureBtn = document.getElementById('giftcard_signatureBtn');
      const fromInput = document.getElementById('giftcard_fromInput');

      // Hide text signature display
      textSignatureDisplay.style.display = 'none';

      // Show signature button
      signatureBtn.style.display = 'flex';

      // Clear signature data
      if (fromInput) {
        fromInput.value = '';
        fromInput.style.fontFamily = '';
        fromInput.removeAttribute('data-signature-type');
        fromInput.removeAttribute('data-signature-font');

        // Trigger update events
        const events = ['input', 'change'];
        events.forEach(eventType => {
          const event = new Event(eventType, { bubbles: true });
          fromInput.dispatchEvent(event);
        });

        // Update preview content
        updatePreviewContent();
      }
    });
  }

  if (drawSignatureDeleteBtn) {
    drawSignatureDeleteBtn.addEventListener('click', function() {
      const drawSignatureDisplay = document.getElementById('draw-signature-display');
      const signatureBtn = document.getElementById('giftcard_signatureBtn');
      const fromInput = document.getElementById('giftcard_fromInput');

      // Hide draw signature display
      drawSignatureDisplay.style.display = 'none';

      // Show signature button
      signatureBtn.style.display = 'flex';

      // Clear signature data
      if (fromInput) {
        fromInput.value = '';
        fromInput.removeAttribute('data-signature-type');
        fromInput.removeAttribute('data-signature-data');

        // Trigger update events
        const events = ['input', 'change'];
        events.forEach(eventType => {
          const event = new Event(eventType, { bubbles: true });
          fromInput.dispatchEvent(event);
        });

        // Update preview content
        updatePreviewContent();
      }
    });
  }

  // Signature change button
  const signatureChangeBtn = document.getElementById('signature-change-btn');
  if (signatureChangeBtn) {
    signatureChangeBtn.addEventListener('click', function() {
      // Simply open the signature modal
      const signatureBtn = document.getElementById('giftcard_signatureBtn');
      if (signatureBtn) {
        signatureBtn.click();
      }
    });
  }

  // We now have separate delete buttons for each signature type

  // Add input event listeners to recipient and signature fields with enhanced handling
  const toInput = document.getElementById('giftcard_toInput');
  if (toInput) {
    // Add multiple event listeners to ensure changes are captured
    ['input', 'change', 'keyup', 'focus', 'blur'].forEach(eventType => {
      toInput.addEventListener(eventType, function() {
        // Always update preview when input changes
        updatePreviewContent();
        // Double-check after delays to ensure content is updated
        setTimeout(updatePreviewContent, 50);
        setTimeout(updatePreviewContent, 200);
      });
    });

    // Force initial focus/blur to ensure proper initialization
    setTimeout(() => {
      toInput.focus();
      setTimeout(() => toInput.blur(), 50);
    }, 100);
  } else {
    console.error('To input not found');
  }

  const fromInput = document.getElementById('giftcard_fromInput');
  if (fromInput) {
    // Add multiple event listeners to ensure changes are captured
    ['input', 'change', 'keyup', 'focus', 'blur'].forEach(eventType => {
      fromInput.addEventListener(eventType, function() {
        // Always update preview when input changes
        updatePreviewContent();
        // Double-check after delays to ensure content is updated
        setTimeout(updatePreviewContent, 50);
        setTimeout(updatePreviewContent, 200);
      });
    });

    // Force initial focus/blur to ensure proper initialization
    setTimeout(() => {
      fromInput.focus();
      setTimeout(() => fromInput.blur(), 50);
    }, 150);
  } else {
    console.error('From input not found');
  }

  // Add event listeners to card radio buttons
  const cardRadios = document.querySelectorAll('input[name="card"]');
  if (cardRadios.length > 0) {
    cardRadios.forEach(radio => {
      radio.addEventListener('change', function() {
        // Update main card display
        updateMainCardDisplay();

        // Always update preview when card selection changes
        updatePreviewContent();

        // Update preview if preview modal is open
        if (document.querySelector('#previewModal').open) {
          console.log('Preview modal is open, updating preview content again');
          setTimeout(updatePreviewContent, 100); // Extra update after a short delay
        }

        // Update selection indicators
        document.querySelectorAll('.gift-card-selection-indicator, .gift-card-checkbox').forEach(indicator => {
          indicator.style.opacity = '0';
        });

        // Show the indicators for the selected card
        const selectedCard = radio.closest('.gift-card-option');
        if (selectedCard) {
          const indicator = selectedCard.querySelector('.gift-card-selection-indicator');
          const checkbox = selectedCard.querySelector('.gift-card-checkbox');
          if (indicator) {
            indicator.style.opacity = '1';
          }
          if (checkbox) {
            checkbox.style.opacity = '1';
          }
        }
      });
    });
  } else {
    console.error('Card radio buttons not found');
  }
}

// Add everything to the document
document.head.insertAdjacentHTML('beforeend', styles);
document.body.insertAdjacentHTML('beforeend', giftModalHtml);
document.body.insertAdjacentHTML('beforeend', giftCardHtml);

// Set up event listeners and initialize selection indicators after a short delay to ensure DOM is ready
setTimeout(() => {
  setupEventListeners();
  initializeSelectionIndicators();
}, 100);

// Also set up on DOMContentLoaded as a fallback
document.addEventListener('DOMContentLoaded', () => {
  setupEventListeners();
  initializeSelectionIndicators();
});